package com.cap10mycap10.worklinkservice.controller;

import com.cap10mycap10.worklinkservice.dto.tax.TaxRateDto;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.model.TaxRate;
import com.cap10mycap10.worklinkservice.service.TaxRateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/tax-rates")
@RequiredArgsConstructor
@Slf4j
public class TaxRateController {

    private final TaxRateService taxRateService;

    @GetMapping("/agency/{agencyId}")
    public ResponseEntity<List<TaxRateDto>> getTaxRatesByAgency(@PathVariable Long agencyId) {
        List<TaxRate> taxRates = taxRateService.findByAgencyId(agencyId);
        List<TaxRateDto> taxRateDtos = taxRates.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(taxRateDtos);
    }

    @GetMapping("/agency/{agencyId}/active")
    public ResponseEntity<List<TaxRateDto>> getActiveTaxRatesByAgency(@PathVariable Long agencyId) {
        List<TaxRate> taxRates = taxRateService.findActiveByAgencyId(agencyId);
        List<TaxRateDto> taxRateDtos = taxRates.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(taxRateDtos);
    }

    @GetMapping("/{id}")
    public ResponseEntity<TaxRateDto> getTaxRate(@PathVariable Long id) {
        return taxRateService.findById(id)
                .map(taxRate -> ResponseEntity.ok(convertToDto(taxRate)))
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping
    public ResponseEntity<?> createTaxRate(@Valid @RequestBody TaxRateDto taxRateDto) {
        try {
            TaxRate taxRate = convertToEntity(taxRateDto);
            TaxRate savedTaxRate = taxRateService.create(taxRate);
            return ResponseEntity.status(HttpStatus.CREATED).body(convertToDto(savedTaxRate));
        } catch (BusinessValidationException e) {
            log.warn("Failed to create tax rate: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new ErrorResponse(e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<?> updateTaxRate(@PathVariable Long id, @Valid @RequestBody TaxRateDto taxRateDto) {
        try {
            return taxRateService.findById(id)
                    .map(existingTaxRate -> {
                        existingTaxRate.setName(taxRateDto.getName());
                        existingTaxRate.setPercentage(taxRateDto.getPercentage());
                        existingTaxRate.setDescription(taxRateDto.getDescription());
                        existingTaxRate.setActive(taxRateDto.isActive());
                        TaxRate updatedTaxRate = taxRateService.update(existingTaxRate);
                        return ResponseEntity.ok(convertToDto(updatedTaxRate));
                    })
                    .orElse(ResponseEntity.notFound().build());
        } catch (BusinessValidationException e) {
            log.warn("Failed to update tax rate {}: {}", id, e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new ErrorResponse(e.getMessage()));
        }
    }

    @PatchMapping("/{id}/activate")
    public ResponseEntity<Void> activateTaxRate(@PathVariable Long id) {
        taxRateService.activate(id);
        return ResponseEntity.ok().build();
    }

    @PatchMapping("/{id}/deactivate")
    public ResponseEntity<Void> deactivateTaxRate(@PathVariable Long id) {
        taxRateService.deactivate(id);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteTaxRate(@PathVariable Long id) {
        try {
            taxRateService.delete(id);
            return ResponseEntity.noContent().build();
        } catch (BusinessValidationException e) {
            log.warn("Failed to delete tax rate {}: {}", id, e.getMessage());
            return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(new ErrorResponse(e.getMessage()));
        }
    }

    // Error response class for better error handling
    private static class ErrorResponse {
        private final String message;

        public ErrorResponse(String message) {
            this.message = message;
        }

        public String getMessage() {
            return message;
        }
    }

    private TaxRateDto convertToDto(TaxRate taxRate) {
        TaxRateDto dto = new TaxRateDto();
        dto.setId(taxRate.getId());
        dto.setName(taxRate.getName());
        dto.setPercentage(taxRate.getPercentage());
        dto.setDescription(taxRate.getDescription());
        dto.setActive(taxRate.isActive());
        dto.setAgencyId(taxRate.getAgencyId());
        return dto;
    }

    private TaxRate convertToEntity(TaxRateDto dto) {
        TaxRate taxRate = new TaxRate();
        taxRate.setId(dto.getId());
        taxRate.setName(dto.getName());
        taxRate.setPercentage(dto.getPercentage());
        taxRate.setDescription(dto.getDescription());
        taxRate.setActive(dto.isActive());
        taxRate.setAgencyId(dto.getAgencyId());
        return taxRate;
    }
}
