package com.cap10mycap10.worklinkservice.model;

import com.cap10mycap10.worklinkservice.enums.ClientDocType;
import com.cap10mycap10.worklinkservice.enums.Status;
import com.cap10mycap10.worklinkservice.enums.VehicleBookingStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import org.hibernate.search.annotations.Field;
import org.hibernate.search.annotations.Indexed;

import javax.persistence.*;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


@Entity
@AllArgsConstructor
@NoArgsConstructor
@Data
@NamedEntityGraph(
        name = "Client.service",
        attributeNodes = @NamedAttributeNode("service")
)
@Indexed
public class Client  extends AbstractAuditingEntity{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Field
    private String name;

    @Field
    private String telephone;

    private Address address;

    @Field
    @Column(unique = true, nullable = false)
    private String email;

    @Field
    private String billingEmail;

    private String logo;
    private String sbsCode;
    private String purchaseOrder;


    @OneToMany(cascade = CascadeType.ALL)
    private Set<Invoice> invoices = new HashSet<>();

    @Enumerated(EnumType.STRING)
    private Status status;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Services service;

    @OneToMany(mappedBy = "client")
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    private Set<Transport> transports = new HashSet<>();;

    @OneToMany(mappedBy = "client", fetch = FetchType.EAGER)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    private Set<VehicleBooking> vehicleBookings = new HashSet<>();;


    @JsonIgnore
    @ManyToMany( mappedBy = "clients")
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Set<Agency> agencies = new HashSet<>();

    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "client", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    Set<ClientDocs> clientDocs = new HashSet<>();

    public Client(Transport transport) {
        name = transport.getBname();
        billingEmail = transport.getBemail();
        email = transport.getBemail();
        address = new Address();
        address.setFirstLine(transport.getBaddress());
        address.setPostcode(transport.getBpostCode());
        agencies.add(transport.getAgency());
        transports.add(transport);
        status = Status.ACTIVE;
        transport.setClient(this);
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Client tag = (Client) o;
        return Objects.equals(name, tag.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name);
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("Client{");
        sb.append("id=").append(id);
        sb.append(", city='").append(name).append('\'');
        sb.append(", telephone='").append(telephone).append('\'');
        sb.append(", address=").append(address);
        sb.append(", email='").append(email).append('\'');
        sb.append(", billingEmail='").append(billingEmail).append('\'');
        sb.append(", logo='").append(logo).append('\'');
        sb.append(", status=").append(status);
        sb.append(", service=").append(service);
        sb.append('}');
        return sb.toString();
    }

//    public ClientDto toClientDto() {
//        ClientDto clientDto = new ClientDto();
//        clientDto.setName(this.name);
//        clientDto.setId(this.id);
//        clientDto.setSbsCode(this.sbsCode);
//        clientDto.setPurchaseOrder(this.purchaseOrder);
//        clientDto.setTelephone(this.telephone);
//        clientDto.setAddress(this.address);
//        clientDto.setEmail(this.email);
//        clientDto.setBillingEmail(this.billingEmail);
//        clientDto.setLogo(this.logo);
//        clientDto.setRating(getRating());
//        clientDto.setTotalBooking(this.vehicleBookings.size());
//        clientDto.setVerified(isVerified());
//        clientDto.setClientDocs(this.clientDocs);
//        if(nonNull(service))clientDto.setServiceId(this.service.getId());
//        return clientDto;
//    }


    public Boolean isVerified() {
        return this.vehicleBookings.stream()
                .anyMatch(booking -> booking.getStatus() == VehicleBookingStatus.COMPLETE)
                && hasAllDocs();
    }

    private Boolean hasAllDocs() {
//        Set<ClientDoc> requiredDocs = EnumSet.allOf(ClientDoc.class);
        Set<ClientDocType> requiredDocs = Set.of(ClientDocType.DRIVER);
        Set<ClientDocType> availableDocs = this.clientDocs.stream()
                .map(ClientDocs::getName)
                .collect(Collectors.toSet());

        return this.vehicleBookings.stream()
                .anyMatch(booking -> booking.getStatus() == VehicleBookingStatus.COMPLETE)
                && availableDocs.containsAll(requiredDocs);
    }

    public Set<VehicleBooking> getValidVehicleBookings() {
        return vehicleBookings.stream().filter(booking -> booking.getStatus() != VehicleBookingStatus.CANCELLED).collect(Collectors.toSet());
    }

    public void addDocuments(Set<ClientDocs> docs) {

        for(ClientDocs newDoc : docs) {
            newDoc.setClient(this);

            clientDocs.stream().filter(d->d.getName()==newDoc.getName()).findFirst()
                    .ifPresentOrElse(doc -> {
                        doc.setUrl(newDoc.getUrl());
                        doc.setExpiryDate(newDoc.getExpiryDate());
                        doc.setClient(this);
                    }, () -> {
                        newDoc.setClient(this);
                        clientDocs.add(newDoc);
                    });


        };
    }
}
