package com.cap10mycap10.userservice.service;

import com.cap10mycap10.userservice.dto.*;
import com.cap10mycap10.userservice.enums.UserType;
import com.cap10mycap10.userservice.exception.BusinessValidationException;
import com.cap10mycap10.userservice.exception.InvalidRequestException;
import com.cap10mycap10.userservice.exception.RecordNotFoundException;
import com.cap10mycap10.userservice.jpa.CustomSpecificationTemplateImplBuilder;
import com.cap10mycap10.userservice.mapper.UserToUserResponse;
import com.cap10mycap10.userservice.model.PasswordResetToken;
import com.cap10mycap10.userservice.model.Role;
import com.cap10mycap10.userservice.model.User;
import com.cap10mycap10.userservice.model.VerificationToken;
import com.cap10mycap10.userservice.repository.PasswordResetTokenRepository;
import com.cap10mycap10.userservice.repository.RoleRepository;
import com.cap10mycap10.userservice.repository.UserRepository;
import com.cap10mycap10.userservice.repository.VerificationTokenRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import javax.persistence.EntityNotFoundException;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Slf4j
@Service
public class UserServiceImpl implements UserService {
    @Value("${env.companyName}")
    private String companyName;
    private final UserToUserResponse toUserResponse;

    private final UserRepository userRepository;

    private final VerificationTokenRepository tokenRepository;

    private final PasswordResetTokenRepository passwordTokenRepository;

    private final EmailService emailService;

    private final PasswordEncoder passwordEncoder;

    private final RoleRepository roleRepository;

    private final ISecurityUserService securityUserService;



    @Value("${env.supportEmail}")
    private String supportEmail;




    @Value("${worker-portal.url}")
    private String workerUrl;

    @Value("${api.url}")
    private String apiUrl;

    @Value("${iosApp.url}")
    private String iosAppUrl;

    @Value("${androidApp.url}")
    private String androidAppUrl;

    public UserServiceImpl(final UserToUserResponse toUserResponse,
                           final UserRepository userRepository,
                           final VerificationTokenRepository tokenRepository,
                           final PasswordResetTokenRepository passwordTokenRepository,
                           EmailService emailService, final PasswordEncoder passwordEncoder,
                           final RoleRepository roleRepository,
                           ISecurityUserService securityUserService) {
        this.toUserResponse = toUserResponse;
        this.userRepository = userRepository;
        this.tokenRepository = tokenRepository;
        this.passwordTokenRepository = passwordTokenRepository;
        this.emailService = emailService;
        this.passwordEncoder = passwordEncoder;
        this.roleRepository = roleRepository;
        this.securityUserService = securityUserService;
    }

    @Override
    @Transactional
    public UserResponse registerNewUserAccount(UserDto accountDto, HttpServletRequest request, Long agencyId) {
        if (emailExists(accountDto.getEmail())) {
            throw new InvalidRequestException("There is an account with that email address: " + accountDto.getEmail());
        }
        if (usernameExists(accountDto.getUsername())) {
            throw new InvalidRequestException("There is an account with that email address: " + accountDto.getUsername());
        }
        final User user = new User();

        String password = PasswordGenerator.generatePassword(8);

        user.setFirstName(accountDto.getFirstName());
        user.setLastName(accountDto.getLastName());
        user.setUsername(accountDto.getUsername());
        if ((accountDto.getAgentId() != null) && (accountDto.getAgentId() > 0)) {
            user.setAgentId(accountDto.getAgentId());
//            accountDto.getRoleIds().add(3L);
        }
        if ((accountDto.getClientId() != null) && (accountDto.getClientId() > 0)) {
            user.setClientId(accountDto.getClientId());
//            accountDto.getRoleIds().add(2L);
        }
        if ((accountDto.getWorkerId() != null) && (accountDto.getWorkerId() > 0)) {
            user.setWorkerId(accountDto.getWorkerId());
//            accountDto.getRoleIds().add(4L);
        }
        user.setPassword(passwordEncoder.encode(password));
        user.setEmail(accountDto.getEmail());


        List<Role> role = roleRepository.findAllById(accountDto.getRoleIds());
        List<Role> roles = new ArrayList<>(role);
        user.setRoles(roles);
        user.setEnabled(true);
        user.setUserType(accountDto.getUserType());
        User persistedUser = userRepository.save(user);

        String url = workerUrl;

        final String token = UUID.randomUUID().toString();
        checkCurrentPassword(persistedUser);
        createPasswordResetTokenForUser(persistedUser, token);


        sendCreateAccountEmail(url, token, persistedUser, agencyId);

        log.info("New user registered: {}", user.getEmail());

        return toUserResponse.convert(persistedUser);
    }

    @Override
    @Transactional
    public UserResponse addRoleToUser(AddRoleToUserDto accountDto, HttpServletRequest request) {



        User user = userRepository.findById(accountDto.getUserId()).orElseThrow(EntityNotFoundException::new);
        Role role = roleRepository.findById(accountDto.getRoleId()).orElseThrow(EntityNotFoundException::new);
        List<Role> rolesArr = new ArrayList<>();
        List<Role> roles = user.getRoles();
        roles.forEach(e->{
            if(e.getName().equalsIgnoreCase("ROLE_ADMIN_ADMIN")
                    || e.getName().equalsIgnoreCase("ROLE_AGENCY_ADMIN")
                    || e.getName().equalsIgnoreCase("ROLE_CLIENT_ADMIN")){
                rolesArr.add(role);
            }
        });
        rolesArr.add(role);
        user.setRoles(roles);
        User persistedUser = userRepository.save(user);

        return toUserResponse.convert(persistedUser);
    }

    private void sendCreateAccountEmail(final String contextPath, final String token, final User user, Long agencyId) {
        log.info("Sending email to none null AgentId {}, ClientId {}, WorkerId {}", user.getAgentId(), user.getClientId(), user.getWorkerId());
        String message = "";
        String resetPasswordUrl = contextPath + "/resetPassword/changePassword?token=" + token;

        if (user.getAgentId() != null && user.getUserType() == UserType.AGENCY ){
            log.info("Inside Agent send email");
            message = "<h1>Welcome to MyKarlink!</h1>" +
                    " <div class='content'> " +
                    " <p>Hi "+  user.getFirstName()+"  ,</p> " +
                    " <p>We're excited to have you on board! A new account has been created for you at <a href=' "+ workerUrl+"  '>  "+workerUrl+"   </a>.</p> " +
                    " <p>To get started, please set your password using the link below:</p> " +
                    " <div style='text-align: center;'> " +
                    " <a href='"+  resetPasswordUrl+"  ' class='button'>Set Your Password</a> " +
                    "</div> " +
                    " <div class='video-container'> " +
                    " <p><strong>Watch our quick start guide:</strong></p> " +
                    " <a href='https://youtu.be/5xRJaj2DEZQ?si=dGfHl9ofqFE0DPIr' target='_blank'> " +
                    " <img src='https://img.youtube.com/vi/5xRJaj2DEZQ/maxresdefault.jpg' alt='Get Started Video' style='max-width: 100%%; height: auto; border: 1px solid #ddd; border-radius: 4px;'> " +
                    "</a> " +
                    " <p>Click the image above to watch our get started video</p> " +
                    "</div> " +
                    " <div class='footer'> " +
                    " <p>If this account was created in error or you have any issues with your account, please contact <a href='mailto:  "+supportEmail+"  '> "+ supportEmail+"  </a>.</p> ";


        } else if (user.getClientId() != null) {
            log.info("Inside client send email");
            message = "<p>Hi " + user.getFirstName() + ",</p>" +
                    "<p>Welcome to our platform! A new account has been created for you at <a href='" + workerUrl + "'>" + workerUrl + "</a>.</p>" +
                    "<p>To set your password, please use the following link:</p>" +
                    "<p><a href='" + resetPasswordUrl + "'>Reset Password</a></p>" +
                    "<p>If this account was created in error or you have any issues with your account, please contact <a href='mailto:" + supportEmail + "'>" + supportEmail + "</a>.</p>" +
                    "<p>Best regards,<br>Support Team</p>";
        } else if (user.getWorkerId() != null) {
            log.info("Inside worker send email");
            message = "<p>Hi " + user.getFirstName() + ",</p>" +
                    "<p>A new account was created for " + user.getEmail() + " at MyWorklink.</p>" +
                    "<p>For Android users, please download the mobile app via the link below:</p>" +
                    "<p><a href='" + androidAppUrl + "'>Download Android App</a></p>" +
                    "<p>Or open the web application via this link:</p>" +
                    "<p><a href='" + iosAppUrl + "'>Open Web Application</a></p>" +
                    "<p>To set your password, please use the following link:</p>" +
                    "<p><a href='" + resetPasswordUrl + "'>Reset Password</a></p>" +
                    "<p>If this account was created in error or you have any issues with your account, please contact " + supportEmail + ".</p>";
        } else {
            message = "Hi " + user.getFirstName() + "<br>" +
                    "You have been invited by Admin at https://online.myworklink.uk as a system user.<br>" +
                    "Please click the above link to set your password. <br> " +
                    "Use the following link to reset your password: <a href='" + resetPasswordUrl + "'>Reset Password</a><br>" +
                    "If this was an error or you have any issues, please contact: " + supportEmail + " .";
        }

        // Use enhanced email service for agency-specific branding
        CompletableFuture.runAsync(() -> {
            emailService.sendUserRegistrationEmail(user, resetPasswordUrl, workerUrl, androidAppUrl, iosAppUrl, agencyId);
        });
    }



    private void constructResetTokenEmail(final String contextPath, final Locale locale, final String token, final User user, Long agencyId) {
        final String resetPasswordUrl = contextPath + "/resetPassword/changePassword?token=" + token;

        // Use enhanced email service for agency-specific branding
        emailService.sendPasswordResetEmail(user, resetPasswordUrl, agencyId);
    }


    private boolean emailExists(String email) {
        return userRepository.findByEmail(email) != null;
    }

    private boolean usernameExists(String username) {
        return userRepository.findByUsername(username).isPresent();
    }

    @Override
    public User getUser(final String verificationToken) {
        final VerificationToken token = tokenRepository.findByToken(verificationToken);
        if (token != null) {
            return token.getUser();
        }
        return null;
    }

    @Override
    public void createVerificationTokenForUser(final User user, final String token) {
        final VerificationToken myToken = new VerificationToken(token, user);
        tokenRepository.save(myToken);
    }

    @Override
    public VerificationToken generateNewVerificationToken(final String existingVerificationToken) {
        VerificationToken vToken = tokenRepository.findByToken(existingVerificationToken);
        vToken.updateToken(UUID.randomUUID()
                .toString());
        vToken = tokenRepository.save(vToken);
        return vToken;
    }

    @Override
    public void createPasswordResetTokenForUser(final User user, final String token) {
        final PasswordResetToken myToken = new PasswordResetToken(token, user);
        Date dt = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(dt);
        c.add(Calendar.DATE, 1);
        dt = c.getTime();
        myToken.setExpiryDate(dt);
        passwordTokenRepository.save(myToken);
    }

    public void checkCurrentPassword(final User user) {
        if(!(user.getLastPasswordChange()!=null &&  LocalDate.now().isBefore(user.getLastPasswordChange().plusDays(120))       ) ) {
            user.setLastPassword(user.getPassword());
            user.setLastPasswordChange(LocalDate.now());
            userRepository.save(user);
        }
    }

    @Override
    public User findUserByEmail(final String email) {
        return userRepository.findByEmail(email);
    }

    @Override
    public Optional<User> getUserByPasswordResetToken(final String token) {
        return Optional.ofNullable(passwordTokenRepository.findByToken(token).getUser());
    }

    @Override
    public boolean checkIfValidOldPassword(final User user, final String oldPassword) {
        return passwordEncoder.matches(oldPassword.trim(), user.getPassword());
    }

    @Override
    public Page<UserResponse> findAll(Pageable pageable, Long agentId) {
        return userRepository.findAllByAgentId(agentId, pageable)
                .map(toUserResponse::convert);
    }

    @Override
    public Page<UserResponse> findAll(Pageable pageable) {
        return userRepository.findAll(pageable)
                .map(toUserResponse::convert);
    }

    @Override
    public List<UserResponse> findByRole(String role) {
        List<UserResponse> userResponseList = new ArrayList<>();
        List<User> userList = userRepository.findAll();

        for (User user : userList) {
            for (Role userRole : user.getRoles()
            ) {
                if (role.equalsIgnoreCase(userRole.getName())) {
                    userResponseList.add(toUserResponse.convert(user));
                }
            }
        }
        return userResponseList;
    }

    @Override
    public List<UserResponse> findAll() {
        return userRepository.findAll()
                .stream()
                .map(toUserResponse::convert)
                .collect(Collectors.toList());
    }

    @Override
    public UserResponse activate(Long id) {
        User user = userRepository.findById(id).orElseThrow(EntityNotFoundException::new);
        user.setEnabled(true);
       /* user.setAccountNonLocked(true);
        user.setCredentialsNonExpired(true);
        user.setAccountNonExpired(true);*/
        user = userRepository.save(user);
        return toUserResponse.convert(user);
    }

    @Override
    public UserResponse deactivate(Long id) {
        User user = userRepository.findById(id).orElseThrow(EntityNotFoundException::new);
        user.setEnabled(false);
      /*  user.setAccountNonLocked(false);
        user.setCredentialsNonExpired(false);
        user.setAccountNonExpired(false);*/
        user = userRepository.save(user);
        return toUserResponse.convert(user);
    }

    @Override
    public UserResponse enableUser(EnableAdminUserRequest request) {
        Role role = roleRepository.findByName(request.getRoleName())
                .orElseThrow(EntityNotFoundException::new);
        User user = userRepository.findById(request.getUserId())
                .orElseThrow(EntityNotFoundException::new);
        user.setEnabled(request.isEnabled());
        user.getRoles().clear();
        user.addRole(role);
        user = userRepository.save(user);
        return toUserResponse.convert(user);
    }

    @Override
    public List<Role> findAllRoles() {
        return roleRepository.findAll();
    }

    @Override
    public void resendRegistrationToken(HttpServletRequest request, String existingToken, Long agencyId) {
        final VerificationToken newToken = generateNewVerificationToken(existingToken);
        final User user = getUser(newToken.getToken());
        try {
            constructResendVerificationTokenEmail(workerUrl, request.getLocale(), newToken, user, agencyId);
        } catch (MessagingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public GenericResponse resetPassword(HttpServletRequest request, String userEmail, Long agencyId) {
        log.info("Inside RESET SERVice");
        final User user = findUserByEmail(userEmail);
        if (user != null) {
            final String token = UUID.randomUUID().toString();
            createPasswordResetTokenForUser(user, token);
            checkCurrentPassword(user);
            constructResetTokenEmail(workerUrl, request.getLocale(), token, user, agencyId);
            return new GenericResponse("You should receive a Password Reset Email shortly");
        } else {
            throw new InvalidRequestException("User with that email does not exist");
        }
    }

    @Override
    public GenericResponse savePassword(Locale locale, PasswordDto passwordDto) {
        final String result = securityUserService.validatePasswordResetToken(passwordDto.getToken());

        log.info("Result: {}", result);

        if (result != null) {
            log.info("Token not found");
            throw  new BusinessValidationException("Invalid password reset token.");
        }

        Optional<User> user = getUserByPasswordResetToken(passwordDto.getToken());
        if (user.isPresent()) {

            String user1 = user.get().getPassword();


            if (passwordEncoder.matches( passwordDto.getNewPassword(), user1)) {
                throw new BusinessValidationException("New password should not be similar to old password");
            }

            user.get().setPassword(passwordEncoder.encode(passwordDto.getNewPassword().trim()));
            userRepository.save(user.get());
            log.info("Password reset successfully");
            passwordTokenRepository.delete(passwordTokenRepository.findByToken(passwordDto.getToken()));
            return new GenericResponse("Password reset successfully");
        } else {
            log.info("Invalid Token");
            throw new BusinessValidationException("Invalid token");
        }
    }

    @Override
    public void changeUserPassword(ChangePasswordRequest passwordDto) {

        String username = getUsername();

        final User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new RecordNotFoundException("User not found"));

        if (!checkIfValidOldPassword(user, passwordDto.getOldPassword())) {
            throw new InvalidRequestException("Invalid Old Password");
        }
        user.setPassword(passwordEncoder.encode(passwordDto.getNewPassword().trim()));
        userRepository.save(user);
    }

    private String getUsername() {

        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        String username;
        if (principal instanceof UserDetails) {
            username = ((UserDetails) principal).getUsername();
        } else {
            username = principal.toString();
        }
        return username;
    }

    @Override
    public UserResponse findById(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("User not found"));
        return toUserResponse.convert(user);
    }

    @Override
    public UserResponse findByEmail(String email) {
        User user = userRepository.findByEmail(email);
        return toUserResponse.convert(user);
    }

    @Override
    public Page<UserResponse> findAllAdminUsers(Pageable pageable, String search) {
        if (isNull(search)) {
            return userRepository.findAllAdminUsers(pageable)
                    .map(toUserResponse::convert);
        } else {
            Specification<User> spec = (new CustomSpecificationTemplateImplBuilder<User>())
                    .buildSpecification(search);
            spec = spec.and(isAgentIdNull());
            return this.userRepository.findAll(spec, pageable)
                    .map(toUserResponse::convert);
        }
    }

    public static Specification<User> isAgentIdNull() {
        return (root, query, builder) -> builder.equal(root.get("payeeId"), null);
    }

    public static Specification<User> isAgentIdEqual(Long agentId) {
        return (root, query, builder) -> builder.equal(root.get("payeeId"), agentId);
    }

    public static Specification<User> isClientIdNull() {
        return (root, query, builder) -> builder.equal(root.get("payerId"), null);
    }

    public static Specification<User> isClientIdEqual(Long clientId) {
        return (root, query, builder) -> builder.equal(root.get("payerId"), clientId);
    }


    public static Specification<User> isWorkerIdNull() {
        return (root, query, builder) -> builder.equal(root.get("workerId"), null);
    }

    public static Specification<User> isWorkerIdEqual(Long workerId) {
        return (root, query, builder) -> builder.equal(root.get("workerId"), workerId);
    }


    @Override
    public UserSearchResponse findByUsername(String username) {

        Optional<User> optionalUser = userRepository.findByUsername(username);

        if (optionalUser.isPresent()) {

            UserResponse userResponse = toUserResponse.convert(optionalUser.get());

            return new UserSearchResponse(true, userResponse);
        }

        return new UserSearchResponse(false);
    }

    @Override
    public UserResponse registerClientAccount(ClientAccountRequest accountDto) {

        if (usernameExists(accountDto.getUsername())) {
            throw new InvalidRequestException("There is an account with that email address: " + accountDto.getUsername());
        }

        if (emailExists(accountDto.getEmail())) {
            throw new InvalidRequestException("There is an account with that email address: " + accountDto.getEmail());
        }

        final User user = new User();

        user.setFirstName(accountDto.getFirstName());
        user.setLastName(accountDto.getLastName());
        user.setUsername(accountDto.getUsername());
        user.setClientId(accountDto.getClientId());

        user.setEnabled(true);
        user.setPassword(passwordEncoder.encode(accountDto.getPassword()));
        user.setEmail(accountDto.getEmail());

        Role role = roleRepository.findById(5L)
                .orElseThrow(() -> new RecordNotFoundException("Role not found"));
        List<Role> roles = new ArrayList<>();
        roles.add(role);
        user.setRoles(roles);

        User persistedUser = userRepository.save(user);

        return toUserResponse.convert(persistedUser);
    }

    @Override
    public List<UserResponse> findAllAgentUsers(PageRequest pageRequest, Long agentId) {


        return userRepository.findAllByAgentIdAndUserType(agentId, UserType.AGENCY,  pageRequest)
                .map(toUserResponse::convert).getContent();


    }

    @Override
    public void activateAgentUsers(Long agentId, boolean active) {
        userRepository.activateAgentUsers(agentId, active);
    }

    @Override
    public UserResponse editUser(EditTellerRequest request, Long id) {
        final User user = userRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("User not found"));

        user.setEmail(request.getEmail());
        user.setFirstName(request.getFirstName());
        user.setLastName(request.getLastName());
        user.setUsername(request.getEmail());

        Role role = roleRepository.findById(request.getRoleId())
                .orElseThrow(() -> new RecordNotFoundException("Role not found"));
        List<Role> roles = new ArrayList<>();
        roles.add(role);
        user.setRoles(roles);

        User persistedUser = userRepository.save(user);

        return toUserResponse.convert(persistedUser);
    }

    @Override
    public Page<UserResponse> findAllClientUsers(PageRequest of, Long clientId) {

        return userRepository.findAllByClientIdAndUserType(clientId,UserType.CLIENT, of)
                .map(toUserResponse::convert);


    }

    @Override
    public void activateClientUsers(Long clientId, boolean active) {
        userRepository.activateClientUsers(clientId, active);
    }

    @Override
    public void activateWorkerUsers(Long workerId, boolean active) {
        userRepository.activateWorkerUsers(workerId, active);
    }

    @Override
    public Page<UserResponse> findAllWorkerUsers(PageRequest of, String search) {
        if (isNull(search)) {

            return userRepository.findAllWorkers(of)
                    .map(toUserResponse::convert);

        } else {

            Specification<User> spec = (new CustomSpecificationTemplateImplBuilder<User>())
                    .buildSpecification(search);


            return this.userRepository.findAll(spec, of)
                    .map(toUserResponse::convert);
        }
    }

    @Override
    public UserResponse registerNewUserFeignAccount(UserDto accountDto, Long agencyId) {
        User user1 = userRepository.findByEmail(accountDto.getEmail());

        if (nonNull(user1)) {
            user1.updateFields(accountDto);


            Role role = roleRepository.findById(accountDto.getRoleId()).orElseThrow(EntityNotFoundException::new);
            List<Role> roles = new ArrayList<>();
            roles.add(role);
            user1.setRoles(roles);

            return toUserResponse.convert(userRepository.save(user1));
        }
        else if( userRepository.findByUsername(accountDto.getUsername()).isPresent()){
            throw new BusinessValidationException("An error occurred while creating the user, contact upport");
        }

        final User user = new User();

        String password = PasswordGenerator.generatePassword(8);

        user.setFirstName(accountDto.getFirstName());
        user.setLastName(accountDto.getLastName());
        user.setUsername(accountDto.getUsername());
        user.setUserType(accountDto.getUserType());
        if ((accountDto.getAgentId() != null) && (accountDto.getAgentId() > 0)) {
            user.setAgentId(accountDto.getAgentId());
        }
        if ((accountDto.getClientId() != null) && (accountDto.getClientId() > 0)) {
            user.setClientId(accountDto.getClientId());

        }
        if ((accountDto.getWorkerId() != null) && (accountDto.getWorkerId() > 0)) {
            user.setWorkerId(accountDto.getWorkerId());

        }
        user.setPassword(passwordEncoder.encode(password));
        user.setEmail(accountDto.getEmail());

        user.setEnabled(true);

        Role role = roleRepository.findById(accountDto.getRoleId()).orElseThrow(EntityNotFoundException::new);
        List<Role> roles = new ArrayList<>();
        roles.add(role);
        user.setRoles(roles);

        User persistedUser = userRepository.save(user);


        try{
            String url = workerUrl;

            final String token = UUID.randomUUID().toString();
            checkCurrentPassword(persistedUser);
            createPasswordResetTokenForUser(persistedUser, token);


            sendCreateAccountEmail(url, token, persistedUser, agencyId);
            log.info("New user registered: {}", user.getEmail());
            return toUserResponse.convert(persistedUser);
        }catch (Exception e){
            log.error(e.toString());
        }


        return toUserResponse.convert(persistedUser);
    }

    @Override
    public UserResponse findByClientId(Long id) {
        User user = null;
        try {
            user = userRepository.findByClientId(id).get();
            return toUserResponse.convert(user);
        } catch (Exception exception) {
            return null;
        }

    }

    private void constructResendVerificationTokenEmail(final String contextPath, final Locale locale,
                                                                    final VerificationToken newToken, final User user, Long agencyId) throws MessagingException {
        final String confirmationUrl = contextPath + "/registrationConfirm.html?token=" + newToken.getToken();

        // Use enhanced email service for agency-specific branding
        emailService.sendRegistrationConfirmationEmail(user, confirmationUrl, agencyId);
    }


}
