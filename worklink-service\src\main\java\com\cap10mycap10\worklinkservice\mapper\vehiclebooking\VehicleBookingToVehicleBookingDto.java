package com.cap10mycap10.worklinkservice.mapper.vehiclebooking;

import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleBookingDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.vehiclelog.VehicleLogDto;
import com.cap10mycap10.worklinkservice.enums.FrontPlatform;
import com.cap10mycap10.worklinkservice.mapper.asset.admin.VehicleInventoryToVehicleInventoryDto;
import com.cap10mycap10.worklinkservice.mapper.asset.agency.VehicleToVehicleDto;
import com.cap10mycap10.worklinkservice.mapper.client.ClientToClientDto;
import com.cap10mycap10.worklinkservice.mapper.invoice.InvoiceToInvoiceResult;
import com.cap10mycap10.worklinkservice.mapper.promocode.PromotionToPromotionDto;
import com.cap10mycap10.worklinkservice.mapper.transport.TransportToTransportDto;
import com.cap10mycap10.worklinkservice.model.VehicleBooking;
import com.cap10mycap10.worklinkservice.model.VehicleLog;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.cap10mycap10.worklinkservice.config.AppConfiguration.dateTimeFormat;
import static com.cap10mycap10.worklinkservice.config.AppConfiguration.isoDateTimeFormat;
import static java.util.Objects.nonNull;

@Slf4j
@Service
@EqualsAndHashCode
public class VehicleBookingToVehicleBookingDto implements Function<VehicleBooking, VehicleBookingDto> {

    @Autowired
    private InvoiceToInvoiceResult toInvoiceResult;
    @Autowired
    private VehicleToVehicleDto toVehicleDto;
    @Autowired
    private ClientToClientDto toClientDto;
    @Autowired
    private PromotionToPromotionDto toPromotionDto;
    @Autowired
    private VehicleInventoryToVehicleInventoryDto vehicleInventoryMapper;


    @Override
    @Transactional
    public VehicleBookingDto apply(VehicleBooking booking) {

        VehicleBookingDto vehicleBookingDto = new VehicleBookingDto();

        vehicleBookingDto.setCheckoutItems(booking.getCheckoutItems());
        vehicleBookingDto.setFuelOut(booking.getFuelOut());
        vehicleBookingDto.setSource(booking.getSource());
        vehicleBookingDto.setMileageOut(booking.getMileageOut());
        vehicleBookingDto.setDamageImage(booking.getDamageImage());
//        vehicleBookingDto.setSignatureOut(booking.getSignatureOut());
        vehicleBookingDto.setSignatureOutDate(booking.getSignatureOutDateZoned());

        vehicleBookingDto.setReturnItems(booking.getReturnItems());
        vehicleBookingDto.setFuelIn(booking.getFuelIn());
        vehicleBookingDto.setMileageIn(booking.getMileageIn());
        vehicleBookingDto.setDamageImageIn(booking.getDamageImageIn());
        vehicleBookingDto.setDamageInfoIn(booking.getDamageInfoIn());
        vehicleBookingDto.setByAgency(booking.getByAgency());

        vehicleBookingDto.setExtraCharges(booking.getExtraCharges());
        vehicleBookingDto.setDamagePhotos(booking.getDamagePhotos());
        vehicleBookingDto.setDamageInfoOut(booking.getDamageInfoOut());

        // Set legacy signature fields for backward compatibility
//        vehicleBookingDto.setSignatureOut(booking.getSignatureOut());
//        vehicleBookingDto.setSignatureOutName(booking.getSignatureOutName());
//        vehicleBookingDto.setSignatureIn(booking.getSignatureIn());
//        vehicleBookingDto.setSignatureInName(booking.getSignatureInName());

        // Set new signature fields for handover process
        vehicleBookingDto.setSignatureOutHirer(booking.getSignatureOutHirer());
        vehicleBookingDto.setSignatureOutHirerName(booking.getSignatureOutHirerName());
        vehicleBookingDto.setSignatureOutCarRental(booking.getSignatureOutCarRental());
        vehicleBookingDto.setSignatureOutCarRentalName(booking.getSignatureOutCarRentalName());

        // Set new signature fields for return process
        vehicleBookingDto.setSignatureInHirer(booking.getSignatureInHirer());
        vehicleBookingDto.setSignatureInHirerName(booking.getSignatureInHirerName());
        vehicleBookingDto.setSignatureInCarRental(booking.getSignatureInCarRental());
        vehicleBookingDto.setSignatureInCarRentalName(booking.getSignatureInCarRentalName());

        vehicleBookingDto.setSignatureInDate(booking.getSignatureInDateZoned());

         vehicleBookingDto.setCancelReason(booking.getCancelReason());
        vehicleBookingDto.setId(booking.getId());
        vehicleBookingDto.setFirstname(booking.getFirstname());
        vehicleBookingDto.setSurname(booking.getSurname());
        vehicleBookingDto.setPhone(booking.getPhone());
        vehicleBookingDto.setEmail(booking.getEmail());
        vehicleBookingDto.setLocation(booking.getLocation());
        vehicleBookingDto.setVehicle((toVehicleDto.convert(booking.getVehicle(), booking.getSource()==FrontPlatform.KARLINK)));
        vehicleBookingDto.setRating(booking.getRating());

        vehicleBookingDto.setRatings(booking.getRatings());
        if(nonNull(booking.getPromotion()))vehicleBookingDto.setPromotion(toPromotionDto.convert(booking.getPromotion()));


        vehicleBookingDto.setEnd(booking.getEndZoned());
//        vehicleBookingDto.setEndLocal(isoDateTimeFormat.format(booking.getEndZoned()));
        vehicleBookingDto.setStart(booking.getStartZoned());
//        vehicleBookingDto.setStartLocal(isoDateTimeFormat.format(booking.getStartZoned()));
        vehicleBookingDto.setStatus(booking.getStatus());
        vehicleBookingDto.setClient(toClientDto.convert(booking.getClient()));
        vehicleBookingDto.setInvoices(booking.getInvoices().stream().map(toInvoiceResult::convert).collect(Collectors.toSet()));

        // Convert VehicleInventory addons to VehicleInventoryDto to avoid Hibernate proxy issues
        if (booking.getAddons() != null) {
            vehicleBookingDto.setVehicleAddons(vehicleInventoryMapper.convertSet(booking.getAddons()));
        }

        // Set deposit from vehicle if available
        if (nonNull(booking.getVehicle()) && nonNull(booking.getVehicle().getDepositAmt())) {
            vehicleBookingDto.setDeposit(BigDecimal.valueOf(booking.getVehicle().getDepositAmt()));
        }

        return vehicleBookingDto;
    }
}
