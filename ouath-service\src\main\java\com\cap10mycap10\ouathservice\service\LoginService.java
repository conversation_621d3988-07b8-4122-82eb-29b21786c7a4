package com.cap10mycap10.ouathservice.service;


import com.cap10mycap10.ouathservice.dto.UserLogin;
import com.cap10mycap10.ouathservice.dto.UserLoginRequest;
import com.cap10mycap10.ouathservice.entity.ResultDTO;
import com.cap10mycap10.ouathservice.exception.BusinessValidationException;
import com.maxmind.geoip2.exception.GeoIp2Exception;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

public interface LoginService {

    ResultDTO userLogin(UserLogin userLogin, HttpServletRequest req, Long agencyId) throws BusinessValidationException, IOException, GeoIp2Exception;

    ResultDTO refreshToken(String token);

    ResultDTO clientLogin(UserLoginRequest request);
}
