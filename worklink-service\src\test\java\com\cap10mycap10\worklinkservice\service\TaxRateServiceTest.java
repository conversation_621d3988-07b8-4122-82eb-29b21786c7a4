package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.implementation.TaxRateServiceImpl;
import com.cap10mycap10.worklinkservice.model.TaxRate;
import com.cap10mycap10.worklinkservice.repository.TaxRateRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaxRateServiceTest {

    @Mock
    private TaxRateRepository taxRateRepository;

    @InjectMocks
    private TaxRateServiceImpl taxRateService;

    private TaxRate validTaxRate;
    private Long agencyId = 1L;
    private Long taxRateId = 1L;

    @BeforeEach
    void setUp() {
        validTaxRate = new TaxRate();
        validTaxRate.setId(taxRateId);
        validTaxRate.setName("Standard VAT");
        validTaxRate.setPercentage(new BigDecimal("15.00"));
        validTaxRate.setDescription("Standard VAT rate");
        validTaxRate.setActive(true);
        validTaxRate.setAgencyId(agencyId);
    }

    @Test
    void testFindByAgencyId_Success() {
        // Given
        List<TaxRate> expectedTaxRates = Arrays.asList(validTaxRate);
        when(taxRateRepository.findByAgencyId(agencyId)).thenReturn(expectedTaxRates);

        // When
        List<TaxRate> result = taxRateService.findByAgencyId(agencyId);

        // Then
        assertEquals(expectedTaxRates, result);
        verify(taxRateRepository).findByAgencyId(agencyId);
    }

    @Test
    void testFindActiveByAgencyId_Success() {
        // Given
        List<TaxRate> expectedTaxRates = Arrays.asList(validTaxRate);
        when(taxRateRepository.findByAgencyIdAndActiveTrue(agencyId)).thenReturn(expectedTaxRates);

        // When
        List<TaxRate> result = taxRateService.findActiveByAgencyId(agencyId);

        // Then
        assertEquals(expectedTaxRates, result);
        verify(taxRateRepository).findByAgencyIdAndActiveTrue(agencyId);
    }

    @Test
    void testFindById_Success() {
        // Given
        when(taxRateRepository.findById(taxRateId)).thenReturn(Optional.of(validTaxRate));

        // When
        Optional<TaxRate> result = taxRateService.findById(taxRateId);

        // Then
        assertTrue(result.isPresent());
        assertEquals(validTaxRate, result.get());
        verify(taxRateRepository).findById(taxRateId);
    }

    @Test
    void testCreate_Success() {
        // Given
        when(taxRateRepository.findByNameAndAgencyId(validTaxRate.getName(), agencyId))
                .thenReturn(Optional.empty());
        when(taxRateRepository.save(validTaxRate)).thenReturn(validTaxRate);

        // When
        TaxRate result = taxRateService.create(validTaxRate);

        // Then
        assertEquals(validTaxRate, result);
        verify(taxRateRepository).save(validTaxRate);
    }

    @Test
    void testCreate_ValidationFailure_EmptyName() {
        // Given
        validTaxRate.setName("");

        // When & Then
        BusinessValidationException exception = assertThrows(
                BusinessValidationException.class,
                () -> taxRateService.create(validTaxRate)
        );
        assertEquals("Tax rate name is required", exception.getMessage());
        verify(taxRateRepository, never()).save(any());
    }

    @Test
    void testCreate_ValidationFailure_NullPercentage() {
        // Given
        validTaxRate.setPercentage(null);

        // When & Then
        BusinessValidationException exception = assertThrows(
                BusinessValidationException.class,
                () -> taxRateService.create(validTaxRate)
        );
        assertEquals("Tax percentage is required", exception.getMessage());
        verify(taxRateRepository, never()).save(any());
    }

    @Test
    void testCreate_ValidationFailure_NegativePercentage() {
        // Given
        validTaxRate.setPercentage(new BigDecimal("-5.00"));

        // When & Then
        BusinessValidationException exception = assertThrows(
                BusinessValidationException.class,
                () -> taxRateService.create(validTaxRate)
        );
        assertEquals("Tax percentage cannot be negative", exception.getMessage());
        verify(taxRateRepository, never()).save(any());
    }

    @Test
    void testCreate_ValidationFailure_PercentageExceedsLimit() {
        // Given
        validTaxRate.setPercentage(new BigDecimal("150.00"));

        // When & Then
        BusinessValidationException exception = assertThrows(
                BusinessValidationException.class,
                () -> taxRateService.create(validTaxRate)
        );
        assertEquals("Tax percentage cannot exceed 100%", exception.getMessage());
        verify(taxRateRepository, never()).save(any());
    }

    @Test
    void testCreate_ValidationFailure_DuplicateName() {
        // Given
        when(taxRateRepository.findByNameAndAgencyId(validTaxRate.getName(), agencyId))
                .thenReturn(Optional.of(validTaxRate));

        // When & Then
        BusinessValidationException exception = assertThrows(
                BusinessValidationException.class,
                () -> taxRateService.create(validTaxRate)
        );
        assertTrue(exception.getMessage().contains("already exists"));
        verify(taxRateRepository, never()).save(any());
    }

    @Test
    void testUpdate_Success() {
        // Given
        when(taxRateRepository.findByNameAndAgencyId(validTaxRate.getName(), agencyId))
                .thenReturn(Optional.of(validTaxRate)); // Same tax rate being updated
        when(taxRateRepository.save(validTaxRate)).thenReturn(validTaxRate);

        // When
        TaxRate result = taxRateService.update(validTaxRate);

        // Then
        assertEquals(validTaxRate, result);
        verify(taxRateRepository).save(validTaxRate);
    }

    @Test
    void testActivate_Success() {
        // Given
        when(taxRateRepository.findById(taxRateId)).thenReturn(Optional.of(validTaxRate));
        when(taxRateRepository.save(any(TaxRate.class))).thenReturn(validTaxRate);

        // When
        taxRateService.activate(taxRateId);

        // Then
        verify(taxRateRepository).findById(taxRateId);
        verify(taxRateRepository).save(argThat(taxRate -> taxRate.isActive()));
    }

    @Test
    void testDeactivate_Success() {
        // Given
        when(taxRateRepository.findById(taxRateId)).thenReturn(Optional.of(validTaxRate));
        when(taxRateRepository.save(any(TaxRate.class))).thenReturn(validTaxRate);

        // When
        taxRateService.deactivate(taxRateId);

        // Then
        verify(taxRateRepository).findById(taxRateId);
        verify(taxRateRepository).save(argThat(taxRate -> !taxRate.isActive()));
    }

    @Test
    void testDelete_Success() {
        // Given
        when(taxRateRepository.findById(taxRateId)).thenReturn(Optional.of(validTaxRate));
        when(taxRateRepository.isUsedByVehicles(taxRateId)).thenReturn(false);
        when(taxRateRepository.isUsedByVehicleInventory(taxRateId)).thenReturn(false);
        when(taxRateRepository.isUsedByInvoiceItems(taxRateId)).thenReturn(false);

        // When
        taxRateService.delete(taxRateId);

        // Then
        verify(taxRateRepository).deleteById(taxRateId);
    }

    @Test
    void testDelete_Failure_InUseByVehicles() {
        // Given
        when(taxRateRepository.findById(taxRateId)).thenReturn(Optional.of(validTaxRate));
        when(taxRateRepository.isUsedByVehicles(taxRateId)).thenReturn(true);

        // When & Then
        BusinessValidationException exception = assertThrows(
                BusinessValidationException.class,
                () -> taxRateService.delete(taxRateId)
        );
        assertTrue(exception.getMessage().contains("currently in use"));
        verify(taxRateRepository, never()).deleteById(any());
    }

    @Test
    void testDelete_Failure_TaxRateNotFound() {
        // Given
        when(taxRateRepository.findById(taxRateId)).thenReturn(Optional.empty());

        // When & Then
        BusinessValidationException exception = assertThrows(
                BusinessValidationException.class,
                () -> taxRateService.delete(taxRateId)
        );
        assertEquals("Tax rate not found with ID: " + taxRateId, exception.getMessage());
        verify(taxRateRepository, never()).deleteById(any());
    }

    @Test
    void testIsInUse_True_UsedByVehicles() {
        // Given
        when(taxRateRepository.isUsedByVehicles(taxRateId)).thenReturn(true);

        // When
        boolean result = taxRateService.isInUse(taxRateId);

        // Then
        assertTrue(result);
    }

    @Test
    void testIsInUse_False_NotUsed() {
        // Given
        when(taxRateRepository.isUsedByVehicles(taxRateId)).thenReturn(false);
        when(taxRateRepository.isUsedByVehicleInventory(taxRateId)).thenReturn(false);
        when(taxRateRepository.isUsedByInvoiceItems(taxRateId)).thenReturn(false);

        // When
        boolean result = taxRateService.isInUse(taxRateId);

        // Then
        assertFalse(result);
    }

    @Test
    void testIsNameUniqueForAgency_True() {
        // Given
        when(taxRateRepository.findByNameAndAgencyId("New Tax Rate", agencyId))
                .thenReturn(Optional.empty());

        // When
        boolean result = taxRateService.isNameUniqueForAgency("New Tax Rate", agencyId, null);

        // Then
        assertTrue(result);
    }

    @Test
    void testIsNameUniqueForAgency_False() {
        // Given
        when(taxRateRepository.findByNameAndAgencyId("Existing Tax Rate", agencyId))
                .thenReturn(Optional.of(validTaxRate));

        // When
        boolean result = taxRateService.isNameUniqueForAgency("Existing Tax Rate", agencyId, null);

        // Then
        assertFalse(result);
    }

    @Test
    void testCreateDefaultTaxRates_Success() {
        // Given
        when(taxRateRepository.existsByNameAndAgencyId("Standard VAT", agencyId))
                .thenReturn(false);
        when(taxRateRepository.existsByNameAndAgencyId("Zero Rate", agencyId))
                .thenReturn(false);

        // When
        taxRateService.createDefaultTaxRates(agencyId);

        // Then
        verify(taxRateRepository, times(2)).save(any(TaxRate.class));
    }
}
