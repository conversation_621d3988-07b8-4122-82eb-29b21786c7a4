package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.NotificationRepository;
import com.cap10mycap10.worklinkservice.dto.agency.AgencyResultDto;
import com.cap10mycap10.worklinkservice.dto.client.ClientDto;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.model.Agency;
import com.cap10mycap10.worklinkservice.model.Client;
import com.cap10mycap10.worklinkservice.model.Device;
import com.cap10mycap10.worklinkservice.model.Notification;
import com.cap10mycap10.worklinkservice.dto.notification.NotificationCreateDto;
import com.cap10mycap10.worklinkservice.enums.WorklinkUserType;
import com.cap10mycap10.worklinkservice.events.email.EmailService;
import com.cap10mycap10.worklinkservice.helpers.DataBucketUtil;
import com.cap10mycap10.worklinkservice.helpers.PushNotification;
import com.cap10mycap10.worklinkservice.service.*;
import com.google.firebase.messaging.FirebaseMessagingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.MailSendException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

import static java.time.LocalDateTime.now;

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class BroadcastServiceImpl implements BroadcastService {

    @Autowired
    private NotificationRepository notificationRepository;
    @Autowired
    private DataBucketUtil dataBucketUtil;
    @Autowired
    private WorkerService workerService;
    @Autowired
    private AgencyService agencyService;
    @Autowired
    private ClientService clientService;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private PushNotification pushNotification;
    @Autowired
    private EmailService emailService;




    @Override
    public Notification broadcast(NotificationCreateDto notificationCreateDto ) {
        List<String> emails = new ArrayList<>();
        List<Device> devices = new ArrayList<>();

        //:todo Send push notifications to other user types
        if(notificationCreateDto.getSendToType() ==  WorklinkUserType.CLIENT){
            if(notificationCreateDto.getSendToAll()){
                if(notificationCreateDto.getSenderType()==WorklinkUserType.AGENCY){
                    Set<Client> clients = agencyService.getOne(notificationCreateDto.getSendingAgencyId()).getClients();
                    clients.forEach(e->{
                        emails.add(e.getEmail());
                    });
                }else if(notificationCreateDto.getSenderType()==WorklinkUserType.ADMIN){
                    List<ClientDto> clients = clientService.findAll();
                    clients.forEach(e->{
                        emails.add(e.getEmail());
                    });
                }
            }else{
                notificationCreateDto.getSendToIds().forEach(e->{
                    emails.add(clientService.getOne(e).getEmail());
                });
            }
            
        }
        else if(notificationCreateDto.getSendToType() ==  WorklinkUserType.AGENCY){
            if(notificationCreateDto.getSendToAll()){
                if(notificationCreateDto.getSenderType()==WorklinkUserType.CLIENT){
                    Set<Agency> agencys = clientService.getOne(notificationCreateDto.getSendingClientId()).getAgencies();
                    agencys.forEach(e->{
                        emails.add(e.getEmail());
                    });
                }else if(notificationCreateDto.getSenderType()==WorklinkUserType.ADMIN){
                    List<AgencyResultDto> agencys = agencyService.findAll();
                    agencys.forEach(e->{
                        emails.add(e.getEmail());
                    });
                }
            }else{
                notificationCreateDto.getSendToIds().forEach(e->{
                    emails.add(agencyService.getOne(e).getEmail());
                });
            }
        }
        else if(notificationCreateDto.getSendToType() ==  WorklinkUserType.WORKER) {
            if(notificationCreateDto.getSenderType()==WorklinkUserType.AGENCY){
                Agency agency = agencyService.getOne(notificationCreateDto.getSendingAgencyId());
                if(notificationCreateDto.getSendToAll()){
                    agency.getWorkers().forEach(e->{
                        emails.add(e.getEmail());
                        devices.addAll(e.getDevices());
                    });
                }else{
                    agency.getWorkers().forEach(e->{
                        if(notificationCreateDto.getSendToAssCodes().contains(e.getAssignmentCode().getId())) {
                            emails.add(e.getEmail());
                            devices.addAll(e.getDevices());
                        }
                    });
                }

            }
            else if(notificationCreateDto.getSenderType()==WorklinkUserType.CLIENT){
                Client client = clientService.getOne(notificationCreateDto.getSendingClientId());
                if(notificationCreateDto.getSendToAll()){
                    client.getAgencies().forEach(a->{
                        a.getWorkers().forEach(e->{
                            emails.add(e.getEmail());
                            devices.addAll(e.getDevices());
                        });
                    });
                }else{
                    client.getAgencies().forEach(a->{
                        a.getWorkers().forEach(e->{
                            if(notificationCreateDto.getSendToAssCodes().contains(e.getAssignmentCode().getId())) {
                                emails.add(e.getEmail());
                                devices.addAll(e.getDevices());
                            }
                        });
                    });
                }
            }
            else if(notificationCreateDto.getSenderType()==WorklinkUserType.ADMIN){
                if(notificationCreateDto.getSendToAll()){
                    workerService.findAll().forEach(e->{
                        emails.add(e.getEmail());
                        devices.addAll(workerService.getOne(e.getId()).getDevices());
                    });
                }else{
                    workerService.findAll().forEach(e->{
                        if(notificationCreateDto.getSendToAssCodes().contains(e.getAssignmentCodeId())) {
                            emails.add(e.getEmail());
                            devices.addAll(workerService.getOne(e.getId()).getDevices());
                        }
                    });
                }
            }
        }


        //  Save notification
        //:todo Save notification properly
        notificationService.saveNotification(notificationCreateDto);

        // Send push notifications

        CompletableFuture.runAsync(() ->

                devices.forEach(d->{
                    try {
                        log.info("Sending notification to device:{}",d);
                        pushNotification.sendPushMessage(
                                notificationCreateDto.getTitle(),
                                notificationCreateDto.getBody(),
                                d.getFcmToken()
                        );
                    } catch (FirebaseMessagingException e) {
                        log.error("An error occurred while trying to send notification:",e);
                    }
                })
        );

//        // Send emails
        if(notificationCreateDto.getFile()!=null){
            emails.forEach(e-> {
                ArrayList<String> email = new ArrayList<>();
                email.add(e);
                try{
                    CompletableFuture.runAsync(() ->
                            emailService.sendEmailAsUserReply(
                                    email,
                                    notificationCreateDto.getTitle(),
                                    notificationCreateDto.getBody(),
                                    null,null,
                                    notificationCreateDto.getSendingAgencyId()
                            )
                    );
                }catch (MailSendException err){
                    log.error(String.valueOf(err));
                }
            });

        }else {
            emails.forEach(e->{
                try{
                    CompletableFuture.runAsync(() ->
                            emailService.sendSimpleMessage(
                                    e,
                                    notificationCreateDto.getTitle(),
                                    notificationCreateDto.getBody(),
                                    notificationCreateDto.getSendingAgencyId()
                            )
                    );
                }catch (MailSendException err){
                    log.error(String.valueOf(err));
                }
            });

        }

        return null;
    }

    @Override
    public List<Notification> findAgencySent(Long id) {
        return notificationRepository.findAllBySenderAgencyOrderByCreatedDateDesc(agencyService.getOne(id));
    }

    @Override
    public void delete(Long id) {
        Notification not = getOne(id);
        notificationRepository.delete(not);
    }



    @Override
    public List<Notification> findClientSent(Long id) {
        return notificationRepository.findAllBySenderClientOrderByIdDesc(clientService.getOne(id));
    }
    @Override
    public List<Notification> findAdminSent() {
        return notificationRepository.findAllBySenderTypeOrderByIdDesc(WorklinkUserType.ADMIN);
    }

    @Override
    public NotificationCreateDto findByWorkerId(Long id) {
        return null;
    }

    @Override
    public void deleteById(Long id) {

    }

    @Override
    public Notification getOne(Long id) {
        return notificationRepository.findById(id).orElseThrow(
                ()-> new RecordNotFoundException(String.format("Notification with id: %s not found", id))
        );
    }

    @Override
    public void addGeneralSignature(Long workerId, MultipartFile file) {

    }
}
