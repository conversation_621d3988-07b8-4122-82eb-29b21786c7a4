//package com.cap10mycap10.userservice.events;
//
//
//import com.cap10mycap10.userservice.model.User;
//import com.cap10mycap10.userservice.service.EmailService;
//import com.cap10mycap10.userservice.service.UserService;
//import org.springframework.context.ApplicationListener;
//import org.springframework.context.MessageSource;
//import org.springframework.mail.SimpleMailMessage;
//import org.springframework.stereotype.Component;
//
//
//import javax.mail.MessagingException;
//import javax.mail.internet.MimeMessage;
//import java.util.Collections;
//import java.util.UUID;
//
//
//@Component
//public class RegistrationCompleteListener implements ApplicationListener<OnRegistrationCompleteEvent> {
//
//
//    private final UserService service;
//
//    private final MessageSource messages;
//
//    private final EmailService emailService;
//
//    public RegistrationCompleteListener(final UserService service,
//                                        final MessageSource messages,
//                                        final EmailService emailService) {
//        this.service = service;
//        this.messages = messages;
//        this.emailService = emailService;
//    }
//
//    // API
//
//    @Override
//    public void onApplicationEvent(final OnRegistrationCompleteEvent event, Long agencyId) {
//        try {
//            this.confirmRegistration(event, agencyId);
//        } catch (MessagingException e) {
//            e.printStackTrace();
//        }
//    }
//
//    private void confirmRegistration(final OnRegistrationCompleteEvent event, Long agencyId) throws MessagingException {
//
//        final User user = event.getUser();
//        final String token = UUID.randomUUID().toString();
//        service.createVerificationTokenForUser(user, token);
//         constructEmailMessage(event, user, token, agencyId);
//
//    }
//
//
//    private void constructEmailMessage(final OnRegistrationCompleteEvent event, final User user, final String token, Long agencyId) throws MessagingException {
//        final String confirmationUrl = event.getAppUrl() + "/registrationConfirm.html?token=" + token;
//
//        // Use enhanced email service for agency-specific branding
//        emailService.sendRegistrationConfirmationEmail(user, confirmationUrl, agencyId);
//    }
//
//}
