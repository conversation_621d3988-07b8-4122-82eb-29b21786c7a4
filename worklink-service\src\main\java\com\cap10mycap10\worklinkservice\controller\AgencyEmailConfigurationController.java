package com.cap10mycap10.worklinkservice.controller;

import com.cap10mycap10.worklinkservice.dto.email.AgencyEmailConfigurationDto;
import com.cap10mycap10.worklinkservice.dto.email.TestEmailDto;
import com.cap10mycap10.worklinkservice.model.AgencyEmailConfiguration;
import com.cap10mycap10.worklinkservice.service.AgencyEmailConfigurationService;
import com.cap10mycap10.worklinkservice.service.EmailSenderFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * REST Controller for managing agency email configurations
 */
@RestController
@RequestMapping("/api/v1/agency-email-config")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class AgencyEmailConfigurationController {

    private final AgencyEmailConfigurationService emailConfigService;
    private final EmailSenderFactory emailSenderFactory;

    /**
     * Get email configuration for an agency
     */
    @GetMapping("/agency/{agencyId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('AGENCY_ADMIN')")
    public ResponseEntity<AgencyEmailConfigurationDto> getConfigurationByAgencyId(@PathVariable Long agencyId) {
        log.info("Getting email configuration for agency ID: {}", agencyId);
        
        Optional<AgencyEmailConfiguration> config = emailConfigService.findByAgencyId(agencyId);
        if (config.isPresent()) {
            return ResponseEntity.ok(AgencyEmailConfigurationDto.fromEntity(config.get()));
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Create or update email configuration for an agency
     */
    @PostMapping("/agency/{agencyId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('AGENCY_ADMIN')")
    public ResponseEntity<AgencyEmailConfigurationDto> createOrUpdateConfiguration(
            @PathVariable Long agencyId,
            @Valid @RequestBody AgencyEmailConfigurationDto configDto) {
        
        log.info("Creating/updating email configuration for agency ID: {}", agencyId);
        
        try {
            // Ensure the agency ID matches
            configDto.setAgencyId(agencyId);
            
            // Check if configuration already exists
            Optional<AgencyEmailConfiguration> existingConfig = emailConfigService.findByAgencyId(agencyId);
            if (existingConfig.isPresent()) {
                // Update existing configuration
                AgencyEmailConfiguration existing = existingConfig.get();
                configDto.setId(existing.getId());
                configDto.setCreatedAt(existing.getCreatedAt());
                configDto.setCreatedBy(existing.getCreatedBy());
            }
            
            // Convert DTO to entity and save
            AgencyEmailConfiguration entity = configDto.toEntity();
            AgencyEmailConfiguration savedConfig = emailConfigService.save(entity);
            
            // Clear cache for this agency
            emailSenderFactory.clearAgencyCache(agencyId);
            
            return ResponseEntity.ok(AgencyEmailConfigurationDto.fromEntity(savedConfig));
            
        } catch (Exception e) {
            log.error("Error creating/updating email configuration for agency ID: {}", agencyId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Test email configuration
     */
    @PostMapping("/test")
    @PreAuthorize("hasRole('ADMIN') or hasRole('AGENCY_ADMIN')")
    public ResponseEntity<Map<String, Object>> testConfiguration(@Valid @RequestBody TestEmailDto testDto) {
        log.info("Testing email configuration for agency ID: {}", testDto.getAgencyId());
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean testResult = emailConfigService.testConfiguration(
                testDto.getAgencyId(), 
                testDto.getTestEmailAddress()
            );
            
            response.put("success", testResult);
            response.put("message", testResult ? 
                "Test email sent successfully" : 
                "Test email failed - please check your configuration");
            
            if (testResult) {
                // Clear cache to ensure fresh configuration is used
                emailSenderFactory.clearAgencyCache(testDto.getAgencyId());
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error testing email configuration for agency ID: {}", testDto.getAgencyId(), e);
            response.put("success", false);
            response.put("message", "Test failed: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * Activate email configuration
     */
    @PutMapping("/agency/{agencyId}/activate")
    @PreAuthorize("hasRole('ADMIN') or hasRole('AGENCY_ADMIN')")
    public ResponseEntity<AgencyEmailConfigurationDto> activateConfiguration(@PathVariable Long agencyId) {
        log.info("Activating email configuration for agency ID: {}", agencyId);
        
        try {
            AgencyEmailConfiguration config = emailConfigService.activateConfiguration(agencyId);
            emailSenderFactory.clearAgencyCache(agencyId);
            return ResponseEntity.ok(AgencyEmailConfigurationDto.fromEntity(config));
        } catch (Exception e) {
            log.error("Error activating email configuration for agency ID: {}", agencyId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Deactivate email configuration
     */
    @PutMapping("/agency/{agencyId}/deactivate")
    @PreAuthorize("hasRole('ADMIN') or hasRole('AGENCY_ADMIN')")
    public ResponseEntity<AgencyEmailConfigurationDto> deactivateConfiguration(@PathVariable Long agencyId) {
        log.info("Deactivating email configuration for agency ID: {}", agencyId);
        
        try {
            AgencyEmailConfiguration config = emailConfigService.deactivateConfiguration(agencyId);
            emailSenderFactory.clearAgencyCache(agencyId);
            return ResponseEntity.ok(AgencyEmailConfigurationDto.fromEntity(config));
        } catch (Exception e) {
            log.error("Error deactivating email configuration for agency ID: {}", agencyId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Delete email configuration
     */
    @DeleteMapping("/agency/{agencyId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, String>> deleteConfiguration(@PathVariable Long agencyId) {
        log.info("Deleting email configuration for agency ID: {}", agencyId);
        
        try {
            emailConfigService.deleteByAgencyId(agencyId);
            emailSenderFactory.clearAgencyCache(agencyId);
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "Email configuration deleted successfully");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error deleting email configuration for agency ID: {}", agencyId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get all email configurations (Admin only)
     */
    @GetMapping("/all")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<List<AgencyEmailConfigurationDto>> getAllConfigurations() {
        log.info("Getting all email configurations");
        
        try {
            List<AgencyEmailConfiguration> configs = emailConfigService.findAll();
            List<AgencyEmailConfigurationDto> dtos = configs.stream()
                .map(AgencyEmailConfigurationDto::fromEntity)
                .collect(Collectors.toList());
            
            return ResponseEntity.ok(dtos);
            
        } catch (Exception e) {
            log.error("Error getting all email configurations", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Check if agency has custom email configuration
     */
    @GetMapping("/agency/{agencyId}/status")
    @PreAuthorize("hasRole('ADMIN') or hasRole('AGENCY_ADMIN')")
    public ResponseEntity<Map<String, Object>> getConfigurationStatus(@PathVariable Long agencyId) {
        log.info("Getting email configuration status for agency ID: {}", agencyId);
        
        Map<String, Object> status = new HashMap<>();
        status.put("agencyId", agencyId);
        status.put("hasConfiguration", emailConfigService.existsByAgencyId(agencyId));
        status.put("hasActiveConfiguration", emailConfigService.hasActiveConfiguration(agencyId));
        status.put("hasVerifiedConfiguration", emailConfigService.hasVerifiedActiveConfiguration(agencyId));
        status.put("isUsingCustomEmail", emailSenderFactory.hasCustomEmailConfiguration(agencyId));
        
        return ResponseEntity.ok(status);
    }

    /**
     * Clear email cache for agency
     */
    @PostMapping("/agency/{agencyId}/clear-cache")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, String>> clearCache(@PathVariable Long agencyId) {
        log.info("Clearing email cache for agency ID: {}", agencyId);
        
        emailSenderFactory.clearAgencyCache(agencyId);
        
        Map<String, String> response = new HashMap<>();
        response.put("message", "Email cache cleared successfully for agency ID: " + agencyId);
        return ResponseEntity.ok(response);
    }

    /**
     * Clear all email caches
     */
    @PostMapping("/clear-all-caches")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, String>> clearAllCaches() {
        log.info("Clearing all email caches");
        
        emailSenderFactory.clearAllCaches();
        
        Map<String, String> response = new HashMap<>();
        response.put("message", "All email caches cleared successfully");
        return ResponseEntity.ok(response);
    }
}
