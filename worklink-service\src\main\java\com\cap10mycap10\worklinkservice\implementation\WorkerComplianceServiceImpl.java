package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.AgencyWorkerComplianceRepository;
import com.cap10mycap10.worklinkservice.dao.WorkerComplianceRepository;
import com.cap10mycap10.worklinkservice.dto.notification.NotificationCreateDto;
import com.cap10mycap10.worklinkservice.dto.workercompliance.IWorkerComplianceResultDto;
import com.cap10mycap10.worklinkservice.dto.workercompliance.WorkerComplianceCreateDto;
import com.cap10mycap10.worklinkservice.dto.workercompliance.WorkerComplianceResultDto;
import com.cap10mycap10.worklinkservice.dto.workercompliance.WorkerComplianceUpdateDto;
import com.cap10mycap10.worklinkservice.enums.ComplianceStatus;
import com.cap10mycap10.worklinkservice.enums.TrainingStatus;
import com.cap10mycap10.worklinkservice.enums.WorklinkUserType;
import com.cap10mycap10.worklinkservice.events.email.EmailService;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.helpers.DataBucketUtil;
import com.cap10mycap10.worklinkservice.helpers.PushNotification;
import com.cap10mycap10.worklinkservice.mapper.workercompliance.WorkerComplianceToWorkerComplianceResultDto;
import com.cap10mycap10.worklinkservice.model.AgencyWorkerCompliance;
import com.cap10mycap10.worklinkservice.model.Compliance;
import com.cap10mycap10.worklinkservice.model.WorkerCompliance;
import com.cap10mycap10.worklinkservice.service.*;
import com.google.firebase.messaging.FirebaseMessagingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static java.util.Objects.nonNull;

@Service
@Slf4j
public class  WorkerComplianceServiceImpl implements WorkerComplianceService {
    @Value("${storage.volume.path}")
    private  String rootPath;
    private final AgencyService agencyService;

    private final EmailService emailService;

    private final PushNotification pushNotification;

    private final NotificationService notificationService;

    private final DeviceService deviceService;

    private final DataBucketUtil dataBucketUtil;

    private final WorkerService workerService;

    private final ComplianceService complianceService;

    private final WorkerComplianceRepository workerComplianceRepository;
    private final AgencyWorkerComplianceRepository agencyWorkerComplianceRepository;
    private final WorkerComplianceToWorkerComplianceResultDto toWorkerComplianceResultDto;


    public WorkerComplianceServiceImpl(AgencyService agencyService, EmailService emailService, PushNotification pushNotification, NotificationService notificationService, DeviceService deviceService, DataBucketUtil dataBucketUtil, WorkerService workerService, ComplianceService complianceService,
                                       WorkerComplianceRepository workerComplianceRepository, AgencyWorkerComplianceRepository agencyWorkerComplianceRepository, WorkerComplianceToWorkerComplianceResultDto toWorkerComplianceResultDto) {
        this.agencyService = agencyService;
        this.emailService = emailService;
        this.pushNotification = pushNotification;
        this.notificationService = notificationService;
        this.deviceService = deviceService;
        this.dataBucketUtil = dataBucketUtil;
        this.workerService = workerService;
        this.complianceService = complianceService;
        this.workerComplianceRepository = workerComplianceRepository;
        this.agencyWorkerComplianceRepository = agencyWorkerComplianceRepository;
        this.toWorkerComplianceResultDto = toWorkerComplianceResultDto;
    }

    @Override
    public WorkerComplianceResultDto addWorkerCompliance(WorkerComplianceCreateDto createDto) {
        WorkerCompliance workerCompliance = new WorkerCompliance();
        Compliance compliance = complianceService.getOne(createDto.getComplianceId());
        workerCompliance.setDescription(createDto.getDescription());
        workerCompliance.setCode(compliance.getCode());
        if(nonNull(createDto.getFile())) {
            String fileUrl = dataBucketUtil.uploadFile(createDto.getFile(), "ComplianceDoc", String.valueOf(createDto.getWorkerId()), WorklinkUserType.WORKER);
            workerCompliance.setDocument(fileUrl);
        }
        workerCompliance.setName(compliance.getName());
        workerCompliance.setComplianceDate(LocalDate.parse(createDto.getComplianceDate()));
        workerCompliance.setComplianceExpiry(LocalDate.parse(createDto.getComplianceExpiry()));
        workerCompliance.setAgency(agencyService.getOne(createDto.getAgencyId()));
        workerCompliance.setWorker(workerService.getOne(createDto.getWorkerId()));
        workerCompliance.setCompliance(compliance);
        workerComplianceRepository.saveAndFlush(workerCompliance);
        return toWorkerComplianceResultDto.convert(workerCompliance);
    }

    @Override
    public void updateWorkerCompliance(WorkerComplianceUpdateDto workerComplianceUpdateDto) {
//        try {
        WorkerCompliance workerCompliance = getOne(workerComplianceUpdateDto.getId());
        AgencyWorkerCompliance agencyWorkerCompliance;
        try {
            agencyWorkerCompliance = agencyWorkerComplianceRepository
                    .findAllByWorkerIdAndAgencyIdAndComplianceId(
                            workerCompliance.getWorker().getId(),
                            workerComplianceUpdateDto.getAgencyId(),
                            workerCompliance.getId()
                    ).get(0);
        }
        catch (IndexOutOfBoundsException e){

            AgencyWorkerCompliance trainer = new AgencyWorkerCompliance();
            trainer.setAgency(agencyService.getOne(workerComplianceUpdateDto.getAgencyId()));
            trainer.setWorker(workerService.getOne(workerComplianceUpdateDto.getWorkerId() ));
            trainer.setCompliance(getOne(workerCompliance.getId()));

            agencyWorkerCompliance = trainer;
        }


        log.info("Updated worker-compliance{}", workerCompliance);
        if (workerComplianceUpdateDto.getDescription() != null) {
            workerCompliance.setDescription(workerComplianceUpdateDto.getDescription());
        }
        if (workerComplianceUpdateDto.getComplianceDate() != null) {
            workerCompliance.setComplianceDate(workerComplianceUpdateDto.getComplianceDate());
        }
        if (workerComplianceUpdateDto.getComplianceExpiry() != null) {
            workerCompliance.setComplianceExpiry(workerComplianceUpdateDto.getComplianceExpiry());
        }
        if (workerComplianceUpdateDto.getComment() != null) {
            workerCompliance
                    .setComment(
                            workerComplianceUpdateDto
                                    .getComment()
                    );
        }



        if (workerComplianceUpdateDto.getStatus() != null) {
            if (workerComplianceUpdateDto.getStatus().equalsIgnoreCase(ComplianceStatus.APPROVED.toString())) {
                workerCompliance.setStatus(ComplianceStatus.APPROVED);
                agencyWorkerCompliance.setStatus(ComplianceStatus.APPROVED);
            }
            if (workerComplianceUpdateDto.getStatus().equalsIgnoreCase(ComplianceStatus.REJECTED.toString())) {
                workerCompliance.setStatus(ComplianceStatus.REJECTED);
                agencyWorkerCompliance.setStatus(ComplianceStatus.REJECTED);

                String title = "Worker Compliance Document Rejection";
                String body = "Your worker compliance document has been rejected";
                NotificationCreateDto notificationCreateDto = new NotificationCreateDto();

                notificationCreateDto.setTitle(title);
                notificationCreateDto.setBody(body);
                notificationCreateDto.setWorkerId(workerCompliance.getWorker().getId());
                notificationService.addWorkerNotification(notificationCreateDto);
                notificationService.addWorkerNotification(notificationCreateDto);
                workerCompliance.getWorker().getDevices().forEach(d->{
                    CompletableFuture.runAsync(() -> {
                        try {
                            pushNotification.sendPushMessage(title, body, d.getFcmToken());
                        } catch (FirebaseMessagingException e) {
                            log.error(e.toString());
                            deviceService.delete(d);
                        }
                    });
                });

                emailService.sendEmailAsUserReply(List.of(workerCompliance.getWorker().getEmail()),
                        title,
                        body,
                        agencyWorkerCompliance.getAgency().getEmail(),
                        agencyWorkerCompliance.getAgency().getName(),
                        agencyWorkerCompliance.getAgency().getId()
                        );


            }
        }

        agencyWorkerComplianceRepository.save(agencyWorkerCompliance);
        workerComplianceRepository.save(workerCompliance);



//        } catch (Exception exception) {
//            throw new BusinessValidationException(exception.getMessage());
//        }
    }

    @Override
    public void deleteWorkerCompliance(Long id) {
        WorkerCompliance availability = workerComplianceRepository.findById(id).get();
        workerComplianceRepository.delete(availability);
        workerComplianceRepository.flush();
    }

    @Override
    public WorkerCompliance findById(Long id) {
        return null;
    }

    @Override
    public Page<IWorkerComplianceResultDto> findWorkerCompliances(Long workerId, PageRequest of) {

        Page<IWorkerComplianceResultDto> iWorkerComplianceResultDto = workerComplianceRepository.findAllWorkerCompliances(workerId, of);

//        List<IWorkerComplianceResultDto> list = iWorkerComplianceResultDto.getContent();
//        list.forEach(
//                (compliance) -> {
//                    compliance.setComplianceDate(compliance.getComplianceDate().split(" ", 2)[0]);
//                }
//        );

        return iWorkerComplianceResultDto;
    }

    @Override
    public Page<WorkerCompliance> findAllPaged(PageRequest of) {
        return null;
    }

    @Override
    public WorkerCompliance save(WorkerComplianceUpdateDto workerComplianceUpdateDto) {
        return null;
    }

    @Override
    public WorkerCompliance getOne(Long id) {
        return workerComplianceRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Worker compliance not found"));
    }

    @Override
    public List<WorkerComplianceResultDto> findAgencyWorkerCompliances(Long workerId, Long agencyId, PageRequest of) {

        Page<WorkerComplianceResultDto> iWorkerTrainingResultDto = workerComplianceRepository.findAllWorkerCompliances2(workerId, of).map(toWorkerComplianceResultDto::convert);;

        List<WorkerComplianceResultDto> list = iWorkerTrainingResultDto.getContent();


        list.forEach(
                (train) -> {
                    try{
                        train.setStatus(agencyWorkerComplianceRepository.findAgencyWorkerCompliance(workerId, agencyId, train.getId()).getStatus().toString());

                    }catch (NullPointerException e){

                        AgencyWorkerCompliance trainer = new AgencyWorkerCompliance();
                        trainer.setAgency(agencyService.getOne(agencyId));
                        trainer.setWorker(workerService.getOne(workerId));
                        trainer.setCompliance(getOne(train.getId()));


                        train.setStatus(TrainingStatus.WAITING_APPROVAL.toString());

                    }

                }
        );

        return list;
    }

    @Override
    public void addComplianceDoc(Long workerId,Long compliance, MultipartFile files) {
        WorkerCompliance wc = getOne(compliance);
        String url = dataBucketUtil.uploadFile(files,"WorkerCompliance",wc.getWorker().getId().toString(), WorklinkUserType.WORKER );
        wc.setDocument(url);
        workerComplianceRepository.save(wc);
    }





}
