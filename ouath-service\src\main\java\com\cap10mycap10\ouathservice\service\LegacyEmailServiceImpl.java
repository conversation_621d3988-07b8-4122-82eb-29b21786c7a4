package com.cap10mycap10.ouathservice.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.mail.MailException;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.List;

import static java.util.Objects.nonNull;

/**
 * Legacy email service implementation (kept for backward compatibility)
 */
@Service("legacyEmailService")
@Slf4j
public class LegacyEmailServiceImpl {
    @Value("${env.companyName}")
    private String companyName;
    @Value("${env.companyLogo}")
    private String companyLogo;
    @Value("${env.email}")
    private String NOREPLY_ADDRESS;
    @Value("${env.supportEmail}")
    private String supportEmail;
    @Value("${env.website}")
    private String websiteUrl;
    @Autowired
    private  JavaMailSender emailSender;
    @Value("classpath:/nhaka-logo.png")
    private Resource resourceFile;


    @Async("threadPoolTaskExecutor")
    public void sendEmail(List<String> to, String subject, String text, MultipartFile file) {
        try {
            MimeMessage message = emailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            String[] myArray = new String[to.size()];
            to.toArray(myArray);
            try {
                helper.setFrom(NOREPLY_ADDRESS, companyName);
            } catch (UnsupportedEncodingException e) {
                helper.setFrom(NOREPLY_ADDRESS);
            }

            helper.setTo(myArray);
            helper.setSubject(subject);
            helper.setText(text, "<html><body>"+text+"<br></body></html>");

            helper.addAttachment("File.jpg", file);

            emailSender.send(message);
        } catch (MessagingException e) {
            e.printStackTrace();
        }
    }



    @Async("threadPoolTaskExecutor")
    public void sendMessageWithAttachment(List<String> to, String subject, String text) {
        try {
            MimeMessage message = emailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            String[] myArray = new String[to.size()];
            to.toArray(myArray);
            try {
                helper.setFrom(NOREPLY_ADDRESS, companyName);
            } catch (UnsupportedEncodingException e) {
                helper.setFrom(NOREPLY_ADDRESS);
            }
            helper.setTo(myArray);
            helper.setSubject(subject);
            helper.setText(text, "<html><body>"+text+"<br></body></html>");

            emailSender.send(message);
        } catch (MessagingException e) {
            e.printStackTrace();
        }
    }




    public void sendInvoice(String to, String subject, String emailMessage, String file, Long invoiceId) throws MessagingException, IOException {


        try {
            MimeMessage message = emailSender.createMimeMessage();
            message.setFrom(new InternetAddress(NOREPLY_ADDRESS));
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(to));
            message.setSubject(subject.concat(": ").concat(String.valueOf(invoiceId)));
            message.setText("Please find attached your invoice");
            BodyPart messageBodyPart = new MimeBodyPart();
            messageBodyPart.setText(emailMessage);

            MimeBodyPart attachmentPart = new MimeBodyPart();
            attachmentPart.attachFile(new File(file));

            Multipart multipart = new MimeMultipart();
            multipart.addBodyPart(messageBodyPart);
            multipart.addBodyPart(attachmentPart);

            message.setContent(multipart);
            Transport.send(message);
        }catch (Exception ex){

        }
    }


    public void sendPayAdvice(String to, String subject, String emailMessage, String file, Long invoiceId) throws MessagingException, IOException {
        try {
            MimeMessage message = emailSender.createMimeMessage();
            message.setFrom(new InternetAddress(NOREPLY_ADDRESS));
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(to));
            message.setSubject(subject.concat(": ").concat(String.valueOf(invoiceId)));
            message.setText("Please find attached your invoice");
            BodyPart messageBodyPart = new MimeBodyPart();
            messageBodyPart.setText(emailMessage);

            MimeBodyPart attachmentPart = new MimeBodyPart();
            attachmentPart.attachFile(new File(file));

            Multipart multipart = new MimeMultipart();
            multipart.addBodyPart(messageBodyPart);
            multipart.addBodyPart(attachmentPart);

            message.setContent(multipart);
            Transport.send(message);
        }catch (Exception ex){

        }
    }


    private void sendHtmlMessage(String to, String subject, String htmlBody) throws MessagingException {

        MimeMessage message = emailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
        helper.setFrom(NOREPLY_ADDRESS);
        helper.setTo(to);
        helper.setSubject(subject);
        helper.setText(htmlBody, true);
        helper.addInline("attachment.png", resourceFile);
        emailSender.send(message);
    }



    public void sendEmailAsUser(List<String> to, String subject, String text) {
        sendEmailAsUser(to, subject, text, null, null);
    }



    @Async("threadPoolTaskExecutor")
    public void sendEmailAsUser(List<String> to, String subject, String text, String replyToEmail, String replyToName) {
        text = text.concat(
                "Best regards,  \n" +
                        companyName+" Team  \n\n" +
                        "Email us: "+supportEmail+"\n"+
                        "Visit the "+ companyName + " website "+ websiteUrl
        );

        text = text.replaceAll("(\r\n|\n)", "<br />");


        try {
            MimeMessage message = emailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            String[] myArray = new String[to.size()];
            to.toArray(myArray);
            try {
                if(nonNull(replyToEmail)&&nonNull(replyToName))
                    helper.setReplyTo(replyToEmail, replyToName);
                if(nonNull(replyToName))
                    helper.setFrom(NOREPLY_ADDRESS, replyToName);
                else
                    helper.setFrom(NOREPLY_ADDRESS, companyName+" Team");
            } catch (UnsupportedEncodingException e) {
                helper.setFrom(NOREPLY_ADDRESS);
            }
            helper.setTo(myArray);
            helper.setSubject(subject);
            helper.setText( getHtmlEmail(text, subject), true);
            emailSender.send(message);
        } catch (MessagingException e) {
            e.printStackTrace();
        }
    }



    public String getHtmlEmail(String text, String title) {
        String svgLogo = "<svg width=\"182\" height=\"59\" viewBox=\"0 0 182 59\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n" +
                "<path d=\"M33.223 58.5064H22.0489L13.6185 44.9821L9.67758 49.4738V58.5064H0V23.363H9.67758V37.9238L21.5501 23.363H32.7242L19.9039 37.8745L33.223 58.5064Z\" fill=\"#FF6B00\"/>\n" +
                "<path d=\"M43.2027 59C36.9671 59 33.7246 55.9891 33.7246 51.3C33.7246 47.302 35.9694 43.9456 42.7038 43.4026L48.5403 42.909C50.0368 42.761 50.4858 42.1687 50.4858 41.4283C50.4858 39.7501 49.3883 38.8616 47.4927 38.8616C45.5971 38.8616 44.5495 39.7501 44.3001 41.4283H35.1214C35.1214 36.0482 38.6632 32.5437 47.393 32.5437C56.1227 32.5437 59.6645 36.0482 59.6645 41.4283V51.9911C59.6645 55.4462 59.9638 57.6673 60.4627 58.5064H52.0821C51.3837 57.7167 50.9846 56.7295 50.9846 55.5449C49.4881 57.8154 47.393 59 43.2027 59ZM45.9463 52.9782C48.7398 52.9782 50.4858 51.0039 50.4858 48.5853V47.6968C50.2364 47.8943 49.4881 48.0423 48.4904 48.1411L46.0461 48.3385C43.7514 48.4866 42.9034 49.3751 42.9034 50.609C42.9034 52.1391 43.8512 52.9782 45.9463 52.9782Z\" fill=\"#FF6B00\"/>\n" +
                "<path d=\"M72.8379 58.5064H63.6592V33.0373H72.3391V38.2693C73.6361 33.827 75.7811 32.6424 79.0236 32.6424C79.722 32.6424 81.019 32.8399 81.4181 33.0373V40.9347H78.6245C75.1326 40.9347 72.8379 43.2052 72.8379 47.1539V58.5064Z\" fill=\"#FF6B00\"/>\n" +
                "<path d=\"M109.268 58.5064H83.7269V23.363H93.4044V50.7077H109.268V58.5064Z\" fill=\"#002357\"/>\n" +
                "<path d=\"M120.954 58.5064H111.775V33.0373H120.954V58.5064ZM116.365 30.6681C113.072 30.6681 111.276 28.7431 111.276 26.1765C111.276 23.6098 113.072 21.6848 116.365 21.6848C119.657 21.6848 121.453 23.6098 121.453 26.1765C121.453 28.7431 119.657 30.6681 116.365 30.6681Z\" fill=\"#002357\"/>\n" +
                "<path d=\"M149.871 58.5064H140.692V43.5507C140.692 41.1321 139.794 39.8488 137.898 39.8488C135.604 39.8488 134.107 41.3296 134.107 44.2911V58.5064H124.928V33.0373H133.608V36.4924C134.955 34.3206 136.901 32.5437 140.891 32.5437C146.778 32.5437 149.871 35.5052 149.871 41.6751V58.5064Z\" fill=\"#002357\"/>\n" +
                "<path d=\"M182 58.5064H171.424L165.788 49.2763L163.044 52.0898V58.5064H153.865V23.363H163.044V41.8232L170.926 33.0373H181.501L171.973 42.909L182 58.5064Z\" fill=\"#002357\"/>\n" +
                "<path d=\"M111.037 19.945C76.8073 -4.98389 46.2629 9.0083 31.3257 25.0429C29.7757 25.0429 24.9845 24.3736 21.6025 23.3697C56.7706 -16.6174 98.5138 3.32557 111.037 19.945Z\" fill=\"#FF6B00\"/>\n" +
                "</svg>";

        return String.format(
                "<!DOCTYPE html>" +
                        "<html lang='en'>" +
                        "<head>" +
                        "<meta charset='UTF-8'>" +
                        "<meta name='viewport' content='width=device-width, initial-scale=1.0'>" +
                        "<title>" + title + "</title>" +
                        "<style>" +
                        "body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; background-color: #f0f0f0; padding: 20px; }" +
                        ".container { width: 80%%; margin: auto; padding: 20px; border: 1px solid #ddd; border-radius: 15px; background-color: #fff; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }" +
                        ".header { text-align: center; padding-bottom: 20px; }" +
                        ".logo { display: block; margin: 0 auto; max-width: 100px; }" +
                        ".content { margin-top: 20px; }" +
                        ".footer { text-align: center; margin-top: 30px; }" +
                        "button {     background-color: #002357;\n" +
                        "    color: white;\n" +
                        "    padding: 9px;\n" +
                        "    text-align: center;\n" +
                        "    display: inline-block;\n" +
                        "    border: none;\n" +
                        "    border-radius: 5px;}" +
                        ".footer img { width: 100px; height: auto; }" +
                        "</style>" +
                        "</head>" +
                        "<body>" +
                        "<div class='container'>" +
                        "<div class='content'>" +
                        text +
                        "</div><br>" +
                        "<div class='footer'>" +
                        svgLogo +
                        "</div>" +
                        "</div>" +
                        "</body>" +
                        "</html>"
        );
    }




    public SimpleMailMessage sendSimpleMessage(String to, String subject, String text) {
        SimpleMailMessage message = new SimpleMailMessage();
        try {
            message.setFrom(NOREPLY_ADDRESS);
            message.setTo(to);
            message.setSubject(subject);
            message.setText(text);
            new Thread(() -> emailSender.send(message)).start();
        } catch (MailException exception) {
            exception.printStackTrace();
        }
        return message;
    }
}
