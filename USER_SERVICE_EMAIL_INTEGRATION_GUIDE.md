# User Service Email Integration Guide

This guide explains how the user-service now integrates with agency-specific email configuration for white-label email sending.

## Overview

The user-service has been enhanced to support agency-specific email branding for:
- **Password reset emails**
- **User registration emails** 
- **Registration confirmation emails**

## Architecture

### Components Added

1. **WorklinkServiceClient** - Feign client to communicate with worklink-service
2. **AgencyService** - Service to get agency information for users
3. **EmailSenderFactory** - Factory to create agency-specific email senders
4. **EnhancedEmailServiceImpl** - New email service with agency support
5. **AgencyResultDto** - DTO for agency information
6. **AgencyEmailConfigurationDto** - DTO for email configuration

### Integration Flow

```
User Registration/Password Reset
        ↓
UserServiceImpl
        ↓
EnhancedEmailServiceImpl
        ↓
AgencyService.getAgencyForUser()
        ↓
WorklinkServiceClient (Feign)
        ↓
worklink-service API endpoints
        ↓
Agency email configuration
        ↓
Agency-branded email sent
```

## New Endpoints in worklink-service

The following endpoints were added to support user-service integration:

- `GET /api/v1/agents/{agentId}/agency` - Get agency for agent
- `GET /api/v1/clients/{clientId}/agency` - Get agency for client  
- `GET /api/v1/workers/{workerId}/agency` - Get agency for worker

## Email Transformation

### Content Transformation
- **System names**: "MyWorklink", "KarLink", "MyKarlink" → Agency name
- **URLs**: Default URLs → Agency website URLs
- **Support emails**: Default support → Agency support email
- **Logos**: Default logo → Agency logo

### Email Types Enhanced

#### 1. Password Reset Emails
**Before:**
```
Subject: Password Reset Request
From: <EMAIL> (MyWorklink)
Content: "We received a request to reset your password for your MyWorklink account..."
```

**After (Agency Branded):**
```
Subject: Password Reset Request  
From: <EMAIL> (Premium Car Rentals)
Content: "We received a request to reset your password for your Premium Car Rentals account..."
```

#### 2. User Registration Emails
**Before:**
```
Subject: MyWorklink Account Creation
Content: "Welcome to MyWorklink! A new account has been created..."
```

**After (Agency Branded):**
```
Subject: Premium Car Rentals Account Creation
Content: "Welcome to Premium Car Rentals! A new account has been created..."
```

#### 3. Registration Confirmation Emails
**Before:**
```
Subject: Registration Confirmation
Content: "Thank you for registering with MyWorklink..."
```

**After (Agency Branded):**
```
Subject: Registration Confirmation
Content: "Thank you for registering with Premium Car Rentals..."
```

## User-Agency Relationship Mapping

The system determines agency association through:

1. **Agent Users** (`user.agentId != null`) → Agency via agent relationship
2. **Client Users** (`user.clientId != null`) → Agency via client relationship  
3. **Worker Users** (`user.workerId != null`) → Agency via worker relationship
4. **System Users** → Default email configuration (fallback)

## Configuration Requirements

### 1. Eureka Service Discovery
Both services must be registered with Eureka for Feign client communication.

### 2. Agency Email Configuration
Agencies must have active and verified email configuration in worklink-service.

### 3. Fallback Behavior
If agency email configuration is not available:
- System falls back to default email configuration
- No errors thrown, seamless operation
- Logs warning for troubleshooting

## Testing the Integration

### 1. Setup Test Agency
```bash
# Create agency email configuration
curl -X POST "http://localhost:8080/api/v1/agency-email-config/agency/123" \
  -H "Content-Type: application/json" \
  -d '{
    "smtpHost": "smtp.gmail.com",
    "smtpPort": 587,
    "smtpUsername": "<EMAIL>",
    "smtpPassword": "app-password",
    "fromEmail": "<EMAIL>",
    "fromName": "Test Car Rentals",
    "supportEmail": "<EMAIL>",
    "websiteUrl": "https://testcarrentals.com",
    "isActive": true
  }'
```

### 2. Test Password Reset
```bash
# Trigger password reset for user associated with agency
curl -X POST "http://localhost:8081/api/v1/user/resetPassword" \
  -d "email=<EMAIL>"
```

### 3. Test User Registration
```bash
# Create user associated with agency
curl -X POST "http://localhost:8081/api/v1/user/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "agentId": 123
  }'
```

## Monitoring and Troubleshooting

### Log Messages to Watch
```
# Successful agency email configuration
INFO - Sending password reset email for user: <EMAIL>
INFO - Using agency email configuration for agency ID: 123

# Fallback to default
WARN - Failed to get agency email configuration for user 456: Agency not found
INFO - Using default email configuration
```

### Common Issues

1. **Feign Client Connection Issues**
   - Check Eureka registration
   - Verify service names match
   - Check network connectivity

2. **Agency Not Found**
   - Verify user has correct agentId/clientId/workerId
   - Check agency exists in worklink-service
   - Verify relationship tables are populated

3. **Email Configuration Not Active**
   - Check `isActive` and `isVerified` flags
   - Verify SMTP credentials are correct
   - Test email configuration endpoint

## Benefits Achieved

1. **Complete White-Labeling**: Users receive emails with agency branding
2. **Seamless Integration**: No changes needed to existing user flows
3. **Fallback Support**: System continues working if agency config unavailable
4. **Performance Optimized**: Caching prevents repeated API calls
5. **Maintainable**: Clean separation of concerns

## Future Enhancements

1. **Email Template Customization**: Allow agencies to customize email templates
2. **Multi-language Support**: Agency-specific language preferences
3. **Email Analytics**: Track email delivery and engagement per agency
4. **Bulk Configuration**: Import/export agency email configurations
5. **Advanced Fallback**: Hierarchical fallback (agency → parent agency → default)

The user-service now fully supports agency-specific email branding while maintaining backward compatibility and robust error handling.
