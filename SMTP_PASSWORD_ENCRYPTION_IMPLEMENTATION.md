# SMTP Password Encryption Implementation

## Overview

This document describes the implementation of SMTP password encryption in the worklink-service to ensure that sensitive SMTP passwords are stored securely in the database and can be decrypted when needed by other services.

## Security Features

### 🔐 **AES-256-GCM Encryption**
- Uses industry-standard AES-256-GCM encryption algorithm
- Provides both confidentiality and authenticity
- Each encryption uses a random 96-bit IV (Initialization Vector)
- 128-bit authentication tag prevents tampering

### 🔄 **Automatic Encryption/Decryption**
- Passwords are automatically encrypted when saved to database
- Passwords are automatically decrypted when retrieved from database
- Transparent to application code - no manual encryption/decryption needed

### 🛡️ **Security Best Practices**
- Random IV ensures same password encrypts to different values each time
- Base64 encoding for safe database storage
- Encryption key configurable via environment variables
- Passwords never appear in logs (custom toString() methods)

## Implementation Components

### 1. EncryptionService
**Location**: `worklink-service/src/main/java/com/cap10mycap10/worklinkservice/service/EncryptionService.java`

**Key Features**:
- `encrypt(String plaintext)` - Encrypts plaintext to Base64 encoded string
- `decrypt(String encryptedText)` - Decrypts Base64 encoded string to plaintext
- `isEncrypted(String text)` - Checks if text appears to be encrypted
- `generateRandomKey()` - Generates secure random encryption keys

**Configuration**:
```yaml
app:
  encryption:
    key: ${ENCRYPTION_KEY:MySecretEncryptionKey123456789012}
```

### 2. JPA Converter
**Location**: `worklink-service/src/main/java/com/cap10mycap10/worklinkservice/converter/EncryptedStringConverter.java`

**Features**:
- Automatically encrypts data when saving to database
- Automatically decrypts data when loading from database
- Prevents double encryption
- Graceful fallback if encryption service unavailable

### 3. Model Updates
**Location**: `worklink-service/src/main/java/com/cap10mycap10/worklinkservice/model/AgencyEmailConfiguration.java`

**Changes**:
```java
@Column(name = "smtp_password", nullable = false)
@NotBlank
@Convert(converter = EncryptedStringConverter.class)
private String smtpPassword;

public String getDecryptedSmtpPassword() {
    return smtpPassword; // Already decrypted by converter
}
```

### 4. API Endpoint for Other Services
**Location**: `worklink-service/src/main/java/com/cap10mycap10/worklinkservice/controller/AgencyEmailConfigurationController.java`

**New Endpoint**:
```
GET /api/agency-email-config/agency/{agencyId}/active
```

Returns the active email configuration with decrypted password for internal service use.

### 5. Response DTO
**Location**: `worklink-service/src/main/java/com/cap10mycap10/worklinkservice/dto/email/AgencyEmailConfigurationResponseDto.java`

**Features**:
- Includes decrypted password for API responses
- Custom toString() method hides password in logs
- Converts from entity with decrypted password

## Usage Examples

### For Other Services (Feign Clients)

```java
// Call the worklink-service to get email configuration
@FeignClient(name = "worklink-service")
public interface EmailConfigClient {
    @GetMapping("/api/agency-email-config/agency/{agencyId}/active")
    AgencyEmailConfigurationResponseDto getActiveEmailConfig(@PathVariable Long agencyId);
}

// Use the decrypted password
AgencyEmailConfigurationResponseDto config = emailConfigClient.getActiveEmailConfig(agencyId);
String decryptedPassword = config.getSmtpPassword(); // Ready to use for SMTP
```

### For Email Sending

```java
// EmailSenderFactory automatically uses decrypted password
JavaMailSender mailSender = emailSenderFactory.getEmailSender(agencyId);
// Password is automatically decrypted by JPA converter
```

## Database Storage

### Before Encryption
```
smtp_password: "mySecretPassword123"
```

### After Encryption
```
smtp_password: "o8+ID3qcZOHzoGqoEWaZ7aOV9P7mTZqNiA6+yNlU/04ujozFxrDR2NuSa1hMiyBVsS/8og=="
```

## Testing

### Comprehensive Test Suite
- **EncryptionServiceTest**: 14 tests covering all encryption scenarios
- **AgencyEmailConfigurationServiceTest**: Tests with encrypted passwords
- **EmailSenderFactoryTest**: Tests email sender creation with decrypted passwords
- **EncryptionDemo**: Interactive demonstration of encryption features

### Test Results
✅ All encryption-related tests pass  
✅ Existing functionality preserved  
✅ No breaking changes to existing APIs  

## Security Considerations

### Production Deployment
1. **Change the default encryption key**:
   ```bash
   export ENCRYPTION_KEY="your-secure-32-character-key-here"
   ```

2. **Generate a secure key**:
   ```java
   String secureKey = EncryptionService.generateRandomKey();
   ```

3. **Key Management**:
   - Store encryption key securely (environment variables, secrets management)
   - Use different keys for different environments
   - Rotate keys periodically (requires data migration)

### Migration Strategy
1. **Existing Data**: Existing plaintext passwords will be automatically encrypted on first save
2. **Backward Compatibility**: System detects and handles both encrypted and plaintext passwords
3. **Gradual Migration**: No downtime required - encryption happens transparently

## Benefits

### 🔒 **Enhanced Security**
- SMTP passwords encrypted at rest
- Protection against database breaches
- Industry-standard encryption algorithms

### 🔄 **Seamless Integration**
- No changes required to existing code
- Automatic encryption/decryption
- Transparent to other services

### 🛠️ **Developer Friendly**
- Simple API for other services
- Comprehensive test coverage
- Clear documentation and examples

### 📊 **Operational Benefits**
- Passwords hidden in logs
- Easy key rotation capability
- Configurable encryption settings

## Conclusion

The SMTP password encryption implementation provides robust security for sensitive email credentials while maintaining ease of use for developers and seamless integration with existing systems. The solution uses industry-standard encryption, follows security best practices, and includes comprehensive testing to ensure reliability.
