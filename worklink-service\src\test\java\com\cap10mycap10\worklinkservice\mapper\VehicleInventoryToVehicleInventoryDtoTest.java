package com.cap10mycap10.worklinkservice.mapper;

import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleInventoryDto;
import com.cap10mycap10.worklinkservice.dto.tax.TaxRateDto;
import com.cap10mycap10.worklinkservice.mapper.asset.admin.VehicleInventoryToVehicleInventoryDto;
import com.cap10mycap10.worklinkservice.model.TaxRate;
import com.cap10mycap10.worklinkservice.model.Vehicle;
import com.cap10mycap10.worklinkservice.model.VehicleInventory;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class VehicleInventoryToVehicleInventoryDtoTest {

    @InjectMocks
    private VehicleInventoryToVehicleInventoryDto mapper;

    private VehicleInventory vehicleInventory;
    private TaxRate taxRate;
    private Vehicle vehicle;

    @BeforeEach
    void setUp() {
        // Create a TaxRate
        taxRate = new TaxRate();
        taxRate.setId(1L);
        taxRate.setName("Standard VAT");
        taxRate.setPercentage(new BigDecimal("15.00"));
        taxRate.setDescription("Standard VAT rate");
        taxRate.setActive(true);
        taxRate.setAgencyId(100L);

        // Create a Vehicle
        vehicle = new Vehicle();
        vehicle.setId(1L);
        vehicle.setName("Test Vehicle");

        // Create a VehicleInventory
        vehicleInventory = new VehicleInventory();
        vehicleInventory.setId(1L);
        vehicleInventory.setDateInstalled(LocalDate.of(2024, 1, 1));
        vehicleInventory.setNextCheckDate(LocalDate.of(2024, 12, 31));
        vehicleInventory.setName("GPS Navigation");
        vehicleInventory.setDescription("Premium GPS navigation system");
        vehicleInventory.setPhotoUrl1("http://example.com/photo1.jpg");
        vehicleInventory.setPhotoUrl2("http://example.com/photo2.jpg");
        vehicleInventory.setPhotoUrl3("http://example.com/photo3.jpg");
        vehicleInventory.setPhotoUrl4("http://example.com/photo4.jpg");
        vehicleInventory.setPrice(25.0f);
        vehicleInventory.setTaxExempt(false);
        vehicleInventory.setTaxInclusive(true);
        vehicleInventory.setCustomTaxRate(taxRate);
        vehicleInventory.setVehicle(vehicle);
        vehicleInventory.setCreatedDate(Instant.parse("2024-01-01T10:00:00Z"));
        vehicleInventory.setLastModifiedDate(LocalDateTime.of(2024, 1, 2, 15, 30));
        vehicleInventory.setVersion(1L);
    }

    @Test
    void testConvert_Success() {
        // When
        VehicleInventoryDto result = mapper.convert(vehicleInventory);

        // Then
        assertNotNull(result);
        assertEquals(vehicleInventory.getId(), result.getId());
        assertEquals(vehicleInventory.getDateInstalled(), result.getDateInstalled());
        assertEquals(vehicleInventory.getNextCheckDate(), result.getNextCheckDate());
        assertEquals(vehicleInventory.getName(), result.getName());
        assertEquals(vehicleInventory.getDescription(), result.getDescription());
        assertEquals(vehicleInventory.getPhotoUrl1(), result.getPhotoUrl1());
        assertEquals(vehicleInventory.getPhotoUrl2(), result.getPhotoUrl2());
        assertEquals(vehicleInventory.getPhotoUrl3(), result.getPhotoUrl3());
        assertEquals(vehicleInventory.getPhotoUrl4(), result.getPhotoUrl4());
        assertEquals(vehicleInventory.getPrice(), result.getPrice());
        assertEquals(vehicleInventory.getTaxExempt(), result.getTaxExempt());
        assertEquals(vehicleInventory.getTaxInclusive(), result.getTaxInclusive());
        assertEquals(vehicleInventory.getVehicle().getId(), result.getVehicleId());
        assertEquals(vehicleInventory.getCreatedDate(), result.getCreatedDate());
        assertEquals(vehicleInventory.getLastModifiedDate(), result.getLastModifiedDate());
        assertEquals(vehicleInventory.getVersion(), result.getVersion());

        // Verify TaxRate conversion
        assertNotNull(result.getCustomTaxRate());
        TaxRateDto taxRateDto = result.getCustomTaxRate();
        assertEquals(taxRate.getId(), taxRateDto.getId());
        assertEquals(taxRate.getName(), taxRateDto.getName());
        assertEquals(taxRate.getPercentage(), taxRateDto.getPercentage());
        assertEquals(taxRate.getDescription(), taxRateDto.getDescription());
        assertEquals(taxRate.isActive(), taxRateDto.isActive());
        assertEquals(taxRate.getAgencyId(), taxRateDto.getAgencyId());
    }

    @Test
    void testConvert_WithNullTaxRate() {
        // Given
        vehicleInventory.setCustomTaxRate(null);

        // When
        VehicleInventoryDto result = mapper.convert(vehicleInventory);

        // Then
        assertNotNull(result);
        assertNull(result.getCustomTaxRate());
    }

    @Test
    void testConvert_WithNullVehicle() {
        // Given
        vehicleInventory.setVehicle(null);

        // When
        VehicleInventoryDto result = mapper.convert(vehicleInventory);

        // Then
        assertNotNull(result);
        assertNull(result.getVehicleId());
    }

    @Test
    void testConvert_WithNullInput() {
        // When
        VehicleInventoryDto result = mapper.convert(null);

        // Then
        assertNull(result);
    }

    @Test
    void testConvertSet_Success() {
        // Given
        VehicleInventory inventory2 = new VehicleInventory();
        inventory2.setId(2L);
        inventory2.setName("Child Seat");
        inventory2.setPrice(15.0f);
        inventory2.setTaxExempt(true);

        Set<VehicleInventory> inventorySet = new HashSet<>();
        inventorySet.add(vehicleInventory);
        inventorySet.add(inventory2);

        // When
        Set<VehicleInventoryDto> result = mapper.convertSet(inventorySet);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
    }

    @Test
    void testConvertSet_WithNullInput() {
        // When
        Set<VehicleInventoryDto> result = mapper.convertSet(null);

        // Then
        assertNull(result);
    }

    @Test
    void testJsonSerialization_NoHibernateProxyIssues() throws Exception {
        // Given
        VehicleInventoryDto dto = mapper.convert(vehicleInventory);
        ObjectMapper objectMapper = new ObjectMapper();

        // When - This should not throw any Hibernate proxy serialization exceptions
        String json = objectMapper.writeValueAsString(dto);

        // Then
        assertNotNull(json);
        assertTrue(json.contains("GPS Navigation"));
        assertTrue(json.contains("Standard VAT"));
        // Verify that the JSON contains the tax rate information without Hibernate proxy issues
        assertTrue(json.contains("\"percentage\":15.00"));
    }
}
