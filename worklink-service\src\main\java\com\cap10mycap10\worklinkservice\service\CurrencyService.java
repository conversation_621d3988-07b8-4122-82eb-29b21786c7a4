package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.model.Currency;
import com.cap10mycap10.worklinkservice.repository.CurrencyRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class CurrencyService {

    private final CurrencyRepository currencyRepository;

    /**
     * Get all active currencies
     */
    public List<Currency> getActiveCurrencies() {
        return currencyRepository.findByActiveTrue();
    }

    /**
     * Get all currencies
     */
    public List<Currency> getAllCurrencies() {
        return currencyRepository.findAll();
    }

    /**
     * Get currency by code
     */
    public Optional<Currency> getCurrencyByCode(String code) {
        return currencyRepository.findByCodeIgnoreCase(code);
    }

    /**
     * Check if currency exists and is active
     */
    public boolean isCurrencyActive(String code) {
        return currencyRepository.existsByCodeAndActiveTrue(code);
    }

    /**
     * Save or update currency
     */
    public Currency saveCurrency(Currency currency) {
        return currencyRepository.save(currency);
    }

    /**
     * Create a new currency
     */
    public Currency createCurrency(String code, String name, String symbol) {
        Currency currency = new Currency(code.toUpperCase(), name, symbol);
        return currencyRepository.save(currency);
    }

    /**
     * Activate or deactivate a currency
     */
    public Currency updateCurrencyStatus(String code, boolean active) {
        Optional<Currency> currencyOpt = getCurrencyByCode(code);
        if (currencyOpt.isPresent()) {
            Currency currency = currencyOpt.get();
            currency.setActive(active);
            return currencyRepository.save(currency);
        }
        throw new RuntimeException("Currency not found: " + code);
    }

    /**
     * Initialize default currencies if they don't exist
     */
    public void initializeDefaultCurrencies() {
        log.info("Initializing default currencies...");
        
        // Check if currencies already exist
        if (currencyRepository.count() > 0) {
            log.info("Currencies already exist, skipping initialization");
            return;
        }

        // Create default currencies
        Currency[] defaultCurrencies = {
            new Currency("USD", "US Dollar", "$"),
            new Currency("EUR", "Euro", "€"),
            new Currency("GBP", "British Pound", "£"),
            new Currency("CAD", "Canadian Dollar", "C$"),
            new Currency("AUD", "Australian Dollar", "A$"),
            new Currency("JPY", "Japanese Yen", "¥"),
            new Currency("CHF", "Swiss Franc", "CHF"),
            new Currency("CNY", "Chinese Yuan", "¥"),
            new Currency("SEK", "Swedish Krona", "kr"),
            new Currency("NOK", "Norwegian Krone", "kr"),
            new Currency("DKK", "Danish Krone", "kr"),
            new Currency("PLN", "Polish Zloty", "zł"),
            new Currency("CZK", "Czech Koruna", "Kč"),
            new Currency("HUF", "Hungarian Forint", "Ft"),
            new Currency("RON", "Romanian Leu", "lei"),
            new Currency("BGN", "Bulgarian Lev", "лв"),
            new Currency("HRK", "Croatian Kuna", "kn"),
            new Currency("RUB", "Russian Ruble", "₽"),
            new Currency("TRY", "Turkish Lira", "₺"),
            new Currency("BRL", "Brazilian Real", "R$"),
            new Currency("MXN", "Mexican Peso", "$"),
            new Currency("INR", "Indian Rupee", "₹"),
            new Currency("KRW", "South Korean Won", "₩"),
            new Currency("SGD", "Singapore Dollar", "S$"),
            new Currency("HKD", "Hong Kong Dollar", "HK$"),
            new Currency("NZD", "New Zealand Dollar", "NZ$"),
            new Currency("ZAR", "South African Rand", "R"),
            new Currency("THB", "Thai Baht", "฿"),
            new Currency("MYR", "Malaysian Ringgit", "RM"),
            new Currency("PHP", "Philippine Peso", "₱"),
            new Currency("IDR", "Indonesian Rupiah", "Rp"),
            new Currency("VND", "Vietnamese Dong", "₫")
        };

        // Set decimal places for currencies that don't use 2 decimal places
        for (Currency currency : defaultCurrencies) {
            if ("JPY".equals(currency.getCode()) || "KRW".equals(currency.getCode()) || 
                "HUF".equals(currency.getCode()) || "IDR".equals(currency.getCode()) || 
                "VND".equals(currency.getCode())) {
                currency.setDecimalPlaces(0);
            }
        }

        currencyRepository.saveAll(List.of(defaultCurrencies));
        log.info("Initialized {} default currencies", defaultCurrencies.length);
    }
}
