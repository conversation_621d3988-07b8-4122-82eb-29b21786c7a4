package com.cap10mycap10.worklinkservice.controller;

import com.cap10mycap10.worklinkservice.model.ExchangeRate;
import com.cap10mycap10.worklinkservice.service.ExchangeRateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v1/exchange-rates")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class ExchangeRateController {

    private final ExchangeRateService exchangeRateService;

    /**
     * Get exchange rate between two currencies
     */
    @GetMapping("/{fromCurrency}/{toCurrency}")
    public ResponseEntity<ExchangeRate> getExchangeRate(
            @PathVariable String fromCurrency,
            @PathVariable String toCurrency) {
        try {
            ExchangeRate rate = exchangeRateService.getExchangeRateWithFallback(
                fromCurrency.toUpperCase(), 
                toCurrency.toUpperCase()
            );
            return ResponseEntity.ok(rate);
        } catch (Exception e) {
            log.error("Failed to get exchange rate for {} to {}: {}", 
                     fromCurrency, toCurrency, e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Get multiple exchange rates for a base currency
     */
    @PostMapping("/batch")
    public ResponseEntity<List<ExchangeRate>> getBatchExchangeRates(
            @RequestBody BatchExchangeRateRequest request) {
        try {
            // For now, get rates synchronously. In production, you might want to use the async method
            List<ExchangeRate> rates = request.getTargetCurrencies().stream()
                .map(targetCurrency -> {
                    try {
                        return exchangeRateService.getExchangeRateWithFallback(
                            request.getBaseCurrency().toUpperCase(),
                            targetCurrency.toUpperCase()
                        );
                    } catch (Exception e) {
                        log.warn("Failed to get rate for {} to {}: {}", 
                                request.getBaseCurrency(), targetCurrency, e.getMessage());
                        return null;
                    }
                })
                .filter(rate -> rate != null)
                    .collect(Collectors.toList());
            
            return ResponseEntity.ok(rates);
        } catch (Exception e) {
            log.error("Failed to get batch exchange rates: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Convert currency amount
     */
    @PostMapping("/convert")
    public ResponseEntity<Map<String, Object>> convertCurrency(
            @RequestBody ConvertCurrencyRequest request) {
        try {
            Map<String, Object> conversionDetails = exchangeRateService.getConversionDetails(
                request.getAmount(),
                request.getFromCurrency().toUpperCase(),
                request.getToCurrency().toUpperCase()
            );
            return ResponseEntity.ok(conversionDetails);
        } catch (Exception e) {
            log.error("Failed to convert currency: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Refresh exchange rates from Stripe
     */
    @PostMapping("/refresh")
    public ResponseEntity<String> refreshExchangeRates() {
        try {
            exchangeRateService.refreshExchangeRates();
            return ResponseEntity.ok("Exchange rates refresh initiated");
        } catch (Exception e) {
            log.error("Failed to refresh exchange rates: {}", e.getMessage());
            return ResponseEntity.badRequest().body("Failed to refresh rates: " + e.getMessage());
        }
    }

    /**
     * Get latest exchange rate (without fallback to Stripe)
     */
    @GetMapping("/latest/{fromCurrency}/{toCurrency}")
    public ResponseEntity<ExchangeRate> getLatestExchangeRate(
            @PathVariable String fromCurrency,
            @PathVariable String toCurrency) {
        Optional<ExchangeRate> rate = exchangeRateService.getLatestExchangeRate(
            fromCurrency.toUpperCase(), 
            toCurrency.toUpperCase()
        );
        return rate.map(ResponseEntity::ok)
                  .orElse(ResponseEntity.notFound().build());
    }

    // Request DTOs
    public static class BatchExchangeRateRequest {
        private String baseCurrency;
        private List<String> targetCurrencies;

        // Getters and setters
        public String getBaseCurrency() { return baseCurrency; }
        public void setBaseCurrency(String baseCurrency) { this.baseCurrency = baseCurrency; }
        public List<String> getTargetCurrencies() { return targetCurrencies; }
        public void setTargetCurrencies(List<String> targetCurrencies) { this.targetCurrencies = targetCurrencies; }
    }

    public static class ConvertCurrencyRequest {
        private BigDecimal amount;
        private String fromCurrency;
        private String toCurrency;

        // Getters and setters
        public BigDecimal getAmount() { return amount; }
        public void setAmount(BigDecimal amount) { this.amount = amount; }
        public String getFromCurrency() { return fromCurrency; }
        public void setFromCurrency(String fromCurrency) { this.fromCurrency = fromCurrency; }
        public String getToCurrency() { return toCurrency; }
        public void setToCurrency(String toCurrency) { this.toCurrency = toCurrency; }
    }
}
