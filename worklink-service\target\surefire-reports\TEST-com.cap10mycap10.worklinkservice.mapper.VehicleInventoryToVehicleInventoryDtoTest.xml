<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="com.cap10mycap10.worklinkservice.mapper.VehicleInventoryToVehicleInventoryDtoTest" time="0.028" tests="7" errors="0" skipped="0" failures="1">
  <properties>
    <property name="sun.desktop" value="windows"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="java.specification.version" value="11"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="Cp1252"/>
    <property name="java.class.path" value="E:\Local Documents\GitHub\worklink-backend\worklink-service\target\test-classes;E:\Local Documents\GitHub\worklink-backend\worklink-service\target\classes;C:\Users\<USER>\.m2\repository\com\google\maps\google-maps-services\2.2.0\google-maps-services-2.2.0.jar;C:\Users\<USER>\.m2\repository\io\opencensus\opencensus-api\0.31.0\opencensus-api-0.31.0.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.30\slf4j-api-1.7.30.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp\3.14.9\okhttp-3.14.9.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio\1.17.2\okio-1.17.2.jar;C:\Users\<USER>\.m2\repository\net\sf\jasperreports\jasperreports\6.18.1\jasperreports-6.18.1.jar;C:\Users\<USER>\.m2\repository\commons-beanutils\commons-beanutils\1.9.4\commons-beanutils-1.9.4.jar;C:\Users\<USER>\.m2\repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;C:\Users\<USER>\.m2\repository\commons-digester\commons-digester\2.1\commons-digester-2.1.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.1.1\commons-logging-1.1.1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-collections4\4.2\commons-collections4-4.2.jar;C:\Users\<USER>\.m2\repository\com\lowagie\itext\2.1.7.js9\itext-2.1.7.js9.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk15on\1.68\bcprov-jdk15on-1.68.jar;C:\Users\<USER>\.m2\repository\org\jfree\jcommon\1.0.23\jcommon-1.0.23.jar;C:\Users\<USER>\.m2\repository\org\jfree\jfreechart\1.0.19\jfreechart-1.0.19.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jdt\ecj\3.21.0\ecj-3.21.0.jar;C:\Users\<USER>\.m2\repository\org\codehaus\castor\castor-xml\1.4.1\castor-xml-1.4.1.jar;C:\Users\<USER>\.m2\repository\org\codehaus\castor\castor-core\1.4.1\castor-core-1.4.1.jar;C:\Users\<USER>\.m2\repository\javax\inject\javax.inject\1\javax.inject-1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.11.4\jackson-core-2.11.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-properties-migrator\2.3.10.RELEASE\spring-boot-properties-migrator-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.3.10.RELEASE\spring-boot-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.2.14.RELEASE\spring-context-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-configuration-metadata\2.3.10.RELEASE\spring-boot-configuration-metadata-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\zw\co\paynow\java-sdk\1.1.0\java-sdk-1.1.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\29.0-jre\guava-29.0-jre.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\2.11.1\checker-qual-2.11.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\2.3.10.RELEASE\spring-boot-starter-validation-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.3.10.RELEASE\spring-boot-starter-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.3.10.RELEASE\spring-boot-autoconfigure-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.3.10.RELEASE\spring-boot-starter-logging-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.3\logback-classic-1.2.3.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.3\logback-core-1.2.3.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.13.3\log4j-to-slf4j-2.13.3.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.13.3\log4j-api-2.13.3.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.30\jul-to-slf4j-1.7.30.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.26\snakeyaml-1.26.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jakarta.el\3.0.3\jakarta.el-3.0.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.1.7.Final\hibernate-validator-6.1.7.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.4.1.Final\jboss-logging-3.4.1.Final.jar;C:\Users\<USER>\.m2\repository\net\sf\jasperreports\jasperreports-fonts\6.18.1\jasperreports-fonts-6.18.1.jar;C:\Users\<USER>\.m2\repository\org\modelmapper\modelmapper\2.3.9\modelmapper-2.3.9.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\2.3.10.RELEASE\spring-boot-starter-security-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.2.14.RELEASE\spring-aop-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.3.9.RELEASE\spring-security-config-5.3.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.3.9.RELEASE\spring-security-core-5.3.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.3.9.RELEASE\spring-security-web-5.3.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.2.14.RELEASE\spring-expression-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-oauth2\2.2.4.RELEASE\spring-cloud-starter-oauth2-2.2.4.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-security\2.2.4.RELEASE\spring-cloud-starter-security-2.2.4.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\2.3.10.RELEASE\spring-boot-starter-actuator-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\2.3.10.RELEASE\spring-boot-actuator-autoconfigure-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\2.3.10.RELEASE\spring-boot-actuator-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.5.13\micrometer-core-1.5.13.jar;C:\Users\<USER>\.m2\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-security\2.2.4.RELEASE\spring-cloud-security-2.2.4.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\oauth\boot\spring-security-oauth2-autoconfigure\2.1.2.RELEASE\spring-security-oauth2-autoconfigure-2.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\sun\xml\bind\jaxb-core\2.3.0.1\jaxb-core-2.3.0.1.jar;C:\Users\<USER>\.m2\repository\com\sun\xml\bind\jaxb-impl\2.3.0.1\jaxb-impl-2.3.0.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\oauth\spring-security-oauth2\2.3.4.RELEASE\spring-security-oauth2-2.3.4.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jackson\jackson-mapper-asl\1.9.13\jackson-mapper-asl-1.9.13.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jackson\jackson-core-asl\1.9.13\jackson-core-asl-1.9.13.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-jwt\1.0.9.RELEASE\spring-security-jwt-1.0.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcpkix-jdk15on\1.56\bcpkix-jdk15on-1.56.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-netflix-eureka-client\2.2.6.RELEASE\spring-cloud-starter-netflix-eureka-client-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter\2.2.6.RELEASE\spring-cloud-starter-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-context\2.2.6.RELEASE\spring-cloud-context-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-rsa\1.0.9.RELEASE\spring-security-rsa-1.0.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-netflix-hystrix\2.2.6.RELEASE\spring-cloud-netflix-hystrix-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-netflix-eureka-client\2.2.6.RELEASE\spring-cloud-netflix-eureka-client-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\netflix\eureka\eureka-client\1.10.7\eureka-client-1.10.7.jar;C:\Users\<USER>\.m2\repository\com\netflix\netflix-commons\netflix-eventbus\0.3.0\netflix-eventbus-0.3.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\netflix-commons\netflix-infix\0.3.0\netflix-infix-0.3.0.jar;C:\Users\<USER>\.m2\repository\commons-jxpath\commons-jxpath\1.3\commons-jxpath-1.3.jar;C:\Users\<USER>\.m2\repository\joda-time\joda-time\2.3\joda-time-2.3.jar;C:\Users\<USER>\.m2\repository\org\antlr\antlr-runtime\3.4\antlr-runtime-3.4.jar;C:\Users\<USER>\.m2\repository\org\antlr\stringtemplate\3.2.1\stringtemplate-3.2.1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-math\2.2\commons-math-2.2.jar;C:\Users\<USER>\.m2\repository\com\netflix\archaius\archaius-core\0.7.6\archaius-core-0.7.6.jar;C:\Users\<USER>\.m2\repository\javax\ws\rs\jsr311-api\1.1.1\jsr311-api-1.1.1.jar;C:\Users\<USER>\.m2\repository\com\netflix\servo\servo-core\0.12.21\servo-core-0.12.21.jar;C:\Users\<USER>\.m2\repository\com\sun\jersey\jersey-core\1.19.1\jersey-core-1.19.1.jar;C:\Users\<USER>\.m2\repository\com\sun\jersey\jersey-client\1.19.1\jersey-client-1.19.1.jar;C:\Users\<USER>\.m2\repository\com\sun\jersey\contribs\jersey-apache-client4\1.19.1\jersey-apache-client4-1.19.1.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;C:\Users\<USER>\.m2\repository\commons-configuration\commons-configuration\1.10\commons-configuration-1.10.jar;C:\Users\<USER>\.m2\repository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;C:\Users\<USER>\.m2\repository\com\google\inject\guice\4.1.0\guice-4.1.0.jar;C:\Users\<USER>\.m2\repository\aopalliance\aopalliance\1.0\aopalliance-1.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\eureka\eureka-core\1.10.7\eureka-core-1.10.7.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\woodstox\woodstox-core\5.3.0\woodstox-core-5.3.0.jar;C:\Users\<USER>\.m2\repository\org\codehaus\woodstox\stax2-api\4.2\stax2-api-4.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-netflix-archaius\2.2.6.RELEASE\spring-cloud-starter-netflix-archaius-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-netflix-ribbon\2.2.6.RELEASE\spring-cloud-starter-netflix-ribbon-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon\2.3.0\ribbon-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon-transport\2.3.0\ribbon-transport-2.3.0.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxnetty-contexts\0.4.9\rxnetty-contexts-0.4.9.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxnetty-servo\0.4.9\rxnetty-servo-0.4.9.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxnetty\0.4.9\rxnetty-0.4.9.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon-core\2.3.0\ribbon-core-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon-httpclient\2.3.0\ribbon-httpclient-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\netflix-commons\netflix-commons-util\0.3.0\netflix-commons-util-0.3.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon-loadbalancer\2.3.0\ribbon-loadbalancer-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\netflix-commons\netflix-statistics\0.1.1\netflix-statistics-0.1.1.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxjava\1.3.8\rxjava-1.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-loadbalancer\2.2.6.RELEASE\spring-cloud-starter-loadbalancer-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-loadbalancer\2.2.6.RELEASE\spring-cloud-loadbalancer-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.3.16.RELEASE\reactor-core-3.3.16.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.3\reactive-streams-1.0.3.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\addons\reactor-extra\3.3.6.RELEASE\reactor-extra-3.3.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-cache\2.3.10.RELEASE\spring-boot-starter-cache-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\stoyanr\evictor\1.0.0\evictor-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon-eureka\2.3.0\ribbon-eureka-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\thoughtworks\xstream\xstream\1.4.13\xstream-1.4.13.jar;C:\Users\<USER>\.m2\repository\xmlpull\xmlpull\1.1.3.1\xmlpull-1.1.3.1.jar;C:\Users\<USER>\.m2\repository\xpp3\xpp3_min\1.1.4c\xpp3_min-1.1.4c.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-openfeign\2.2.6.RELEASE\spring-cloud-starter-openfeign-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-openfeign-core\2.2.6.RELEASE\spring-cloud-openfeign-core-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\form\feign-form-spring\3.8.0\feign-form-spring-3.8.0.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\form\feign-form\3.8.0\feign-form-3.8.0.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.2\commons-io-2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-commons\2.2.6.RELEASE\spring-cloud-commons-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.3.9.RELEASE\spring-security-crypto-5.3.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-core\10.10.1\feign-core-10.10.1.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-slf4j\10.10.1\feign-slf4j-10.10.1.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-hystrix\10.10.1\feign-hystrix-10.10.1.jar;C:\Users\<USER>\.m2\repository\com\netflix\hystrix\hystrix-core\1.5.18\hystrix-core-1.5.18.jar;C:\Users\<USER>\.m2\repository\org\hdrhistogram\HdrHistogram\2.1.9\HdrHistogram-2.1.9.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-mail\2.3.4.RELEASE\spring-boot-starter-mail-2.3.4.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\5.2.14.RELEASE\spring-context-support-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\sun\mail\jakarta.mail\1.6.7\jakarta.mail-1.6.7.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\com\stripe\stripe-java\26.5.1\stripe-java-26.5.1.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.9.1\gson-2.9.1.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger2\2.9.2\springfox-swagger2-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-annotations\1.5.20\swagger-annotations-1.5.20.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-models\1.5.20\swagger-models-1.5.20.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spi\2.9.2\springfox-spi-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-core\2.9.2\springfox-core-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-schema\2.9.2\springfox-schema-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-common\2.9.2\springfox-swagger-common-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-web\2.9.2\springfox-spring-web-2.9.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\1.2.0.RELEASE\spring-plugin-core-1.2.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-metadata\1.2.0.RELEASE\spring-plugin-metadata-1.2.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.2.0.Final\mapstruct-1.2.0.Final.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-ui\2.9.2\springfox-swagger-ui-2.9.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.11.2\jackson-annotations-2.11.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.11.2\jackson-databind-2.11.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-netflix-ribbon\2.2.1.RELEASE\spring-cloud-netflix-ribbon-2.2.1.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-netflix-archaius\2.2.6.RELEASE\spring-cloud-netflix-archaius-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\2.3.10.RELEASE\spring-boot-starter-data-jpa-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.3.10.RELEASE\spring-boot-starter-aop-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.6\aspectjweaver-1.9.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.3.10.RELEASE\spring-boot-starter-jdbc-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.2.14.RELEASE\spring-jdbc-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-core\5.4.30.Final\hibernate-core-5.4.30.Final.jar;C:\Users\<USER>\.m2\repository\org\javassist\javassist\3.27.0-GA\javassist-3.27.0-GA.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.10.22\byte-buddy-1.10.22.jar;C:\Users\<USER>\.m2\repository\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\jboss\jandex\2.2.3.Final\jandex-2.2.3.Final.jar;C:\Users\<USER>\.m2\repository\org\dom4j\dom4j\2.1.3\dom4j-2.1.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\5.1.2.Final\hibernate-commons-annotations-5.1.2.Final.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.4\jaxb-runtime-2.3.4.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.4\txw2-2.3.4.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\2.3.9.RELEASE\spring-data-jpa-2.3.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.3.9.RELEASE\spring-data-commons-2.3.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\5.2.14.RELEASE\spring-orm-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.2.14.RELEASE\spring-tx-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\5.2.14.RELEASE\spring-aspects-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.3.10.RELEASE\spring-boot-starter-web-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.3.10.RELEASE\spring-boot-starter-json-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.11.4\jackson-datatype-jdk8-2.11.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.11.4\jackson-datatype-jsr310-2.11.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.11.4\jackson-module-parameter-names-2.11.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.3.10.RELEASE\spring-boot-starter-tomcat-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.45\tomcat-embed-core-9.0.45.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.45\tomcat-embed-websocket-9.0.45.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.2.14.RELEASE\spring-webmvc-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\mariadb\jdbc\mariadb-java-client\2.7.0\mariadb-java-client-2.7.0.jar;C:\Users\<USER>\.m2\repository\com\opencsv\opencsv\4.1\opencsv-4.1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.10\commons-lang3-3.10.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-text\1.1\commons-text-1.1.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar;C:\Users\<USER>\.m2\repository\net\logstash\logback\logstash-logback-encoder\6.4\logstash-logback-encoder-6.4.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.20\lombok-1.18.20.jar;C:\Users\<USER>\.m2\repository\com\google\firebase\firebase-admin\8.1.0\firebase-admin-8.1.0.jar;C:\Users\<USER>\.m2\repository\com\google\api-client\google-api-client\1.30.7\google-api-client-1.30.7.jar;C:\Users\<USER>\.m2\repository\com\google\api-client\google-api-client-gson\1.32.1\google-api-client-gson-1.32.1.jar;C:\Users\<USER>\.m2\repository\com\google\http-client\google-http-client-gson\1.37.0\google-http-client-gson-1.37.0.jar;C:\Users\<USER>\.m2\repository\com\google\http-client\google-http-client\1.37.0\google-http-client-1.37.0.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.14\httpcore-4.4.14.jar;C:\Users\<USER>\.m2\repository\com\google\api\api-common\1.10.0\api-common-1.10.0.jar;C:\Users\<USER>\.m2\repository\com\google\auth\google-auth-library-oauth2-http\0.22.0\google-auth-library-oauth2-http-0.22.0.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\google-cloud-firestore\2.1.0\google-cloud-firestore-2.1.0.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\google-cloud-core-grpc\1.93.9\google-cloud-core-grpc-1.93.9.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-core\1.33.0\grpc-core-1.33.0.jar;C:\Users\<USER>\.m2\repository\com\google\android\annotations\4.1.1.4\annotations-4.1.1.4.jar;C:\Users\<USER>\.m2\repository\io\perfmark\perfmark-api\0.19.0\perfmark-api-0.19.0.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.14\commons-codec-1.14.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-cloud-firestore-v1\2.1.0\proto-google-cloud-firestore-v1-2.1.0.jar;C:\Users\<USER>\.m2\repository\io\opencensus\opencensus-contrib-grpc-util\0.24.0\opencensus-contrib-grpc-util-0.24.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-protobuf\1.33.0\grpc-protobuf-1.33.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-protobuf-lite\1.47.0\grpc-protobuf-lite-1.47.0.jar;C:\Users\<USER>\.m2\repository\org\codehaus\mojo\animal-sniffer-annotations\1.19\animal-sniffer-annotations-1.19.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-api\1.33.0\grpc-api-1.33.0.jar;C:\Users\<USER>\.m2\repository\com\google\api\gax-grpc\1.60.0\gax-grpc-1.60.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-auth\1.33.0\grpc-auth-1.33.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-netty-shaded\1.33.0\grpc-netty-shaded-1.33.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-alts\1.33.0\grpc-alts-1.33.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-grpclb\1.33.0\grpc-grpclb-1.33.0.jar;C:\Users\<USER>\.m2\repository\org\conscrypt\conscrypt-openjdk-uber\2.2.1\conscrypt-openjdk-uber-2.2.1.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-stub\1.33.0\grpc-stub-1.33.0.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.63.Final\netty-codec-http-4.1.63.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.63.Final\netty-common-4.1.63.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.63.Final\netty-buffer-4.1.63.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.63.Final\netty-codec-4.1.63.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.63.Final\netty-handler-4.1.63.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.63.Final\netty-resolver-4.1.63.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.63.Final\netty-transport-4.1.63.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\2.3.10.RELEASE\spring-boot-starter-test-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.3.10.RELEASE\spring-boot-test-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.3.10.RELEASE\spring-boot-test-autoconfigure-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.4.0\json-path-2.4.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.3\json-smart-2.3.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\1.2\accessors-smart-1.2.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\5.0.4\asm-5.0.4.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.16.1\assertj-core-3.16.1.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.6.3\junit-jupiter-5.6.3.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.6.3\junit-jupiter-api-5.6.3.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.6.3\junit-platform-commons-1.6.3.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.6.3\junit-jupiter-params-5.6.3.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.6.3\junit-jupiter-engine-5.6.3.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.6.3\junit-platform-engine-1.6.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\3.3.3\mockito-core-3.3.3.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.10.22\byte-buddy-agent-1.10.22.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\2.6\objenesis-2.6.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\3.3.3\mockito-junit-jupiter-3.3.3.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.2.14.RELEASE\spring-core-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.2.14.RELEASE\spring-jcl-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\5.2.14.RELEASE\spring-test-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.7.0\xmlunit-core-2.7.0.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;C:\Users\<USER>\.m2\repository\org\zalando\problem-spring-web\0.27.0\problem-spring-web-0.27.0.jar;C:\Users\<USER>\.m2\repository\org\zalando\problem-violations\0.27.0\problem-violations-0.27.0.jar;C:\Users\<USER>\.m2\repository\org\zalando\problem-spring-common\0.27.0\problem-spring-common-0.27.0.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\zalando\problem\0.26.0\problem-0.26.0.jar;C:\Users\<USER>\.m2\repository\org\zalando\jackson-datatype-problem\0.26.0\jackson-datatype-problem-0.26.0.jar;C:\Users\<USER>\.m2\repository\org\zalando\faux-pas\0.9.0\faux-pas-0.9.0.jar;C:\Users\<USER>\.m2\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\google-cloud-storage\1.113.1\google-cloud-storage-1.113.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-compat-qual\2.5.5\checker-compat-qual-2.5.5.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.3.4\error_prone_annotations-2.3.4.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\io\opencensus\opencensus-contrib-http-util\0.24.0\opencensus-contrib-http-util-0.24.0.jar;C:\Users\<USER>\.m2\repository\com\google\http-client\google-http-client-jackson2\1.37.0\google-http-client-jackson2-1.37.0.jar;C:\Users\<USER>\.m2\repository\com\google\oauth-client\google-oauth-client\1.31.0\google-oauth-client-1.31.0.jar;C:\Users\<USER>\.m2\repository\com\google\apis\google-api-services-storage\v1-rev20200814-1.30.10\google-api-services-storage-v1-rev20200814-1.30.10.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\google-cloud-core\1.93.9\google-cloud-core-1.93.9.jar;C:\Users\<USER>\.m2\repository\com\google\auto\value\auto-value-annotations\1.7.2\auto-value-annotations-1.7.2.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-common-protos\1.18.1\proto-google-common-protos-1.18.1.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\google-cloud-core-http\1.93.9\google-cloud-core-http-1.93.9.jar;C:\Users\<USER>\.m2\repository\com\google\http-client\google-http-client-appengine\1.37.0\google-http-client-appengine-1.37.0.jar;C:\Users\<USER>\.m2\repository\com\google\api\gax-httpjson\0.77.0\gax-httpjson-0.77.0.jar;C:\Users\<USER>\.m2\repository\com\google\api\gax\1.60.0\gax-1.60.0.jar;C:\Users\<USER>\.m2\repository\com\google\auth\google-auth-library-credentials\0.22.0\google-auth-library-credentials-0.22.0.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-context\1.33.0\grpc-context-1.33.0.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-iam-v1\1.0.1\proto-google-iam-v1-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java\3.13.0\protobuf-java-3.13.0.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java-util\3.13.0\protobuf-java-util-3.13.0.jar;C:\Users\<USER>\.m2\repository\org\threeten\threetenbp\1.4.4\threetenbp-1.4.4.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-search-orm\5.11.10.Final\hibernate-search-orm-5.11.10.Final.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-search-engine\5.11.10.Final\hibernate-search-engine-5.11.10.Final.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-core\5.5.5\lucene-core-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-misc\5.5.5\lucene-misc-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-analyzers-common\5.5.5\lucene-analyzers-common-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-facet\5.5.5\lucene-facet-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-queries\5.5.5\lucene-queries-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-queryparser\5.5.5\lucene-queryparser-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.2.14.RELEASE\spring-web-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.2.14.RELEASE\spring-beans-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jettison\jettison\1.4.0\jettison-1.4.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\23.0.0\annotations-23.0.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-websocket\2.3.10.RELEASE\spring-boot-starter-websocket-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-messaging\5.2.14.RELEASE\spring-messaging-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-websocket\5.2.14.RELEASE\spring-websocket-5.2.14.RELEASE.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://openjdk.java.net/"/>
    <property name="user.timezone" value="Africa/Johannesburg"/>
    <property name="java.vm.specification.version" value="11"/>
    <property name="os.name" value="Windows 11"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="US"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\jdk-11\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire10990347065882896941\surefirebooter10381545004774943267.jar C:\Users\<USER>\AppData\Local\Temp\surefire10990347065882896941 2025-08-04T16-07-02_077-jvmRun1 surefire7126897961465187424tmp surefire_011497919392660234871tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="E:\Local Documents\GitHub\worklink-backend\worklink-service\target\test-classes;E:\Local Documents\GitHub\worklink-backend\worklink-service\target\classes;C:\Users\<USER>\.m2\repository\com\google\maps\google-maps-services\2.2.0\google-maps-services-2.2.0.jar;C:\Users\<USER>\.m2\repository\io\opencensus\opencensus-api\0.31.0\opencensus-api-0.31.0.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.30\slf4j-api-1.7.30.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp\3.14.9\okhttp-3.14.9.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio\1.17.2\okio-1.17.2.jar;C:\Users\<USER>\.m2\repository\net\sf\jasperreports\jasperreports\6.18.1\jasperreports-6.18.1.jar;C:\Users\<USER>\.m2\repository\commons-beanutils\commons-beanutils\1.9.4\commons-beanutils-1.9.4.jar;C:\Users\<USER>\.m2\repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;C:\Users\<USER>\.m2\repository\commons-digester\commons-digester\2.1\commons-digester-2.1.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.1.1\commons-logging-1.1.1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-collections4\4.2\commons-collections4-4.2.jar;C:\Users\<USER>\.m2\repository\com\lowagie\itext\2.1.7.js9\itext-2.1.7.js9.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk15on\1.68\bcprov-jdk15on-1.68.jar;C:\Users\<USER>\.m2\repository\org\jfree\jcommon\1.0.23\jcommon-1.0.23.jar;C:\Users\<USER>\.m2\repository\org\jfree\jfreechart\1.0.19\jfreechart-1.0.19.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jdt\ecj\3.21.0\ecj-3.21.0.jar;C:\Users\<USER>\.m2\repository\org\codehaus\castor\castor-xml\1.4.1\castor-xml-1.4.1.jar;C:\Users\<USER>\.m2\repository\org\codehaus\castor\castor-core\1.4.1\castor-core-1.4.1.jar;C:\Users\<USER>\.m2\repository\javax\inject\javax.inject\1\javax.inject-1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.11.4\jackson-core-2.11.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-properties-migrator\2.3.10.RELEASE\spring-boot-properties-migrator-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.3.10.RELEASE\spring-boot-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.2.14.RELEASE\spring-context-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-configuration-metadata\2.3.10.RELEASE\spring-boot-configuration-metadata-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\zw\co\paynow\java-sdk\1.1.0\java-sdk-1.1.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\29.0-jre\guava-29.0-jre.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\2.11.1\checker-qual-2.11.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\2.3.10.RELEASE\spring-boot-starter-validation-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.3.10.RELEASE\spring-boot-starter-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.3.10.RELEASE\spring-boot-autoconfigure-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.3.10.RELEASE\spring-boot-starter-logging-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.3\logback-classic-1.2.3.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.3\logback-core-1.2.3.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.13.3\log4j-to-slf4j-2.13.3.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.13.3\log4j-api-2.13.3.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.30\jul-to-slf4j-1.7.30.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.26\snakeyaml-1.26.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jakarta.el\3.0.3\jakarta.el-3.0.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.1.7.Final\hibernate-validator-6.1.7.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.4.1.Final\jboss-logging-3.4.1.Final.jar;C:\Users\<USER>\.m2\repository\net\sf\jasperreports\jasperreports-fonts\6.18.1\jasperreports-fonts-6.18.1.jar;C:\Users\<USER>\.m2\repository\org\modelmapper\modelmapper\2.3.9\modelmapper-2.3.9.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\2.3.10.RELEASE\spring-boot-starter-security-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.2.14.RELEASE\spring-aop-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.3.9.RELEASE\spring-security-config-5.3.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.3.9.RELEASE\spring-security-core-5.3.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.3.9.RELEASE\spring-security-web-5.3.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.2.14.RELEASE\spring-expression-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-oauth2\2.2.4.RELEASE\spring-cloud-starter-oauth2-2.2.4.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-security\2.2.4.RELEASE\spring-cloud-starter-security-2.2.4.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\2.3.10.RELEASE\spring-boot-starter-actuator-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\2.3.10.RELEASE\spring-boot-actuator-autoconfigure-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\2.3.10.RELEASE\spring-boot-actuator-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.5.13\micrometer-core-1.5.13.jar;C:\Users\<USER>\.m2\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-security\2.2.4.RELEASE\spring-cloud-security-2.2.4.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\oauth\boot\spring-security-oauth2-autoconfigure\2.1.2.RELEASE\spring-security-oauth2-autoconfigure-2.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\sun\xml\bind\jaxb-core\2.3.0.1\jaxb-core-2.3.0.1.jar;C:\Users\<USER>\.m2\repository\com\sun\xml\bind\jaxb-impl\2.3.0.1\jaxb-impl-2.3.0.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\oauth\spring-security-oauth2\2.3.4.RELEASE\spring-security-oauth2-2.3.4.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jackson\jackson-mapper-asl\1.9.13\jackson-mapper-asl-1.9.13.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jackson\jackson-core-asl\1.9.13\jackson-core-asl-1.9.13.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-jwt\1.0.9.RELEASE\spring-security-jwt-1.0.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcpkix-jdk15on\1.56\bcpkix-jdk15on-1.56.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-netflix-eureka-client\2.2.6.RELEASE\spring-cloud-starter-netflix-eureka-client-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter\2.2.6.RELEASE\spring-cloud-starter-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-context\2.2.6.RELEASE\spring-cloud-context-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-rsa\1.0.9.RELEASE\spring-security-rsa-1.0.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-netflix-hystrix\2.2.6.RELEASE\spring-cloud-netflix-hystrix-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-netflix-eureka-client\2.2.6.RELEASE\spring-cloud-netflix-eureka-client-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\netflix\eureka\eureka-client\1.10.7\eureka-client-1.10.7.jar;C:\Users\<USER>\.m2\repository\com\netflix\netflix-commons\netflix-eventbus\0.3.0\netflix-eventbus-0.3.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\netflix-commons\netflix-infix\0.3.0\netflix-infix-0.3.0.jar;C:\Users\<USER>\.m2\repository\commons-jxpath\commons-jxpath\1.3\commons-jxpath-1.3.jar;C:\Users\<USER>\.m2\repository\joda-time\joda-time\2.3\joda-time-2.3.jar;C:\Users\<USER>\.m2\repository\org\antlr\antlr-runtime\3.4\antlr-runtime-3.4.jar;C:\Users\<USER>\.m2\repository\org\antlr\stringtemplate\3.2.1\stringtemplate-3.2.1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-math\2.2\commons-math-2.2.jar;C:\Users\<USER>\.m2\repository\com\netflix\archaius\archaius-core\0.7.6\archaius-core-0.7.6.jar;C:\Users\<USER>\.m2\repository\javax\ws\rs\jsr311-api\1.1.1\jsr311-api-1.1.1.jar;C:\Users\<USER>\.m2\repository\com\netflix\servo\servo-core\0.12.21\servo-core-0.12.21.jar;C:\Users\<USER>\.m2\repository\com\sun\jersey\jersey-core\1.19.1\jersey-core-1.19.1.jar;C:\Users\<USER>\.m2\repository\com\sun\jersey\jersey-client\1.19.1\jersey-client-1.19.1.jar;C:\Users\<USER>\.m2\repository\com\sun\jersey\contribs\jersey-apache-client4\1.19.1\jersey-apache-client4-1.19.1.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;C:\Users\<USER>\.m2\repository\commons-configuration\commons-configuration\1.10\commons-configuration-1.10.jar;C:\Users\<USER>\.m2\repository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;C:\Users\<USER>\.m2\repository\com\google\inject\guice\4.1.0\guice-4.1.0.jar;C:\Users\<USER>\.m2\repository\aopalliance\aopalliance\1.0\aopalliance-1.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\eureka\eureka-core\1.10.7\eureka-core-1.10.7.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\woodstox\woodstox-core\5.3.0\woodstox-core-5.3.0.jar;C:\Users\<USER>\.m2\repository\org\codehaus\woodstox\stax2-api\4.2\stax2-api-4.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-netflix-archaius\2.2.6.RELEASE\spring-cloud-starter-netflix-archaius-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-netflix-ribbon\2.2.6.RELEASE\spring-cloud-starter-netflix-ribbon-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon\2.3.0\ribbon-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon-transport\2.3.0\ribbon-transport-2.3.0.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxnetty-contexts\0.4.9\rxnetty-contexts-0.4.9.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxnetty-servo\0.4.9\rxnetty-servo-0.4.9.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxnetty\0.4.9\rxnetty-0.4.9.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon-core\2.3.0\ribbon-core-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon-httpclient\2.3.0\ribbon-httpclient-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\netflix-commons\netflix-commons-util\0.3.0\netflix-commons-util-0.3.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon-loadbalancer\2.3.0\ribbon-loadbalancer-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\netflix-commons\netflix-statistics\0.1.1\netflix-statistics-0.1.1.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxjava\1.3.8\rxjava-1.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-loadbalancer\2.2.6.RELEASE\spring-cloud-starter-loadbalancer-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-loadbalancer\2.2.6.RELEASE\spring-cloud-loadbalancer-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.3.16.RELEASE\reactor-core-3.3.16.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.3\reactive-streams-1.0.3.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\addons\reactor-extra\3.3.6.RELEASE\reactor-extra-3.3.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-cache\2.3.10.RELEASE\spring-boot-starter-cache-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\stoyanr\evictor\1.0.0\evictor-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon-eureka\2.3.0\ribbon-eureka-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\thoughtworks\xstream\xstream\1.4.13\xstream-1.4.13.jar;C:\Users\<USER>\.m2\repository\xmlpull\xmlpull\1.1.3.1\xmlpull-1.1.3.1.jar;C:\Users\<USER>\.m2\repository\xpp3\xpp3_min\1.1.4c\xpp3_min-1.1.4c.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-openfeign\2.2.6.RELEASE\spring-cloud-starter-openfeign-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-openfeign-core\2.2.6.RELEASE\spring-cloud-openfeign-core-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\form\feign-form-spring\3.8.0\feign-form-spring-3.8.0.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\form\feign-form\3.8.0\feign-form-3.8.0.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.2\commons-io-2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-commons\2.2.6.RELEASE\spring-cloud-commons-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.3.9.RELEASE\spring-security-crypto-5.3.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-core\10.10.1\feign-core-10.10.1.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-slf4j\10.10.1\feign-slf4j-10.10.1.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-hystrix\10.10.1\feign-hystrix-10.10.1.jar;C:\Users\<USER>\.m2\repository\com\netflix\hystrix\hystrix-core\1.5.18\hystrix-core-1.5.18.jar;C:\Users\<USER>\.m2\repository\org\hdrhistogram\HdrHistogram\2.1.9\HdrHistogram-2.1.9.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-mail\2.3.4.RELEASE\spring-boot-starter-mail-2.3.4.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\5.2.14.RELEASE\spring-context-support-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\sun\mail\jakarta.mail\1.6.7\jakarta.mail-1.6.7.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\com\stripe\stripe-java\26.5.1\stripe-java-26.5.1.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.9.1\gson-2.9.1.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger2\2.9.2\springfox-swagger2-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-annotations\1.5.20\swagger-annotations-1.5.20.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-models\1.5.20\swagger-models-1.5.20.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spi\2.9.2\springfox-spi-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-core\2.9.2\springfox-core-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-schema\2.9.2\springfox-schema-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-common\2.9.2\springfox-swagger-common-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-web\2.9.2\springfox-spring-web-2.9.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\1.2.0.RELEASE\spring-plugin-core-1.2.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-metadata\1.2.0.RELEASE\spring-plugin-metadata-1.2.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.2.0.Final\mapstruct-1.2.0.Final.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-ui\2.9.2\springfox-swagger-ui-2.9.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.11.2\jackson-annotations-2.11.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.11.2\jackson-databind-2.11.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-netflix-ribbon\2.2.1.RELEASE\spring-cloud-netflix-ribbon-2.2.1.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-netflix-archaius\2.2.6.RELEASE\spring-cloud-netflix-archaius-2.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\2.3.10.RELEASE\spring-boot-starter-data-jpa-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.3.10.RELEASE\spring-boot-starter-aop-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.6\aspectjweaver-1.9.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.3.10.RELEASE\spring-boot-starter-jdbc-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.2.14.RELEASE\spring-jdbc-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-core\5.4.30.Final\hibernate-core-5.4.30.Final.jar;C:\Users\<USER>\.m2\repository\org\javassist\javassist\3.27.0-GA\javassist-3.27.0-GA.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.10.22\byte-buddy-1.10.22.jar;C:\Users\<USER>\.m2\repository\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\jboss\jandex\2.2.3.Final\jandex-2.2.3.Final.jar;C:\Users\<USER>\.m2\repository\org\dom4j\dom4j\2.1.3\dom4j-2.1.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\5.1.2.Final\hibernate-commons-annotations-5.1.2.Final.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.4\jaxb-runtime-2.3.4.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.4\txw2-2.3.4.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\2.3.9.RELEASE\spring-data-jpa-2.3.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.3.9.RELEASE\spring-data-commons-2.3.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\5.2.14.RELEASE\spring-orm-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.2.14.RELEASE\spring-tx-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\5.2.14.RELEASE\spring-aspects-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.3.10.RELEASE\spring-boot-starter-web-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.3.10.RELEASE\spring-boot-starter-json-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.11.4\jackson-datatype-jdk8-2.11.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.11.4\jackson-datatype-jsr310-2.11.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.11.4\jackson-module-parameter-names-2.11.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.3.10.RELEASE\spring-boot-starter-tomcat-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.45\tomcat-embed-core-9.0.45.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.45\tomcat-embed-websocket-9.0.45.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.2.14.RELEASE\spring-webmvc-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\mariadb\jdbc\mariadb-java-client\2.7.0\mariadb-java-client-2.7.0.jar;C:\Users\<USER>\.m2\repository\com\opencsv\opencsv\4.1\opencsv-4.1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.10\commons-lang3-3.10.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-text\1.1\commons-text-1.1.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar;C:\Users\<USER>\.m2\repository\net\logstash\logback\logstash-logback-encoder\6.4\logstash-logback-encoder-6.4.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.20\lombok-1.18.20.jar;C:\Users\<USER>\.m2\repository\com\google\firebase\firebase-admin\8.1.0\firebase-admin-8.1.0.jar;C:\Users\<USER>\.m2\repository\com\google\api-client\google-api-client\1.30.7\google-api-client-1.30.7.jar;C:\Users\<USER>\.m2\repository\com\google\api-client\google-api-client-gson\1.32.1\google-api-client-gson-1.32.1.jar;C:\Users\<USER>\.m2\repository\com\google\http-client\google-http-client-gson\1.37.0\google-http-client-gson-1.37.0.jar;C:\Users\<USER>\.m2\repository\com\google\http-client\google-http-client\1.37.0\google-http-client-1.37.0.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.14\httpcore-4.4.14.jar;C:\Users\<USER>\.m2\repository\com\google\api\api-common\1.10.0\api-common-1.10.0.jar;C:\Users\<USER>\.m2\repository\com\google\auth\google-auth-library-oauth2-http\0.22.0\google-auth-library-oauth2-http-0.22.0.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\google-cloud-firestore\2.1.0\google-cloud-firestore-2.1.0.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\google-cloud-core-grpc\1.93.9\google-cloud-core-grpc-1.93.9.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-core\1.33.0\grpc-core-1.33.0.jar;C:\Users\<USER>\.m2\repository\com\google\android\annotations\4.1.1.4\annotations-4.1.1.4.jar;C:\Users\<USER>\.m2\repository\io\perfmark\perfmark-api\0.19.0\perfmark-api-0.19.0.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.14\commons-codec-1.14.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-cloud-firestore-v1\2.1.0\proto-google-cloud-firestore-v1-2.1.0.jar;C:\Users\<USER>\.m2\repository\io\opencensus\opencensus-contrib-grpc-util\0.24.0\opencensus-contrib-grpc-util-0.24.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-protobuf\1.33.0\grpc-protobuf-1.33.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-protobuf-lite\1.47.0\grpc-protobuf-lite-1.47.0.jar;C:\Users\<USER>\.m2\repository\org\codehaus\mojo\animal-sniffer-annotations\1.19\animal-sniffer-annotations-1.19.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-api\1.33.0\grpc-api-1.33.0.jar;C:\Users\<USER>\.m2\repository\com\google\api\gax-grpc\1.60.0\gax-grpc-1.60.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-auth\1.33.0\grpc-auth-1.33.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-netty-shaded\1.33.0\grpc-netty-shaded-1.33.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-alts\1.33.0\grpc-alts-1.33.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-grpclb\1.33.0\grpc-grpclb-1.33.0.jar;C:\Users\<USER>\.m2\repository\org\conscrypt\conscrypt-openjdk-uber\2.2.1\conscrypt-openjdk-uber-2.2.1.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-stub\1.33.0\grpc-stub-1.33.0.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.63.Final\netty-codec-http-4.1.63.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.63.Final\netty-common-4.1.63.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.63.Final\netty-buffer-4.1.63.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.63.Final\netty-codec-4.1.63.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.63.Final\netty-handler-4.1.63.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.63.Final\netty-resolver-4.1.63.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.63.Final\netty-transport-4.1.63.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\2.3.10.RELEASE\spring-boot-starter-test-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.3.10.RELEASE\spring-boot-test-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.3.10.RELEASE\spring-boot-test-autoconfigure-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.4.0\json-path-2.4.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.3\json-smart-2.3.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\1.2\accessors-smart-1.2.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\5.0.4\asm-5.0.4.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.16.1\assertj-core-3.16.1.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.6.3\junit-jupiter-5.6.3.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.6.3\junit-jupiter-api-5.6.3.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.6.3\junit-platform-commons-1.6.3.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.6.3\junit-jupiter-params-5.6.3.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.6.3\junit-jupiter-engine-5.6.3.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.6.3\junit-platform-engine-1.6.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\3.3.3\mockito-core-3.3.3.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.10.22\byte-buddy-agent-1.10.22.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\2.6\objenesis-2.6.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\3.3.3\mockito-junit-jupiter-3.3.3.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.2.14.RELEASE\spring-core-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.2.14.RELEASE\spring-jcl-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\5.2.14.RELEASE\spring-test-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.7.0\xmlunit-core-2.7.0.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;C:\Users\<USER>\.m2\repository\org\zalando\problem-spring-web\0.27.0\problem-spring-web-0.27.0.jar;C:\Users\<USER>\.m2\repository\org\zalando\problem-violations\0.27.0\problem-violations-0.27.0.jar;C:\Users\<USER>\.m2\repository\org\zalando\problem-spring-common\0.27.0\problem-spring-common-0.27.0.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\zalando\problem\0.26.0\problem-0.26.0.jar;C:\Users\<USER>\.m2\repository\org\zalando\jackson-datatype-problem\0.26.0\jackson-datatype-problem-0.26.0.jar;C:\Users\<USER>\.m2\repository\org\zalando\faux-pas\0.9.0\faux-pas-0.9.0.jar;C:\Users\<USER>\.m2\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\google-cloud-storage\1.113.1\google-cloud-storage-1.113.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-compat-qual\2.5.5\checker-compat-qual-2.5.5.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.3.4\error_prone_annotations-2.3.4.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\io\opencensus\opencensus-contrib-http-util\0.24.0\opencensus-contrib-http-util-0.24.0.jar;C:\Users\<USER>\.m2\repository\com\google\http-client\google-http-client-jackson2\1.37.0\google-http-client-jackson2-1.37.0.jar;C:\Users\<USER>\.m2\repository\com\google\oauth-client\google-oauth-client\1.31.0\google-oauth-client-1.31.0.jar;C:\Users\<USER>\.m2\repository\com\google\apis\google-api-services-storage\v1-rev20200814-1.30.10\google-api-services-storage-v1-rev20200814-1.30.10.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\google-cloud-core\1.93.9\google-cloud-core-1.93.9.jar;C:\Users\<USER>\.m2\repository\com\google\auto\value\auto-value-annotations\1.7.2\auto-value-annotations-1.7.2.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-common-protos\1.18.1\proto-google-common-protos-1.18.1.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\google-cloud-core-http\1.93.9\google-cloud-core-http-1.93.9.jar;C:\Users\<USER>\.m2\repository\com\google\http-client\google-http-client-appengine\1.37.0\google-http-client-appengine-1.37.0.jar;C:\Users\<USER>\.m2\repository\com\google\api\gax-httpjson\0.77.0\gax-httpjson-0.77.0.jar;C:\Users\<USER>\.m2\repository\com\google\api\gax\1.60.0\gax-1.60.0.jar;C:\Users\<USER>\.m2\repository\com\google\auth\google-auth-library-credentials\0.22.0\google-auth-library-credentials-0.22.0.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-context\1.33.0\grpc-context-1.33.0.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-iam-v1\1.0.1\proto-google-iam-v1-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java\3.13.0\protobuf-java-3.13.0.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java-util\3.13.0\protobuf-java-util-3.13.0.jar;C:\Users\<USER>\.m2\repository\org\threeten\threetenbp\1.4.4\threetenbp-1.4.4.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-search-orm\5.11.10.Final\hibernate-search-orm-5.11.10.Final.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-search-engine\5.11.10.Final\hibernate-search-engine-5.11.10.Final.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-core\5.5.5\lucene-core-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-misc\5.5.5\lucene-misc-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-analyzers-common\5.5.5\lucene-analyzers-common-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-facet\5.5.5\lucene-facet-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-queries\5.5.5\lucene-queries-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-queryparser\5.5.5\lucene-queryparser-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.2.14.RELEASE\spring-web-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.2.14.RELEASE\spring-beans-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jettison\jettison\1.4.0\jettison-1.4.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\23.0.0\annotations-23.0.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-websocket\2.3.10.RELEASE\spring-boot-starter-websocket-2.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-messaging\5.2.14.RELEASE\spring-messaging-5.2.14.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-websocket\5.2.14.RELEASE\spring-websocket-5.2.14.RELEASE.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\jdk-11"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="E:\Local Documents\GitHub\worklink-backend\worklink-service"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire10990347065882896941\surefirebooter10381545004774943267.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="11.0.26+7-LTS-187"/>
    <property name="user.name" value="tinas"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="Cp1252"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="java.vendor.version" value="18.9"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="11.0.26"/>
    <property name="user.dir" value="E:\Local Documents\GitHub\worklink-backend\worklink-service"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.awt.windows.WPrinterJob"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.library.path" value="C:\Program Files\Java\jdk-11\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Users\<USER>\flutter\bin\cache\dart-sdk\bin\;C:\Program Files (x86)\Microsoft\Edge\Application;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\dotnet\;C:\flutter\bin;C:\Program Files\Git\bin;C:\Windows\System32;&quot;C:\Program Files\Git\bin\git.exe;C:\Program Files\Git\cmd;C:\Windows\System32&quot;;C:\Windows\System32\WindowsPowerShell\v1.0;C:\xampp\php;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\ProgramData\chocolatey\bin;C:\ProgramData\chocolatey\lib\maven\apache-maven-3.9.10\bin;C:\Users\<USER>\Program Files\nodejs\;C:\Program Files\Java\jdk-11\bin;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Users\<USER>\scoop\apps\maven\current\bin;C:\Users\<USER>\scoop\shims;C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.1.2\bin;;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\flutter\bin;C:\flutter;;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vm.version" value="11.0.26+7-LTS-187"/>
    <property name="java.specification.maintenance.version" value="3"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="55.0"/>
  </properties>
  <testcase name="testConvertSet_WithNullInput" classname="com.cap10mycap10.worklinkservice.mapper.VehicleInventoryToVehicleInventoryDtoTest" time="0"/>
  <testcase name="testConvert_WithNullInput" classname="com.cap10mycap10.worklinkservice.mapper.VehicleInventoryToVehicleInventoryDtoTest" time="0"/>
  <testcase name="testConvert_Success" classname="com.cap10mycap10.worklinkservice.mapper.VehicleInventoryToVehicleInventoryDtoTest" time="0.017">
    <failure message="expected: &lt;2024-01-01T10:00:00Z&gt; but was: &lt;2024-01-01T12:00&gt;" type="org.opentest4j.AssertionFailedError"><![CDATA[org.opentest4j.AssertionFailedError: expected: <2024-01-01T10:00:00Z> but was: <2024-01-01T12:00>
	at com.cap10mycap10.worklinkservice.mapper.VehicleInventoryToVehicleInventoryDtoTest.testConvert_Success(VehicleInventoryToVehicleInventoryDtoTest.java:92)
]]></failure>
  </testcase>
  <testcase name="testConvert_WithNullTaxRate" classname="com.cap10mycap10.worklinkservice.mapper.VehicleInventoryToVehicleInventoryDtoTest" time="0.001"/>
  <testcase name="testConvertSet_Success" classname="com.cap10mycap10.worklinkservice.mapper.VehicleInventoryToVehicleInventoryDtoTest" time="0.001"/>
  <testcase name="testJsonSerialization_NoHibernateProxyIssues" classname="com.cap10mycap10.worklinkservice.mapper.VehicleInventoryToVehicleInventoryDtoTest" time="0.007"/>
  <testcase name="testConvert_WithNullVehicle" classname="com.cap10mycap10.worklinkservice.mapper.VehicleInventoryToVehicleInventoryDtoTest" time="0.001"/>
</testsuite>