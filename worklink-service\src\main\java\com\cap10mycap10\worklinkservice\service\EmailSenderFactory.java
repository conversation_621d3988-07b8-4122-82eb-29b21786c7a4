package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.model.AgencyEmailConfiguration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Factory service for creating JavaMailSender instances.
 * This service manages both default system email sender and agency-specific email senders.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EmailSenderFactory {

    private final AgencyEmailConfigurationService agencyEmailConfigService;
    
    @Autowired
    private JavaMailSender defaultEmailSender;
    
    @Value("${env.companyName}")
    private String defaultCompanyName;
    
    @Value("${env.email}")
    private String defaultFromEmail;
    
    @Value("${env.supportEmail}")
    private String defaultSupportEmail;
    
    @Value("${env.website}")
    private String defaultWebsiteUrl;
    
    @Value("${env.companyLogo}")
    private String defaultCompanyLogo;

    // Cache for agency email senders to avoid recreating them frequently
    private final ConcurrentHashMap<Long, JavaMailSender> agencyEmailSenderCache = new ConcurrentHashMap<>();
    
    // Cache for agency email configurations to avoid frequent database calls
    private final ConcurrentHashMap<Long, AgencyEmailConfiguration> agencyConfigCache = new ConcurrentHashMap<>();

    /**
     * Get email sender for a specific agency.
     * Returns agency-specific sender if available and verified, otherwise returns default sender.
     *
     * @param agencyId the agency ID
     * @return JavaMailSender instance
     */
    public JavaMailSender getEmailSender(Long agencyId) {
        if (agencyId == null) {
            log.debug("Agency ID is null, using default email sender");
            return defaultEmailSender;
        }

        try {
            // Check if we have a cached sender for this agency
            JavaMailSender cachedSender = agencyEmailSenderCache.get(agencyId);
            if (cachedSender != null) {
                log.debug("Using cached email sender for agency ID: {}", agencyId);
                return cachedSender;
            }

            // Try to get agency-specific configuration
            Optional<AgencyEmailConfiguration> configOpt = agencyEmailConfigService.findVerifiedActiveByAgencyId(agencyId);

            if (configOpt.isPresent()) {
                AgencyEmailConfiguration config = configOpt.get();

                if (config.isReadyToUse()) {
                    log.info("Creating agency-specific email sender for agency ID: {} with SMTP host: {}",
                            agencyId, config.getSmtpHost());
                    JavaMailSender agencySender = createAgencyEmailSender(config);

                    // Cache the sender and configuration
                    agencyEmailSenderCache.put(agencyId, agencySender);
                    agencyConfigCache.put(agencyId, config);

                    log.info("Successfully created and cached agency-specific email sender for agency ID: {}", agencyId);
                    return agencySender;
                } else {
                    log.warn("Agency email configuration exists but is not ready to use for agency ID: {}. " +
                            "Active: {}, Verified: {}, Ready: {}",
                            agencyId, config.getIsActive(), config.getIsVerified(), config.isReadyToUse());
                }
            } else {
                log.debug("No verified active email configuration found for agency ID: {}, using default sender", agencyId);
            }

        } catch (Exception e) {
            log.error("Error getting email sender for agency ID: {}, falling back to default sender. Error: {}",
                     agencyId, e.getMessage(), e);
        }

        log.debug("Using default email sender for agency ID: {}", agencyId);
        return defaultEmailSender;
    }

    /**
     * Get email configuration for a specific agency.
     * Returns agency-specific configuration if available, otherwise returns default configuration.
     * 
     * @param agencyId the agency ID
     * @return EmailConfiguration containing sender details
     */
    public EmailConfiguration getEmailConfiguration(Long agencyId) {
        if (agencyId == null) {
            return getDefaultEmailConfiguration();
        }

        try {
            // Check cache first
            AgencyEmailConfiguration cachedConfig = agencyConfigCache.get(agencyId);
            if (cachedConfig != null && cachedConfig.isReadyToUse()) {
                return createEmailConfigurationFromAgency(cachedConfig);
            }

            // Try to get from database
            Optional<AgencyEmailConfiguration> configOpt = agencyEmailConfigService.findVerifiedActiveByAgencyId(agencyId);
            
            if (configOpt.isPresent()) {
                AgencyEmailConfiguration config = configOpt.get();
                
                if (config.isReadyToUse()) {
                    // Cache the configuration
                    agencyConfigCache.put(agencyId, config);
                    return createEmailConfigurationFromAgency(config);
                }
            }
            
        } catch (Exception e) {
            log.error("Error getting email configuration for agency ID: {}, using default configuration", agencyId, e);
        }

        return getDefaultEmailConfiguration();
    }

    /**
     * Clear cache for a specific agency (useful when configuration is updated)
     * 
     * @param agencyId the agency ID
     */
    public void clearAgencyCache(Long agencyId) {
        log.info("Clearing email cache for agency ID: {}", agencyId);
        agencyEmailSenderCache.remove(agencyId);
        agencyConfigCache.remove(agencyId);
    }

    /**
     * Clear all agency caches
     */
    public void clearAllCaches() {
        log.info("Clearing all email caches");
        agencyEmailSenderCache.clear();
        agencyConfigCache.clear();
    }

    /**
     * Check if agency has custom email configuration
     * 
     * @param agencyId the agency ID
     * @return true if agency has verified active email configuration
     */
    public boolean hasCustomEmailConfiguration(Long agencyId) {
        if (agencyId == null) {
            return false;
        }
        
        try {
            return agencyEmailConfigService.hasVerifiedActiveConfiguration(agencyId);
        } catch (Exception e) {
            log.error("Error checking custom email configuration for agency ID: {}", agencyId, e);
            return false;
        }
    }

    /**
     * Create JavaMailSender from agency email configuration
     */
    private JavaMailSender createAgencyEmailSender(AgencyEmailConfiguration config) {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        
        mailSender.setHost(config.getSmtpHost());
        mailSender.setPort(config.getSmtpPort());
        mailSender.setUsername(config.getSmtpUsername());
        mailSender.setPassword(config.getSmtpPassword());
        
        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", config.getSmtpAuth().toString());
        props.put("mail.smtp.starttls.enable", config.getSmtpStarttlsEnable().toString());
        props.put("mail.smtp.starttls.required", config.getSmtpStarttlsRequired().toString());
        props.put("mail.debug", "false");
        
        if (config.getSmtpSslEnable()) {
            props.put("mail.smtp.ssl.enable", "true");
            String socketFactoryClass = config.getEffectiveSocketFactoryClass();
            if (socketFactoryClass != null) {
                props.put("mail.smtp.socketFactory.class", socketFactoryClass);
            }
        }
        
        return mailSender;
    }

    /**
     * Create EmailConfiguration from agency configuration
     */
    private EmailConfiguration createEmailConfigurationFromAgency(AgencyEmailConfiguration config) {
        return EmailConfiguration.builder()
            .fromEmail(config.getFromEmail())
            .fromName(config.getFromName())
            .replyToEmail(config.getReplyToEmail())
            .supportEmail(config.getSupportEmail() != null ? config.getSupportEmail() : defaultSupportEmail)
            .websiteUrl(config.getWebsiteUrl() != null ? config.getWebsiteUrl() : defaultWebsiteUrl)
            .logoUrl(config.getLogoUrl() != null ? config.getLogoUrl() : defaultCompanyLogo)
            .isCustomConfiguration(true)
            .build();
    }

    /**
     * Get default email configuration
     */
    private EmailConfiguration getDefaultEmailConfiguration() {
        return EmailConfiguration.builder()
            .fromEmail(defaultFromEmail)
            .fromName(defaultCompanyName)
            .replyToEmail(defaultFromEmail)
            .supportEmail(defaultSupportEmail)
            .websiteUrl(defaultWebsiteUrl)
            .logoUrl(defaultCompanyLogo)
            .isCustomConfiguration(false)
            .build();
    }

    /**
     * Data class for email configuration details
     */
    public static class EmailConfiguration {
        private final String fromEmail;
        private final String fromName;
        private final String replyToEmail;
        private final String supportEmail;
        private final String websiteUrl;
        private final String logoUrl;
        private final boolean isCustomConfiguration;

        private EmailConfiguration(Builder builder) {
            this.fromEmail = builder.fromEmail;
            this.fromName = builder.fromName;
            this.replyToEmail = builder.replyToEmail;
            this.supportEmail = builder.supportEmail;
            this.websiteUrl = builder.websiteUrl;
            this.logoUrl = builder.logoUrl;
            this.isCustomConfiguration = builder.isCustomConfiguration;
        }

        public static Builder builder() {
            return new Builder();
        }

        // Getters
        public String getFromEmail() { return fromEmail; }
        public String getFromName() { return fromName; }
        public String getReplyToEmail() { return replyToEmail; }
        public String getSupportEmail() { return supportEmail; }
        public String getWebsiteUrl() { return websiteUrl; }
        public String getLogoUrl() { return logoUrl; }
        public boolean isCustomConfiguration() { return isCustomConfiguration; }

        public static class Builder {
            private String fromEmail;
            private String fromName;
            private String replyToEmail;
            private String supportEmail;
            private String websiteUrl;
            private String logoUrl;
            private boolean isCustomConfiguration;

            public Builder fromEmail(String fromEmail) { this.fromEmail = fromEmail; return this; }
            public Builder fromName(String fromName) { this.fromName = fromName; return this; }
            public Builder replyToEmail(String replyToEmail) { this.replyToEmail = replyToEmail; return this; }
            public Builder supportEmail(String supportEmail) { this.supportEmail = supportEmail; return this; }
            public Builder websiteUrl(String websiteUrl) { this.websiteUrl = websiteUrl; return this; }
            public Builder logoUrl(String logoUrl) { this.logoUrl = logoUrl; return this; }
            public Builder isCustomConfiguration(boolean isCustomConfiguration) { this.isCustomConfiguration = isCustomConfiguration; return this; }

            public EmailConfiguration build() {
                return new EmailConfiguration(this);
            }
        }
    }
}
