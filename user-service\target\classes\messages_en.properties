message.username=Username required
message.password=Password required
message.unauth=Unauthorized Access !!
message.badCredentials=Invalid Credentials
message.sessionExpired=Session Timed Out
message.logoutError=Sorry, error logging out
message.logoutSucc=You logged out successfully
message.regSucc=You registered successfully. We will send you a confirmation message to your email account.
message.regSuccLink=You registered successfully. To confirm your registration, please click on the below link.
message.regSuccConfirmed=Your registration is confirmed.
message.regError=An account for that username/email already exists. Please enter a different username.
message.lastName=Last name is required
message.firstName=First name required
message.badEmail=Invalid email address
message.email.config.error=Error in java mail configuration
token.message=Your token is:
auth.message.disabled=Your account is disabled please check your mail and click on the confirmation link
auth.message.expired=Your registration token has expired. Please register again.
auth.message.invalidUser=This username is invalid, or does not exist.
auth.message.invalidToken=Invalid token.
label.user.email=Email:
label.user.firstName=First name:
label.user.lastName=Last name:
label.user.password=Password:
label.user.confirmPass=Confirm password
label.user.2fa=Use Two step verification
label.form.submit=Submit
label.form.title=Registration Form
label.form.loginLink=Back to login
label.login=Login here
label.form.loginTitle=Login
label.form.loginEmail=Email
label.form.loginPass=Password
label.form.login2fa=Google Authenticator Verification Code
label.form.loginEnglish=English
label.form.loginSpanish=Spanish
label.form.loginSignUp=Sign up
label.form.loginSignUpCaptcha=Sign up with Captcha
label.form.loginSignUpReCaptchaV3=Sign up with reCAPTCHA v3
label.form.rememberMe=Remember Me
label.pages.logout=Logout
label.pages.admin=Administrator
label.pages.home.title=Home
label.pages.home.message=Welcome Home
label.pages.users.message=View Logged In Users
label.pages.users.sessionregistry.message=View Logged In Users from SessionRegistry
label.pages.admin.message=Welcome Admin
label.pages.user.message=Welcome User
label.successRegister.title=Registration Success
label.badUser.title=Invalid Link
ValidEmail.user.email=Invalid email address!
UniqueUsername.user.username=An account with that username/email already exists
Size.userDto.firstName=Length must be greater than {min}
Size.userDto.lastName=Length must be greater than {min}
Size.userDto.email=Length must be greater than {min}
NotNull.user.firstName=First name required
NotEmpty.user.firstName=First name required
NotNull.user.lastName=Last name required
NotEmpty.user.lastName=Last name required
NotNull.user.username=Username(Email) required
NotEmpty.user.username=Username(Email) required
NotNull.user.password=Password required
NotEmpty.user.password=Password required
NotNull.user.matchingPassword=Required
NotEmpty.user.matchingPassword=Required
PasswordMatches.user:Password does not match!
Email.user.email=Invalid Username (Email)
label.form.resendRegistrationToken=Re-send Token
message.resendToken=We will send an email with a new registration token to your email account
message.forgetPassword=Forget Password
message.resetPassword=Reset Password
message.updatePassword=Update Password
message.userNotFound=User Not Found
auth.message.blocked=This ip is blocked for 24 hours
message.accountVerified=Your account verified successfully
message.resetPasswordSuc=Password reset successfully
message.resetYourPassword=Reset your password
message.resetPasswordEmail=You should receive a Password Reset Email shortly
message.error=Error Occurred
message.updatePasswordSuc=Password updated successfully
message.changePassword=Change Password
message.invalidOldPassword=Invalid Old Password
message.invalidReCaptcha=Invalid reCaptcha
message.unavailableReCaptcha=Registration is unavailable at this time.  Please try again later.
label.user.newPassword=New Password
label.user.oldPassword=Old Password
error.wordLength=Your password is too short
error.wordNotEmail=Do not use your email as your password
error.wordSequences=Your password contains sequences
error.wordLowercase=Use lower case characters
error.wordUppercase=Use upper case characters
error.wordOneNumber=Use numbers
error.wordOneSpecialChar=Use special characters
message.differentLocation=There was a login attempt from unusual location at {0}, we blocked the connection which was from {1} with IP address {2} \r\n If that was you, please enable this new location at {3} \r\n If it was not you, then you need to change your password {4}
message.newLoc.enabled = New Location {0} is now enabled
auth.message.unusual.location = You are trying to login from unusual location, check your email for more details
message.login.notification.deviceDetails=Device details:
message.login.notification.location=Location:
message.login.notification.ip=IP Address: