package com.cap10mycap10.userservice.service;

import com.cap10mycap10.userservice.client.WorklinkServiceClient;
import com.cap10mycap10.userservice.dto.AgencyEmailConfigurationDto;
import com.cap10mycap10.userservice.dto.AgencyInfoDto;
import com.cap10mycap10.userservice.dto.AgencyResultDto;
import com.cap10mycap10.userservice.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Implementation of AgencyService
 */
@Service
@Slf4j
public class AgencyServiceImpl implements AgencyService {

    @Autowired
    private WorklinkServiceClient worklinkServiceClient;


    @Override
    public AgencyEmailConfigurationDto getAgencyEmailConfiguration(Long agencyId) {
        try {
            if (agencyId == null) {
                log.debug("No agency ID found for user");
                return null;
            }

            log.debug("Getting email configuration for agency ID: {}", agencyId);
            AgencyEmailConfigurationDto config = worklinkServiceClient.getAgencyEmailConfiguration(agencyId);
            
            // Only return configuration if it's ready to use
            if (config != null && config.isReadyToUse()) {
                return config;
            } else {
                log.debug("Agency {} email configuration is not ready to use", agencyId);
                return null;
            }
        } catch (Exception e) {
            log.warn("Failed to get agency email configuration for agencyId {}: {}", agencyId, e.getMessage());
            return null;
        }
    }


    /**
     * Convert AgencyResultDto to AgencyInfoDto
     */
    private AgencyInfoDto convertToAgencyInfoDto(AgencyResultDto agencyResult) {
        AgencyInfoDto agencyInfo = new AgencyInfoDto();
        agencyInfo.setId(agencyResult.getId());
        agencyInfo.setName(agencyResult.getName());
        agencyInfo.setEmail(agencyResult.getEmail());
        agencyInfo.setPhone(agencyResult.getTelephone());
        agencyInfo.setAddress(agencyResult.getAddress());
        agencyInfo.setWebsite(agencyResult.getWebsite());
        agencyInfo.setLogoUrl(agencyResult.getLogo());
        agencyInfo.setActive("ACTIVE".equalsIgnoreCase(agencyResult.getStatus()));
        return agencyInfo;
    }
}
