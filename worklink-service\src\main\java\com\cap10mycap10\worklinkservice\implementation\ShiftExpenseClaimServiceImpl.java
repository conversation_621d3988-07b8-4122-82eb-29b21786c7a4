package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.ShiftExpenseClaimRepository;
import com.cap10mycap10.worklinkservice.dao.ShiftRepository;
import com.cap10mycap10.worklinkservice.dto.notification.NotificationCreateDto;
import com.cap10mycap10.worklinkservice.dto.shift.ShiftExpenseClaimDto;
import com.cap10mycap10.worklinkservice.enums.Status;
import com.cap10mycap10.worklinkservice.events.email.EmailService;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.helpers.PushNotification;
import com.cap10mycap10.worklinkservice.model.Shift;
import com.cap10mycap10.worklinkservice.model.ShiftExpenseClaim;
import com.cap10mycap10.worklinkservice.model.Worker;
import com.cap10mycap10.worklinkservice.service.*;
import com.google.firebase.messaging.FirebaseMessagingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;


@Service
@Slf4j
public class ShiftExpenseClaimServiceImpl implements ShiftExpenseClaimService {

    @Autowired
    private   ShiftExpenseClaimRepository agencyExpenseRateRepository;
    @Autowired
    private ShiftService shiftService;
    @Autowired
    private  EmailService emailService;
    @Autowired
    private  NotificationService notificationService;
    @Autowired
    private PushNotification pushNotification;
    @Autowired
    private ShiftRepository shiftRepository;
    @Autowired
    private AgencyExpenseRateService agencyExpenseRateService;



    @Override
    public void addShiftExpenseClaim(ShiftExpenseClaimDto expenseRateDto) {
        ShiftExpenseClaim expenseRate = new ShiftExpenseClaim();
        expenseRate.setShift(shiftService.getOne(expenseRateDto.getShiftId()));
        expenseRate.setAgencyExpenseRate(agencyExpenseRateService.getOne(expenseRateDto.getAgencyExpenseRateId()));
        expenseRate.setRate(expenseRateDto.getRate());
        expenseRate.setAmount(expenseRateDto.getAmount());
        expenseRate.setDescription(expenseRateDto.getDescription());
        agencyExpenseRateRepository.save(expenseRate);
    }

    @Override
    public List<ShiftExpenseClaim> findShiftsClaims(List<Long> shiftIds) {
        List<ShiftExpenseClaim> claims = new ArrayList<>();

        List<Shift> shifts = shiftService.findAllByIds(shiftIds);
        shifts.forEach(s->{
            Set<ShiftExpenseClaim> shiftClaims = s.getShiftExpenseClaims();
            shiftClaims.forEach(c->{
                Shift shif = new Shift();
                shif.setId(s.getId());
                shif.setNotes( s.getStart().toString());
                c.setShift(shif);
            });

            claims.addAll(shiftClaims);
        });
        
        return claims;
    }

    @Override
    public ShiftExpenseClaim save(ShiftExpenseClaimDto agencyExpenseRateDto) {
        ShiftExpenseClaim expenseRate = getOne(agencyExpenseRateDto.getId());


        if(agencyExpenseRateDto.getStatus() == Status.REJECTED){
            Shift shift = expenseRate.getShift();
            Worker worker = shift.getWorker();
            shift.setReleased(false);

            shiftRepository.save(shift);

            String title = "Your expense claim has been rejected!";
            String body =                "Hi, "+ worker.getFirstname()+" unfortunately an expense claim " +
                    "you requested for shift #"+shift.getId()+" has been rejected.\n" +
                    "Review your shift and release again for payroll processing as soon as possible " +
                    "to avoid any inconviniences." +
                    "\n" +

                    "For any queries get in touch with your agency.\n";

            NotificationCreateDto notificationCreateDto = new NotificationCreateDto();

            notificationCreateDto.setTitle(title);
            notificationCreateDto.setBody(body);
            notificationCreateDto.setWorkerId(worker.getId());

            notificationService.addWorkerNotification(notificationCreateDto);

            worker.getDevices().forEach(d->{
                try {
                    pushNotification.sendPushMessage(title, body, d.getFcmToken());
                } catch (FirebaseMessagingException e) {
                    log.error("{}",e);
                }
            });

            CompletableFuture.runAsync(() ->
                    emailService.sendSimpleMessage(worker.getEmail(), title, body, expenseRate.getShift().getAgency().getId())
            );
        }
        expenseRate.setStatus(agencyExpenseRateDto.getStatus());
        return agencyExpenseRateRepository.save(expenseRate);
    }

    @Override
    public ShiftExpenseClaim getOne(Long id) {
        return agencyExpenseRateRepository.findById(id).orElseThrow(
                () -> new RecordNotFoundException("Agency Expense rate not found")
        );
    }
}
