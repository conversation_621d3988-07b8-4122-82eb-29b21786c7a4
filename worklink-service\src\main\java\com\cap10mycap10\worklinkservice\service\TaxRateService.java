package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.model.TaxRate;
import java.util.List;
import java.util.Optional;

public interface TaxRateService {

    /**
     * Create a new tax rate
     */
    TaxRate create(TaxRate taxRate);

    /**
     * Update an existing tax rate
     */
    TaxRate update(TaxRate taxRate);

    /**
     * Get a tax rate by ID
     */
    Optional<TaxRate> findById(Long id);

    /**
     * Get all active tax rates for an agency
     */
    List<TaxRate> findActiveByAgencyId(Long agencyId);

    /**
     * Get all tax rates for an agency
     */
    List<TaxRate> findByAgencyId(Long agencyId);

    /**
     * Find tax rate by name and agency
     */
    Optional<TaxRate> findByNameAndAgencyId(String name, Long agencyId);

    /**
     * Deactivate a tax rate
     */
    void deactivate(Long id);

    /**
     * Activate a tax rate
     */
    void activate(Long id);

    /**
     * Delete a tax rate (only if not in use)
     */
    void delete(Long id);

    /**
     * Create default tax rates for a new agency
     */
    void createDefaultTaxRates(Long agencyId);

    /**
     * Check if a tax rate is in use and cannot be deleted
     */
    boolean isInUse(Long id);

    /**
     * Validate tax rate data before creation or update
     */
    void validateTaxRate(TaxRate taxRate, Long excludeId);

    /**
     * Check if tax rate name is unique within agency
     */
    boolean isNameUniqueForAgency(String name, Long agencyId, Long excludeId);
}
