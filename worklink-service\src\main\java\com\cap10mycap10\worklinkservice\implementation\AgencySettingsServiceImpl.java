package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.AgencyRepository;
import com.cap10mycap10.worklinkservice.dao.AgencySettingsRepository;
import com.cap10mycap10.worklinkservice.dto.settings.AgencySettingsCreateDto;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.model.AgencySettings;
import com.cap10mycap10.worklinkservice.service.AgencySettingsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;

import java.math.BigDecimal;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Service
@RequiredArgsConstructor
public class AgencySettingsServiceImpl implements AgencySettingsService {

    private final AgencySettingsRepository agencySettingsRepository;

    private final AgencyRepository agencyRepository;

    @Override
    public AgencySettings create(AgencySettingsCreateDto settingsCreateDto) {

        validateAgency(settingsCreateDto.getAgencyId());
        var agencySettings = findAgencySettings(settingsCreateDto.getAgencyId());

        if (isNull(agencySettings)) {
            agencySettings = new AgencySettings();
        }
        agencySettings.setAgencyId(settingsCreateDto.getAgencyId());
        agencySettings.setChargeVat(settingsCreateDto.isChargeVat());

        if (!settingsCreateDto.isChargeVat()) {
            agencySettings.setVatPercentage(BigDecimal.ZERO);
        } else {
            agencySettings.setVatPercentage(settingsCreateDto.getVatPercentage());
        }

        // Set new tax-related fields
        agencySettings.setDefaultTaxInclusive(settingsCreateDto.isDefaultTaxInclusive());
        agencySettings.setVehiclesTaxableByDefault(settingsCreateDto.isVehiclesTaxableByDefault());
        agencySettings.setAddonsTaxableByDefault(settingsCreateDto.isAddonsTaxableByDefault());
        return agencySettingsRepository.save(agencySettings);
    }

    @Override
    public AgencySettings update(Long id, AgencySettingsCreateDto createDto) {

        validateAgency(createDto.getAgencyId());
        var agencySettings = agencySettingsRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Settings with id " + id + " not found"));

        var agencySetting = findAgencySettings(createDto.getAgencyId());

        if (nonNull(agencySetting) && !agencySetting.equals(agencySettings)) {
            throw new BusinessValidationException("This agency is already mapped to another settings");
        }
        agencySettings.setChargeVat(createDto.isChargeVat());
        if (!createDto.isChargeVat()) {
            agencySettings.setVatPercentage(BigDecimal.ZERO);
        } else {
            agencySettings.setVatPercentage(createDto.getVatPercentage());
        }
        return agencySettingsRepository.save(agencySettings);
    }

    @Override
    public AgencySettings findAgencySettings(Long agencyId) {
        return agencySettingsRepository.findByAgencyId(agencyId);
    }

    private void validateAgency(Long agencyId) {
        agencyRepository.findById(agencyId)
                .orElseThrow(() -> new RecordNotFoundException("Agency with id " + agencyId + " was not found"));
    }
}
