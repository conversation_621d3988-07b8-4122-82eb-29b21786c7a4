package com.cap10mycap10.userservice.service;

import com.cap10mycap10.userservice.dto.AgencyEmailConfigurationDto;
import com.cap10mycap10.userservice.enums.UserType;
import com.cap10mycap10.userservice.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.MailException;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.UnsupportedEncodingException;
import java.util.List;

import static java.util.Objects.nonNull;

/**
 * Enhanced email service implementation that supports agency-specific email configuration
 */
@Service("emailService")
@Slf4j
public class EnhancedEmailServiceImpl implements EmailService {

    @Autowired
    private AgencyService agencyService;

    @Autowired
    private EmailSenderFactory emailSenderFactory;


    @Override
    @Async("threadPoolTaskExecutor")
    public void sendEmailForUser(User user, List<String> to, String subject, String text, String replyToEmail, String replyToName, Long agencyId) {
        log.info("Sending email for user ID: {}", user.getId());

        AgencyEmailConfigurationDto agencyConfig = agencyService.getAgencyEmailConfiguration(agencyId);
        JavaMailSender sender = emailSenderFactory.getEmailSender(agencyConfig);
        EmailSenderFactory.EmailConfiguration config = emailSenderFactory.getEmailConfiguration(agencyConfig);

        // Transform content for agency branding
        String transformedText = transformContentForAgency(text, config);
        String transformedSubject = transformSubjectForAgency(subject, config);

        sendEmailInternal(sender, config, to, transformedSubject, transformedText, null, replyToEmail, replyToName);
    }

    @Override
    @Async("threadPoolTaskExecutor")
    public void sendPasswordResetEmail(User user, String resetUrl, Long agencyId) {
        log.info("Sending password reset email for user: {}", user.getEmail());

        AgencyEmailConfigurationDto agencyConfig = agencyService.getAgencyEmailConfiguration(agencyId);
        EmailSenderFactory.EmailConfiguration config = emailSenderFactory.getEmailConfiguration(agencyConfig);

        String subject = "Password Reset Request";
        String message = buildPasswordResetMessage(user, resetUrl, config);

        JavaMailSender sender = emailSenderFactory.getEmailSender(agencyConfig);
        sendEmailInternal(sender, config, List.of(user.getEmail()), subject, message, null, null, null);
    }

    @Override
    @Async("threadPoolTaskExecutor")
    public void sendUserRegistrationEmail(User user, String resetPasswordUrl, String workerUrl, String androidAppUrl, String iosAppUrl, Long agencyId) {
        log.info("Sending user registration email for user: {}", user.getEmail());

        AgencyEmailConfigurationDto agencyConfig = agencyService.getAgencyEmailConfiguration(agencyId);
        EmailSenderFactory.EmailConfiguration config = emailSenderFactory.getEmailConfiguration(agencyConfig);

        String subject = config.getFromName() + " Account Creation";
        String message = buildRegistrationMessage(user, resetPasswordUrl, workerUrl, androidAppUrl, iosAppUrl, config);

        JavaMailSender sender = emailSenderFactory.getEmailSender(agencyConfig);
        sendEmailInternal(sender, config, List.of(user.getEmail()), subject, message, null, null, null);
    }

    @Override
    @Async("threadPoolTaskExecutor")
    public void sendRegistrationConfirmationEmail(User user, String confirmationUrl, Long agencyId) {
        log.info("Sending registration confirmation email for user: {}", user.getEmail());

        AgencyEmailConfigurationDto agencyConfig = agencyService.getAgencyEmailConfiguration(agencyId);
        EmailSenderFactory.EmailConfiguration config = emailSenderFactory.getEmailConfiguration(agencyConfig);

        String subject = "Registration Confirmation";
        String message = buildRegistrationConfirmationMessage(user, confirmationUrl, config);

        JavaMailSender sender = emailSenderFactory.getEmailSender(agencyConfig);
        sendEmailInternal(sender, config, List.of(user.getEmail()), subject, message, null, null, null);
    }

    private void sendEmailInternal(JavaMailSender sender, EmailSenderFactory.EmailConfiguration config, 
                                 List<String> to, String subject, String text, MultipartFile file, 
                                 String replyToEmail, String replyToName) {
        try {
            MimeMessage message = sender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            
            String[] recipients = to.toArray(new String[0]);
            helper.setTo(recipients);
            helper.setSubject(subject);

            // Set from address and name
            try {
                helper.setFrom(config.getFromEmail(), config.getFromName());
            } catch (UnsupportedEncodingException e) {
                helper.setFrom(config.getFromEmail());
            }

            // Set reply-to if specified
            if (nonNull(replyToEmail) && nonNull(replyToName)) {
                try {
                    helper.setReplyTo(replyToEmail, replyToName);
                } catch (UnsupportedEncodingException e) {
                    helper.setReplyTo(replyToEmail);
                }
            } else if (config.getReplyToEmail() != null) {
                helper.setReplyTo(config.getReplyToEmail());
            }

            // Set email content
            String htmlContent = getHtmlEmail(text, subject, config);
            helper.setText(htmlContent, true);

            // Add attachment if provided
            if (file != null && !file.isEmpty()) {
                helper.addAttachment("File.jpg", file);
            }

            sender.send(message);
            log.info("Email sent successfully to: {}", to);
        } catch (MessagingException | MailException e) {
            log.error("Failed to send email to: {}", to, e);
        }
    }

    private String transformContentForAgency(String content, EmailSenderFactory.EmailConfiguration config) {
        if (content == null) return content;

        // Replace system names with agency name
        content = content.replaceAll("(?i)MyWorklink|KarLink|MyKarlink", config.getFromName());
        
        // Replace website URLs
        if (config.getWebsiteUrl() != null) {
            content = content.replaceAll("https://online\\.myworklink\\.uk", config.getWebsiteUrl());
            content = content.replaceAll("https://myworklink\\.uk", config.getWebsiteUrl());
        }
        
        // Replace support email
        if (config.getSupportEmail() != null) {
            content = content.replaceAll("support@myworklink\\.uk", config.getSupportEmail());
        }

        return content;
    }

    private String transformSubjectForAgency(String subject, EmailSenderFactory.EmailConfiguration config) {
        if (subject == null) return subject;
        
        // Replace system names in subject
        return subject.replaceAll("(?i)MyWorklink|KarLink|MyKarlink", config.getFromName());
    }

    private String buildPasswordResetMessage(User user, String resetUrl, EmailSenderFactory.EmailConfiguration config) {
        return "<p>Hi " + user.getFirstName() + ",</p>" +
               "<p>We received a request to reset your password for your " + config.getFromName() + " account.</p>" +
               "<p>To reset your password, please click the link below:</p>" +
               "<p><a href='" + resetUrl + "' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Reset Password</a></p>" +
               "<p><strong>Important:</strong> This link will expire in 24 hours for security reasons.</p>" +
               "<p>If you have any issues, feel free to reach out to our support team at <a href='mailto:" + config.getSupportEmail() + "'>" + config.getSupportEmail() + "</a>.</p>" +
               "<p>Best regards,<br>" + config.getFromName() + " Team</p>";
    }

    private String buildRegistrationMessage(User user, String resetPasswordUrl, String workerUrl, String androidAppUrl, String iosAppUrl, EmailSenderFactory.EmailConfiguration config) {
        if (user.getAgentId() != null && user.getUserType() == UserType.AGENCY) {
            return buildAgentRegistrationMessage(user, resetPasswordUrl, workerUrl, config);
        } else if (user.getClientId() != null) {
            return buildClientRegistrationMessage(user, resetPasswordUrl, workerUrl, config);
        } else if (user.getWorkerId() != null) {
            return buildWorkerRegistrationMessage(user, resetPasswordUrl, androidAppUrl, iosAppUrl, config);
        } else {
            return buildSystemUserRegistrationMessage(user, resetPasswordUrl, config);
        }
    }

    private String buildAgentRegistrationMessage(User user, String resetPasswordUrl, String workerUrl, EmailSenderFactory.EmailConfiguration config) {
        return "<h1>Welcome to " + config.getFromName() + "!</h1>" +
               "<div class='content'>" +
               "<p>Hi " + user.getFirstName() + ",</p>" +
               "<p>We're excited to have you on board! A new account has been created for you at <a href='" + workerUrl + "'>" + workerUrl + "</a>.</p>" +
               "<p>To get started, please set your password using the link below:</p>" +
               "<div style='text-align: center;'>" +
               "<a href='" + resetPasswordUrl + "' class='button' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Set Your Password</a>" +
               "</div>" +
               "<p>If you have any questions, please contact our support team at <a href='mailto:" + config.getSupportEmail() + "'>" + config.getSupportEmail() + "</a>.</p>" +
               "<p>Best regards,<br>" + config.getFromName() + " Team</p>" +
               "</div>";
    }

    private String buildClientRegistrationMessage(User user, String resetPasswordUrl, String workerUrl, EmailSenderFactory.EmailConfiguration config) {
        return "<p>Hi " + user.getFirstName() + ",</p>" +
               "<p>Welcome to " + config.getFromName() + "! A new account has been created for you at <a href='" + workerUrl + "'>" + workerUrl + "</a>.</p>" +
               "<p>To set your password, please use the following link:</p>" +
               "<p><a href='" + resetPasswordUrl + "' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Reset Password</a></p>" +
               "<p>If this account was created in error or you have any issues with your account, please contact <a href='mailto:" + config.getSupportEmail() + "'>" + config.getSupportEmail() + "</a>.</p>" +
               "<p>Best regards,<br>" + config.getFromName() + " Support Team</p>";
    }

    private String buildWorkerRegistrationMessage(User user, String resetPasswordUrl, String androidAppUrl, String iosAppUrl, EmailSenderFactory.EmailConfiguration config) {
        return "<p>Hi " + user.getFirstName() + ",</p>" +
               "<p>A new account was created for " + user.getEmail() + " at " + config.getFromName() + ".</p>" +
               "<p>For Android users, please download the mobile app via the link below:</p>" +
               "<p><a href='" + androidAppUrl + "'>Download Android App</a></p>" +
               "<p>Or open the web application via this link:</p>" +
               "<p><a href='" + iosAppUrl + "'>Open Web Application</a></p>" +
               "<p>To set your password, please use the following link:</p>" +
               "<p><a href='" + resetPasswordUrl + "' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Reset Password</a></p>" +
               "<p>If this account was created in error or you have any issues with your account, please contact " + config.getSupportEmail() + ".</p>" +
               "<p>Best regards,<br>" + config.getFromName() + " Team</p>";
    }

    private String buildSystemUserRegistrationMessage(User user, String resetPasswordUrl, EmailSenderFactory.EmailConfiguration config) {
        return "<p>Hi " + user.getFirstName() + ",</p>" +
               "<p>You have been invited by Admin at " + config.getWebsiteUrl() + " as a system user.</p>" +
               "<p>Please click the link below to set your password:</p>" +
               "<p><a href='" + resetPasswordUrl + "' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Reset Password</a></p>" +
               "<p>If this was an error or you have any issues, please contact: " + config.getSupportEmail() + ".</p>" +
               "<p>Best regards,<br>" + config.getFromName() + " Team</p>";
    }

    private String buildRegistrationConfirmationMessage(User user, String confirmationUrl, EmailSenderFactory.EmailConfiguration config) {
        return "<p>Hi " + user.getFirstName() + ",</p>" +
               "<p>Thank you for registering with " + config.getFromName() + "!</p>" +
               "<p>You registered successfully. Once the system administrator has approved your registration, you will be able to login.</p>" +
               "<p>Please click the link below to confirm your registration:</p>" +
               "<p><a href='" + confirmationUrl + "' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Confirm Registration</a></p>" +
               "<p>If you have any questions, please contact our support team at <a href='mailto:" + config.getSupportEmail() + "'>" + config.getSupportEmail() + "</a>.</p>" +
               "<p>Best regards,<br>" + config.getFromName() + " Team</p>";
    }

    private String getHtmlEmail(String text, String subject, EmailSenderFactory.EmailConfiguration config) {
        text = text.replaceAll("(\r\n|\n)", "<br />");
        
        return "<!DOCTYPE html>" +
               "<html>" +
               "<head>" +
               "<meta charset='UTF-8'>" +
               "<title>" + subject + "</title>" +
               "</head>" +
               "<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>" +
               "<div style='max-width: 600px; margin: 0 auto; padding: 20px;'>" +
               (config.getLogoUrl() != null ? 
                   "<div style='text-align: center; margin-bottom: 20px;'>" +
                   "<img src='" + config.getLogoUrl() + "' alt='" + config.getFromName() + "' style='max-height: 80px;'>" +
                   "</div>" : "") +
               "<div style='background-color: #f9f9f9; padding: 20px; border-radius: 5px;'>" +
               text +
               "</div>" +
               "<div style='margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;'>" +
               "<p>This email was sent by " + config.getFromName() + "</p>" +
               (config.getWebsiteUrl() != null ? "<p>Visit our website: <a href='" + config.getWebsiteUrl() + "'>" + config.getWebsiteUrl() + "</a></p>" : "") +
               "</div>" +
               "</div>" +
               "</body>" +
               "</html>";
    }
}
