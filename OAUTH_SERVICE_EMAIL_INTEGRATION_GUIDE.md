# OAuth Service Email Integration Guide

This guide explains how the oauth-service has been enhanced to support agency-specific email white-branding for authentication-related emails.

## Overview

The oauth-service now supports agency-specific email branding for:
- **OTP (One-Time Password) emails** for secure login
- **Unknown device notification emails** for security alerts
- **Future authentication-related emails**

## Architecture

### Components Added

1. **AgencyService & AgencyServiceImpl** - Service to get agency information for users
2. **EmailSenderFactory** - Factory to create agency-specific email senders with caching
3. **EnhancedEmailServiceImpl** - New email service with agency support
4. **EmailService Interface** - Clean interface for email operations
5. **AgencyResultDto & AgencyEmailConfigurationDto** - DTOs for agency data exchange
6. **Extended WorklinkServiceFeignClient** - Enhanced Feign client with agency endpoints

### Integration Flow

```
User Authentication/Security Event
        ↓
LoginServiceImpl / DeviceMetaDataServiceImpl
        ↓
EnhancedEmailServiceImpl
        ↓
AgencyService.getAgencyEmailConfiguration()
        ↓
WorklinkServiceFeignClient (Feign)
        ↓
worklink-service API endpoints
        ↓
Agency email configuration
        ↓
Agency-branded email sent
```

## Email Types Enhanced

### 1. OTP (One-Time Password) Emails
**Before:**
```
Subject: MyWorklink OTP
From: <EMAIL> (MyWorklink)
Content: "Your one time password is 1234..."
```

**After (Agency Branded):**
```
Subject: Your Premium Car Rentals Login Code
From: <EMAIL> (Premium Car Rentals)
Content: "Your login code for Premium Car Rentals is: 1234..."
```

### 2. Unknown Device Notification Emails
**Before:**
```
Subject: MyWorklink new login
Content: "An unknown device just logged into your MyWorklink account..."
```

**After (Agency Branded):**
```
Subject: New Device Login - Premium Car Rentals
Content: "We noticed a login to your Premium Car Rentals account from a new device..."
```

## User-Agency Relationship Mapping

The oauth-service determines agency association through:

1. **Agent Users** (`user.agentId != null`) → Agency via agent relationship
2. **Client Users** (`user.clientId != null`) → Agency via client relationship  
3. **Worker Users** (`user.workerId != null`) → Agency via worker relationship
4. **System Users** → Default email configuration (fallback)

## Enhanced Features

### 1. Content Transformation
- **System names**: "MyWorklink", "KarLink" → Agency name
- **URLs**: Default URLs → Agency website URLs
- **Support emails**: Default support → Agency support email
- **Logos**: Default logo → Agency logo in email templates

### 2. SMTP Configuration
- **Agency SMTP servers**: Each agency can use their own email infrastructure
- **Fallback support**: Automatic fallback to default SMTP if agency config unavailable
- **Caching**: Performance optimization with JavaMailSender caching

### 3. Security Enhancements
- **Branded OTP emails**: Professional appearance increases user trust
- **Agency-specific security alerts**: Users receive notifications from their agency
- **Consistent branding**: All authentication emails match agency identity

## Configuration Requirements

### 1. Eureka Service Discovery
The oauth-service must be registered with Eureka for Feign client communication with worklink-service.

### 2. Agency Email Configuration
Agencies must have active and verified email configuration in worklink-service.

### 3. Fallback Behavior
If agency email configuration is not available:
- System falls back to default email configuration
- No errors thrown, seamless operation
- Logs warning for troubleshooting

## Testing the Integration

### 1. Setup Test Agency Email Configuration
```bash
# Create agency email configuration in worklink-service
curl -X POST "http://localhost:8080/api/v1/agency-email-config/agency/123" \
  -H "Content-Type: application/json" \
  -d '{
    "smtpHost": "smtp.gmail.com",
    "smtpPort": 587,
    "smtpUsername": "<EMAIL>",
    "smtpPassword": "app-password",
    "fromEmail": "<EMAIL>",
    "fromName": "Test Car Rentals",
    "supportEmail": "<EMAIL>",
    "websiteUrl": "https://testcarrentals.com",
    "isActive": true
  }'
```

### 2. Test OTP Email (when enabled)
```bash
# Login with user associated with agency (OTP feature currently commented out)
curl -X POST "http://localhost:8082/api/v1/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "<EMAIL>",
    "password": "password123"
  }'
```

### 3. Test Unknown Device Notification
```bash
# Login from new device/location to trigger unknown device notification
curl -X POST "http://localhost:8082/api/v1/login" \
  -H "Content-Type: application/json" \
  -H "User-Agent: Mozilla/5.0 (Unknown Device)" \
  -d '{
    "username": "<EMAIL>",
    "password": "password123"
  }'
```

## Code Structure

### Service Layer
```
oauth-service/
├── service/
│   ├── EmailService.java (Interface)
│   ├── EnhancedEmailServiceImpl.java (Agency-aware implementation)
│   ├── LegacyEmailServiceImpl.java (Original implementation)
│   ├── AgencyService.java (Interface)
│   ├── AgencyServiceImpl.java (Implementation)
│   └── EmailSenderFactory.java (Factory for email senders)
├── dto/
│   ├── AgencyResultDto.java
│   └── AgencyEmailConfigurationDto.java
└── feignclient/
    └── WorklinkServiceFeignClient.java (Extended)
```

### Key Methods
- `EnhancedEmailServiceImpl.sendOtpEmail()` - Agency-branded OTP emails
- `EnhancedEmailServiceImpl.sendUnknownDeviceNotificationEmail()` - Security alerts
- `AgencyServiceImpl.getAgencyEmailConfiguration()` - Get agency email config
- `EmailSenderFactory.getEmailSender()` - Get agency-specific JavaMailSender

## Monitoring and Troubleshooting

### Log Messages to Watch
```
# Successful agency email configuration
INFO - Sending OTP email for user: <EMAIL>
DEBUG - Getting email configuration for agency ID: 123

# Fallback to default
WARN - Failed to get agency email configuration for user 456: Agency not found
DEBUG - Using default email sender
```

### Common Issues

1. **Feign Client Connection Issues**
   - Check Eureka registration for oauth-service and worklink-service
   - Verify zuul-gateway is routing requests correctly
   - Check network connectivity between services

2. **Agency Not Found**
   - Verify user has correct agentId/clientId/workerId
   - Check agency exists in worklink-service
   - Verify relationship tables are populated

3. **Email Configuration Not Active**
   - Check `isActive` and `isVerified` flags in agency email config
   - Verify SMTP credentials are correct
   - Test email configuration endpoint

## Benefits Achieved

1. **Complete Authentication Branding**: All auth-related emails use agency branding
2. **Enhanced Security Trust**: Users trust branded security emails more
3. **Seamless Integration**: No changes needed to existing authentication flows
4. **Performance Optimized**: Caching prevents repeated API calls
5. **Maintainable**: Clean separation of concerns with proper interfaces

## Future Enhancements

1. **Password Reset Emails**: Add agency branding to password reset flows
2. **Account Verification**: Brand account verification emails
3. **Multi-language Support**: Agency-specific language preferences
4. **Email Analytics**: Track email delivery and engagement per agency
5. **Advanced Templates**: Allow agencies to customize email templates

The oauth-service now provides complete agency-specific email branding for all authentication-related communications while maintaining backward compatibility and robust error handling.
