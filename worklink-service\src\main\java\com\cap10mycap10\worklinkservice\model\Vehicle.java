package com.cap10mycap10.worklinkservice.model;


import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleBookingDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleDto;
import com.cap10mycap10.worklinkservice.enums.*;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Entity
@JsonIgnoreProperties({"hibernateLazyInitializer"})
@Data
public class Vehicle extends AbstractAuditingEntity{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String name;
    private String model;
    private String color;
    @Column(length = 800)
    private String mainPhoto;
    private String engineNumber;
    private Boolean airConditioning;
    private Float depositAmt;
    private String engineSize;
    private String trackerId;
    private String contactPerson;
    private String contactPhone;
    private String contactAddress;
    private String notes;
    private String regno;
    private Integer maxAge = 18;
    private Integer minAge = 65;

    private Integer seats;
    private Integer doors;
    private Integer minHireDays;

    private String description;
    @Column(nullable = false)
    private Boolean forRental = false;
    private Float hourlyRate;
    private Float mileageRate;
    private Float mileage = 0.0f;
    private Float maxDailyMileage;
    private Float excessMileageRate;
    private Boolean approved;

    @Enumerated(EnumType.STRING)
    private AssetStatus status = AssetStatus.AWAITING;

    @Enumerated(EnumType.STRING)
    private FuelType fuelType;
    @Enumerated(EnumType.STRING)
    private TransmissionType transmissionType;
    private Float capacity;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private VehicleType type = VehicleType.DEFAULT;

    // Tax-related fields
    private Boolean taxExempt = false; // Whether this vehicle is exempt from tax
    private Boolean taxInclusive; // Whether the rates are tax-inclusive (null = use agency default)

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "custom_tax_rate_id")
    private TaxRate customTaxRate; // Custom tax rate for this vehicle (null = use agency default)

    @ManyToOne(fetch = FetchType.LAZY)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @JoinColumn(nullable = false)
    private Agency agency;

    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @OneToMany(mappedBy = "vehicle", cascade = CascadeType.ALL, fetch = FetchType.EAGER, orphanRemoval = true)
    private Set<VehicleLocation> vehicleLocations = new HashSet<>();

    @JsonIgnore
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "vehicle", fetch = FetchType.EAGER)
    Set<Transport> transports;

    @JsonIgnore
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JoinColumn(name = "vehicle_id")
    @OneToMany( cascade = CascadeType.ALL)
    Set<VehicleRate> vehicleRates  = new HashSet<>();

    @JsonIgnore
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "vehicle", fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    Set<VehicleInventory> vehicleAddons= new HashSet<>();

    @JsonIgnore
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "vehicle", fetch = FetchType.EAGER)
    Set<VehicleBooking> vehicleBookings= new HashSet<>();


    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "vehicle", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    Set<VehicleDocument> vehicleDocuments =  new HashSet<>();


    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "vehicle", cascade = CascadeType.ALL, fetch = FetchType.EAGER, orphanRemoval = true)
    Set<VehicleInventory> vehicleInventories= new HashSet<>();


    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "vehicle", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    Set<VehiclePhoto> vehiclePhotos = new HashSet<>();

    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "vehicle")
    Set<VehicleAvailability> vehicleAvailabilities = new HashSet<>();

    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @ManyToMany(mappedBy = "vehicles", fetch = FetchType.EAGER,cascade = CascadeType.ALL)
    Set<Promotion> promotions = new HashSet<>();

    public Vehicle() {    }
    public Vehicle(VehicleDto vehicleDto) {
//        if(nonNull(vehicleDto.getId()))this.id = vehicleDto.getId();
        this.name = vehicleDto.getName();
        this.excessMileageRate = vehicleDto.getExcessMileageRate();
        this.notes = vehicleDto.getNotes();
        this.regno = vehicleDto.getRegno().strip();
        // Location assignment will be handled separately through VehicleLocation entities
        this.description = vehicleDto.getDescription();
        this.seats = vehicleDto.getSeats();
        this.doors = vehicleDto.getDoors();
        this.type = vehicleDto.getType();
        this.minHireDays = vehicleDto.getMinHireDays();
        this.agency = vehicleDto.getAgency();
        this.model = vehicleDto.getModel();
        this.color = vehicleDto.getColor();
        this.engineNumber = vehicleDto.getEngineNumber();
        this.airConditioning = vehicleDto.getAirConditioning();
        this.depositAmt = vehicleDto.getDepositAmt();
        this.engineSize = vehicleDto.getEngineSize();
        this.trackerId = vehicleDto.getTrackerId();
        this.contactPerson = vehicleDto.getContactPerson();
        this.contactPhone = vehicleDto.getContactPhone();
        this.contactAddress = vehicleDto.getContactAddress();

        // Note: VehicleDto now uses VehicleInventoryDto, so we can't directly assign or set vehicle references
        // The vehicleAddons will need to be handled separately when converting from DTO to entity
        if(nonNull(vehicleDto.getVehicleRates()))vehicleDto.getVehicleRates().forEach(e->e.setVehicle(this));

        // this.vehicleAddons = vehicleDto.getVehicleAddons(); // Cannot assign DTOs to entity collection
        this.vehicleRates = vehicleDto.getVehicleRates();
        this.maxAge = vehicleDto.getMaxAge();
        this.minAge = vehicleDto.getMinAge();
        this.maxDailyMileage = vehicleDto.getMaxDailyMileage();
        this.forRental = vehicleDto.getForRental();
        this.fuelType = vehicleDto.getFuelType();
        this.transmissionType = vehicleDto.getTransmissionType();
        this.capacity = vehicleDto.getCapacity();

    }




//
//    public void setVehicleRates(Set<VehicleRate> vehicleRatesIn) {
//
//        Set<WeekDay> requiredRates = Set.of(WeekDay.MTF, WeekDay.SAT, WeekDay.SUN);
//
//
//        requiredRates.forEach(day -> {
//            vehicleRatesIn.stream().filter(r-> r.getWeekDay()==day).findAny().orElseThrow(()-> new BusinessValidationException("Add a rate for " + day.getDayOfWeek()));
//        });
//
//        Map<WeekDay, VehicleRate> uniqueRates = new HashMap<>();
//
//
//
//        for (VehicleRate rate : vehicleRatesIn) {
//            uniqueRates.put(rate.getWeekDay(), new VehicleRate(rate));
//        }
//
//        Set<VehicleRate> deduplicatedRates = new HashSet<>(uniqueRates.values());
//
//        deduplicatedRates.forEach(r->r.setVehicle(this));
//        this.vehicleRates.clear();
//        this.vehicleRates.addAll(deduplicatedRates);
//    }


    public String getContactPerson() {
        return nonNull(contactPerson)? contactPerson: (nonNull(agency)?agency.getName():null);
    }

    public String getContactPhone() {
        return nonNull(contactPhone)? contactPhone : (nonNull(agency)?agency.getTelephone():null);
    }

    public String getContactAddress() {
        return nonNull(contactAddress)?contactAddress : ((nonNull(agency) && nonNull(agency.getAddress()))?agency.getAddress().toString():null);
    }

    public VehicleBooking book(VehicleBookingDto vehicleBookingDto) {
        return new VehicleBooking(vehicleBookingDto, this);
    }

    public void setRegno(String regno){
        this.regno = regno.replaceAll(" ", "");
    }

    /**
     * Get the primary location for this vehicle.
     * Returns the first active location, or the first location if no active ones exist.
     * Returns null if no locations are assigned.
     */
    public Location getLocation() {
        if (vehicleLocations == null || vehicleLocations.isEmpty()) {
            return null;
        }

        // First try to find an active location
        return vehicleLocations.stream()
                .filter(vl -> vl.getActive() != null && vl.getActive())
                .map(VehicleLocation::getLocation)
                .findFirst()
                .orElse(
                    // If no active location found, return the first location
                    vehicleLocations.stream()
                            .map(VehicleLocation::getLocation)
                            .findFirst()
                            .orElse(null)
                );
    }

    /**
     * Get all locations assigned to this vehicle.
     */
    public Set<Location> getLocations() {
        if (vehicleLocations == null) {
            return new HashSet<>();
        }
        return vehicleLocations.stream()
                .map(VehicleLocation::getLocation)
                .collect(Collectors.toSet());
    }
    public VehicleRate getRate(LocalDate d) {
        Optional<VehicleRate> rate;

        rate = this.vehicleRates.stream().filter(r->r.getWeekDay().getDayOfWeek() == d.getDayOfWeek()).findFirst();

        if(rate.isEmpty() && !Arrays.asList(DayOfWeek.SATURDAY, DayOfWeek.SUNDAY).contains(d.getDayOfWeek()))
            rate = this.vehicleRates.stream().filter(r->r.getWeekDay() == WeekDay.MTF).findFirst();

        if(rate.isEmpty())
            throw new BusinessValidationException("No rate has been set for this vehicle for "+d.getDayOfWeek());

        return rate.get();
    }

    public List<InvoiceItem> billClient(int minutes) {
        List<InvoiceItem> invoiceItems = new ArrayList<>();

        if(nonNull(hourlyRate)){
            InvoiceItem invoiceItem = new InvoiceItem();
            invoiceItem.setRate(BigDecimal.valueOf(hourlyRate));
            invoiceItem.setTotal(BigDecimal.valueOf(hourlyRate*minutes));
            invoiceItem.setDescription("Vehicle rate");
            invoiceItems.add(invoiceItem);
        }else{
            InvoiceItem invoiceItem = new InvoiceItem();
            invoiceItem.setRate(BigDecimal.valueOf(hourlyRate));
            invoiceItem.setTotal(BigDecimal.valueOf(hourlyRate*minutes));
            invoiceItem.setDescription("Vehicle rate");
            invoiceItems.add(invoiceItem);
        }

        return invoiceItems;

    }

    public void approve() {
        status =  AssetStatus.AVAILABLE;
    }
    public void enable() {
        status =  AssetStatus.AVAILABLE;
    }
    public void disable() {
        status =  AssetStatus.DISABLED;
    }

    public void reject() {
        status =  AssetStatus.REJECTED;
    }

    public void sendForAdminReview() {
        if(List.of(AssetStatus.REJECTED, AssetStatus.AWAITING).contains(status))
          this.status = AssetStatus.AWAITING;
        else
          this.status = AssetStatus.EDITED;
    }

    public ZoneId getZone() {
        // Get timezone from the first active location, or default to UTC
        return vehicleLocations.stream()
                .filter(vl -> vl.getActive())
                .findFirst()
                .map(vl -> ZoneId.of(vl.getLocation().getTimeZone()))
                .orElse(ZoneId.of("UTC"));
    }

    /**
     * Get all active locations for this vehicle
     */
    public Set<Location> getActiveLocations() {
        return vehicleLocations.stream()
                .filter(vl -> vl.getActive())
                .map(VehicleLocation::getLocation)
                .collect(Collectors.toSet());
    }

    /**
     * Get all locations (active and inactive) for this vehicle
     */
    public Set<Location> getAllLocations() {
        return vehicleLocations.stream()
                .map(VehicleLocation::getLocation)
                .collect(Collectors.toSet());
    }

    /**
     * Check if this vehicle is available at a specific location
     */
    public boolean isAvailableAtLocation(Long locationId) {
        return vehicleLocations.stream()
                .anyMatch(vl -> vl.getActive() && vl.getLocation().getId().equals(locationId));
    }

    /**
     * Add a location to this vehicle
     */
    public void addLocation(Location location) {
        VehicleLocation vehicleLocation = new VehicleLocation(this, location);
        this.vehicleLocations.add(vehicleLocation);
    }

    /**
     * Add a location to this vehicle with specific active status
     */
    public void addLocation(Location location, Boolean active) {
        VehicleLocation vehicleLocation = new VehicleLocation(this, location, active);
        this.vehicleLocations.add(vehicleLocation);
    }

    /**
     * Remove a location from this vehicle
     */
    public void removeLocation(Long locationId) {
        vehicleLocations.removeIf(vl -> vl.getLocation().getId().equals(locationId));
    }

    /**
     * Activate/deactivate a vehicle at a specific location
     */
    public void setLocationActive(Long locationId, Boolean active) {
        vehicleLocations.stream()
                .filter(vl -> vl.getLocation().getId().equals(locationId))
                .findFirst()
                .ifPresent(vl -> vl.setActive(active));
    }




    public Set<VehicleInventory> getVehicleAddons() {
        if(!nonNull(vehicleAddons)) vehicleAddons = new HashSet<>();
        return vehicleAddons.stream().filter(v->nonNull(v.getPrice())).collect(Collectors.toSet());
    }

    public Set<VehicleInventory> getVehicleInventories() {
        return vehicleInventories;
    }

    public Set<VehicleRate> getVehicleRates() {
        Set<WeekDay> requiredRates = Set.of(WeekDay.MTF, WeekDay.SAT, WeekDay.SUN);
        Set<VehicleRate> uniqueRates = new HashSet<>();
        if(nonNull(vehicleRates)) requiredRates.forEach(day -> {
            Optional<VehicleRate> rate = vehicleRates.stream().filter(r -> r.getWeekDay() == day).findFirst();
            rate.ifPresent(uniqueRates::add);
        });
        return uniqueRates;
    }

//        public Promotion getLatestValidPublicPromotion() {
//            return promotions.stream()
//                    .filter(promotion -> promotion.getStatus()==Status.ACTIVE) // Check if promotion is active
//                    .filter(promotion -> isNull(promotion.getCode())) // Check if promotion is active
//                    .filter(promotion -> promotion.getExpiryDate() == null || promotion.getExpiryDate().isAfter(ZonedDateTime.now())) // Check if promotion has not expired
//                    .min(Comparator.comparing(Promotion::getId)) // Get the latest promotion
//                    .orElse(null);
//        }




    public void validatePromo(Promotion promotion, VehicleBooking booking) {
        if (!promotion.getAdminDiscount()) {
            if(isNull(promotion.getAgency()))
                throw new BusinessValidationException("Promotion is not valid, it is not for any provider");
            if ((this.getAgency().getId().longValue() != promotion.getAgency().getId().longValue()))
                throw new BusinessValidationException("Promotion is not valid for this vehicle");
        }

        if(promotion.getExpiryDate().isBefore(ZonedDateTime.now()))
            throw new BusinessValidationException("Promotion ended");
        if(promotion.getUsageLimit()<= promotion.getUsageCount())
            throw new BusinessValidationException("Promotion has ended");
        if(promotion.getStatus()==Status.INACTIVE)
            throw new BusinessValidationException("Promotion is inactive");
        if(promotion.getDaysHired()>=booking.getDaysHired())
            throw new BusinessValidationException("You need to book for "+ promotion.getDaysHired()+" days for promotion to apply");
    }
}
