package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.model.*;
import java.math.BigDecimal;

public interface TaxCalculationService {

    /**
     * Calculate tax for a vehicle booking item
     * @param vehicle The vehicle being booked
     * @param rate The daily rate
     * @param agencySettings The agency's tax settings
     * @return TaxCalculationResult containing tax details
     */
    TaxCalculationResult calculateVehicleTax(Vehicle vehicle, BigDecimal rate, AgencySettings agencySettings);

    /**
     * Calculate tax for a vehicle addon item
     * @param addon The vehicle addon
     * @param rate The addon rate
     * @param agencySettings The agency's tax settings
     * @return TaxCalculationResult containing tax details
     */
    TaxCalculationResult calculateAddonTax(VehicleInventory addon, BigDecimal rate, AgencySettings agencySettings);

    /**
     * Apply tax calculation to an invoice item
     * @param invoiceItem The invoice item to calculate tax for
     * @param vehicle The vehicle (if applicable)
     * @param addon The addon (if applicable)
     * @param agencySettings The agency's tax settings
     */
    void applyTaxToInvoiceItem(InvoiceItem invoiceItem, Vehicle vehicle, VehicleInventory addon, AgencySettings agencySettings);

    /**
     * Get the effective tax rate for a vehicle
     * @param vehicle The vehicle
     * @param agencySettings The agency's tax settings
     * @return The tax rate to apply
     */
    BigDecimal getEffectiveVehicleTaxRate(Vehicle vehicle, AgencySettings agencySettings);

    /**
     * Get the effective tax rate for an addon
     * @param addon The addon
     * @param agencySettings The agency's tax settings
     * @return The tax rate to apply
     */
    BigDecimal getEffectiveAddonTaxRate(VehicleInventory addon, AgencySettings agencySettings);

    /**
     * Check if a vehicle is tax exempt
     * @param vehicle The vehicle
     * @param agencySettings The agency's tax settings
     * @return true if tax exempt
     */
    boolean isVehicleTaxExempt(Vehicle vehicle, AgencySettings agencySettings);

    /**
     * Check if an addon is tax exempt
     * @param addon The addon
     * @param agencySettings The agency's tax settings
     * @return true if tax exempt
     */
    boolean isAddonTaxExempt(VehicleInventory addon, AgencySettings agencySettings);

    /**
     * Determine if pricing is tax inclusive for a vehicle
     * @param vehicle The vehicle
     * @param agencySettings The agency's tax settings
     * @return true if tax inclusive
     */
    boolean isVehiclePricingTaxInclusive(Vehicle vehicle, AgencySettings agencySettings);

    /**
     * Determine if pricing is tax inclusive for an addon
     * @param addon The addon
     * @param agencySettings The agency's tax settings
     * @return true if tax inclusive
     */
    boolean isAddonPricingTaxInclusive(VehicleInventory addon, AgencySettings agencySettings);

    /**
     * Result class for tax calculations
     */
    class TaxCalculationResult {
        private final BigDecimal netAmount;
        private final BigDecimal taxAmount;
        private final BigDecimal totalAmount;
        private final BigDecimal taxRate;
        private final boolean taxExempt;
        private final boolean taxInclusive;

        public TaxCalculationResult(BigDecimal netAmount, BigDecimal taxAmount, BigDecimal totalAmount, 
                                  BigDecimal taxRate, boolean taxExempt, boolean taxInclusive) {
            this.netAmount = netAmount;
            this.taxAmount = taxAmount;
            this.totalAmount = totalAmount;
            this.taxRate = taxRate;
            this.taxExempt = taxExempt;
            this.taxInclusive = taxInclusive;
        }

        // Getters
        public BigDecimal getNetAmount() { return netAmount; }
        public BigDecimal getTaxAmount() { return taxAmount; }
        public BigDecimal getTotalAmount() { return totalAmount; }
        public BigDecimal getTaxRate() { return taxRate; }
        public boolean isTaxExempt() { return taxExempt; }
        public boolean isTaxInclusive() { return taxInclusive; }
    }
}
