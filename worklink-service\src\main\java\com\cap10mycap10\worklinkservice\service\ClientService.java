package com.cap10mycap10.worklinkservice.service;


import com.cap10mycap10.worklinkservice.dto.agency.AgencyResultDto;
import com.cap10mycap10.worklinkservice.dto.client.*;
import com.cap10mycap10.worklinkservice.dto.shift.IShiftReportStatus;
import com.cap10mycap10.worklinkservice.dto.worker.WorkerResultDto;
import com.cap10mycap10.worklinkservice.feigndtos.feigndtos.response.UserResponse;
import com.cap10mycap10.worklinkservice.model.Agency;
import com.cap10mycap10.worklinkservice.model.Client;
import com.cap10mycap10.worklinkservice.model.ClientDocs;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Set;

public interface ClientService {

    ClientDto save(ClientDto clientDto) throws JsonProcessingException;

//    void save(ClientUpdateDto clientUpdateDto);

    Client saveClient(ClientDto clientDto);

    @Transactional
    ClientDto update(ClientDto clientDto);

    ClientDto findById(Long id);

    List<ClientDto> findAll();

    Page<ClientDto> findAllPaged(String searchQuery, PageRequest of);

    Page<ClientDto> findAllPaged(String searchQuery, PageRequest pageRequest, Long agencyId);

    Page<ClientDto> findAllPagedClients(Long workerId, PageRequest of);

    Page<ClientDto> searchClientForAgency(Long agencyId, String searchCriteria, PageRequest of);

    void deleteById(Long id);

    Client getOne(Long clientId);

    void addAgency(Long clientId, Long agencyId);

    Page<AgencyResultDto> findAllAgenciesPaged(Long clientId, PageRequest of);

    int findNumberOfClients();


    IShiftReportStatus getStats(Long id);

    ClientStats getMyStats(Long id);

    void linkAgentClient(Long agentId, Long clientId);

    UserResponse findAdminById(Long id);

    Page<WorkerResultDto> findAllWorkers(Long clientId, PageRequest of);



    void addProfilePic(Long clientId, MultipartFile file);

    void addClientDocs(Long id, Set<ClientDocs> clientDocs);
}
