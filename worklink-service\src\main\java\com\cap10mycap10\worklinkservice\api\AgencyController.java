package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.InviteWorkerRequestDto;
import com.cap10mycap10.worklinkservice.dto.agency.AgencyCreateDto;
import com.cap10mycap10.worklinkservice.dto.agency.AgencyResultDto;
import com.cap10mycap10.worklinkservice.dto.agency.AgencyStats;
import com.cap10mycap10.worklinkservice.dto.agency.AgencyUpdateDto;
import com.cap10mycap10.worklinkservice.dto.agencyworkerproperties.AgencyWorkerPropertiesCreateDto;
import com.cap10mycap10.worklinkservice.dto.agencyworkerproperties.IAgencyWorkerProperties;
import com.cap10mycap10.worklinkservice.dto.client.ClientDto;
import com.cap10mycap10.worklinkservice.dto.shift.BookingResultDto;
import com.cap10mycap10.worklinkservice.dto.worker.WorkerResultDto;
import com.cap10mycap10.worklinkservice.enums.AgencyType;
import com.cap10mycap10.worklinkservice.model.AgencyWorkerProperties;
import com.cap10mycap10.worklinkservice.model.BankDetails;
//import com.cap10mycap10.worklinkservice.search.AgencySearchService;
import com.cap10mycap10.worklinkservice.search.AgencySearchService;
import com.cap10mycap10.worklinkservice.service.AgencyService;
import com.cap10mycap10.worklinkservice.service.AgencyWorkerPropertiesService;
import com.cap10mycap10.worklinkservice.service.AuthenticationFacadeService;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import javax.validation.Valid;
import java.net.URI;
import java.time.LocalDate;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
@Transactional
@Validated
public class AgencyController {

    private final AgencyService agencyService;
    @Autowired
    private AgencySearchService agencySearchService;
    @Autowired
    private AuthenticationFacadeService authenticationFacadeService;
    private final AgencyWorkerPropertiesService agencyWorkerPropertiesService;
    public AgencyController(final AgencyService agencyService,   final AgencyWorkerPropertiesService agencyWorkerPropertiesService) {
        this.agencyService = agencyService;
        this.agencyWorkerPropertiesService = agencyWorkerPropertiesService;
    }

   public static final String ROLE_ADMIN_ADMIN = "ROLE_ADMIN_ADMIN";


    //   @Secured({ROLE_ADMIN_ADMIN})
    @PostMapping(value = "agency", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> create(@Valid @RequestBody AgencyCreateDto agencyCreateDto) throws JsonProcessingException {
        log.info("Request to add Agency: {}", agencyCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        agencyService.save(agencyCreateDto);
        return ResponseEntity.created(uri)
                .build();
    }




   /* @ViewAgency*/
//   @Secured({ROLE_ADMIN_ADMIN})
   @GetMapping(value = "agency/{id}")
    public ResponseEntity<AgencyResultDto> findById(@PathVariable("id") Long id) {
       log.info(String.format("received request to list user %s", authenticationFacadeService.getAuthentication().getPrincipal()));
       log.info(String.format("received request to list user %s", authenticationFacadeService.getAuthentication()));

       log.info("Request to get agency with id: {}", id);
        return ResponseEntity.ok(agencyService.findById(id));
    }

    @GetMapping(value = "agency/transporter/{id}")
    public ResponseEntity<List<AgencyResultDto>> findByIsTransporter(@PathVariable("id") Boolean isTransporter) {
        log.info(String.format("received request to all transporters which are %s transporters", isTransporter));

        return ResponseEntity.ok(agencyService.findByIsTransporter(isTransporter));
    }


    @GetMapping(value = "agencies/{page}/{size}")
    public ResponseEntity<Page<AgencyResultDto>> findAll(
            @PathVariable("page") int page,
            @PathVariable("size") int size,
            @RequestParam(value = "searchQuery", required = false) String searchQuery,
            @RequestParam(value = "sort", defaultValue = "name") String sort,
            @RequestParam(value = "direction", defaultValue = "ASC") String direction,
            @RequestParam(required = false) AgencyType agencyTypeFilter
    ) {
        log.info("Request to get agencies: page={}, size={}, searchQuery={}, sort={}, direction={}, agencyTypeFilter={}",
                page, size, searchQuery, sort, direction, agencyTypeFilter);

        org.springframework.data.domain.Sort.Direction sortDirection =
                direction.equalsIgnoreCase("DESC") ?
                org.springframework.data.domain.Sort.Direction.DESC :
                org.springframework.data.domain.Sort.Direction.ASC;

        PageRequest pageRequest = PageRequest.of(page, size, sortDirection, sort);


            return ResponseEntity.ok(agencyService.findAllPaged(agencyTypeFilter,searchQuery, pageRequest));

    }

    /**
     * @deprecated Use the query parameter version instead: /api/v1/agencies?page=0&size=20
     */
//    @Deprecated
//    @GetMapping(value = "agencies/{page}/{size}")
//    public ResponseEntity<Page<AgencyResultDto>> findAllPathVariable(@PathVariable("page") int page,
//                                                          @PathVariable("size") int size,
//                                                          @RequestParam(required=false) AgencyType agencyTypeFilter) {
//        log.info("Request to get agencies (deprecated path): {}, {}", page, size);
//        return findAll(page, size, null, "name", "ASC", agencyTypeFilter);
//    }

    @GetMapping(value = "agencies/default/{page}/{size}")
    public ResponseEntity<Page<AgencyResultDto>> findDefaultAgencies(@PathVariable("page") int page,
                                                          @PathVariable("size") int size) {
        log.info("Request to get agencies: {}, {}", page, size);
        return ResponseEntity.ok(agencyService.findAllDefaultPaged(PageRequest.of(page, size)));
    }

    /*@ViewAgency*/
    @GetMapping(value = "agency-workers/{agencyId}/{page}/{size}")
    public ResponseEntity<Page<WorkerResultDto>> findAllWorkers(@PathVariable("agencyId") Long agencyId,
                                                                @PathVariable("page") int page,
                                                                @PathVariable("size") int size) {
        log.info("Request to view agency workers: {}, {}, {}", agencyId, page, size);
        return ResponseEntity.ok(agencyService.findAllWorkersPaged(agencyId, PageRequest.of(page, size)));
    }

    @GetMapping(value = "agency-applicants/{agencyId}/{page}/{size}")
    public ResponseEntity<Page<WorkerResultDto>> findAllApplicants(@PathVariable("agencyId") Long agencyId,
                                                        @PathVariable("page") int page,
                                                        @PathVariable("size") int size) {
        log.info("Request to view agency workers: {}, {}, {}", agencyId, page, size);
        return ResponseEntity.ok(agencyService.findAllApplicantsPaged(agencyId, PageRequest.of(page, size)));
    }

    @GetMapping(value = "agency-applicants/{agencyId}/{assignmentCodeName}/{page}/{size}")
    public ResponseEntity<Page<WorkerResultDto>> findAllWorkersByCode(@PathVariable("agencyId") Long agencyId,
                                                              @PathVariable("assignmentCodeName") String assignmentCode,
                                                           @PathVariable("page") int page,
                                                           @PathVariable("size") int size) {
        log.info("Request to view agency workers with agencyId: {} assignmentCodeName: {} of page: {} and size: {}", agencyId, assignmentCode,  page, size);
        return ResponseEntity.ok(agencyService.findAllApplicantsByCode(agencyId, assignmentCode, PageRequest.of(page, size)));
    }

    /*@ViewAgency*/
    @GetMapping(value = "agency-workers/pending-shifts/{agencyId}/{page}/{size}")
    public ResponseEntity<Page<WorkerResultDto>> findAllWorkersPendingShifts(@PathVariable("agencyId") Long agencyId,
                                                        @PathVariable("page") int page,
                                                        @PathVariable("size") int size) {
        log.info("Request to view agency workers: {}, {}, {}", agencyId, page, size);
        return ResponseEntity.ok(agencyService.findAllWorkersPagedPending(agencyId, PageRequest.of(page, size)));
    }


    /*@UpdateAgency*/
    @PutMapping(value = "agency", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> update(@RequestBody AgencyUpdateDto agencyUpdateDto) {
        log.info("Request to edit agency: {}", agencyUpdateDto);
        agencyService.save(agencyUpdateDto);
        return ResponseEntity.ok().build();
    }
    /*@UpdateAgencyBankDetails*/
    @PutMapping(value = "agency/bank-details/{agencyId}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity updateBankDetails(@RequestBody BankDetails bankDetails,
                                            @PathVariable("agencyId") Long agencyId) {
        log.info("Request to edit agency: {}", bankDetails);
        agencyService.saveBankDetails(agencyId, bankDetails);
        return ResponseEntity.ok().build();
    }

    @PutMapping(value = "agency/base-currency/{agencyId}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> updateBaseCurrency(@RequestBody UpdateBaseCurrencyRequest request,
                                           @PathVariable("agencyId") Long agencyId) {
        log.info("Request to update agency base currency: {} for agency: {}", request.getBaseCurrency(), agencyId);
        agencyService.updateBaseCurrency(agencyId, request.getBaseCurrency());
        return ResponseEntity.ok().build();
    }

    @PutMapping(value = "agency-activate/{id}")
    public ResponseEntity<Object> activateAgency(@PathVariable("id") Long id) {
        log.info("Request to activate agency: {}", id);
        agencyService.activate(id);
        return ResponseEntity.ok().build();
    }

    @PutMapping(value = "agency-deactivate/{id}")
    public ResponseEntity<Object> deActivateAgency(@PathVariable("id") Long id) {
        log.info("Request to deactivate agency: {}", id);
        agencyService.deactivate(id);
        return ResponseEntity.ok().build();
    }


    /*@UpdateAgency*/
    @PutMapping(value = "remove-worker/{shiftId}/{workerId}")
    public ResponseEntity<BookingResultDto> removeWorker(@PathVariable("shiftId") Long shiftId,
                                                         @PathVariable("workerId") Long workerId) {
        log.info("Request to edit worker-shift: {}, {}",shiftId, workerId );
        return ResponseEntity.ok(agencyService.removeWorker(shiftId, workerId));
    }


    /*@ViewAgency*/
    @GetMapping(value = "agencies")
    public ResponseEntity<List<AgencyResultDto>> findAgencies() {
        log.info("Request to get all agencies");
        return ResponseEntity.ok(agencyService.findAllShifts());
    }

    /*@ViewAgency*/
    @GetMapping(value = "trainers")
    public ResponseEntity<List<AgencyResultDto>> findTrainers() {
        log.info("Request to get all trainers");
        return ResponseEntity.ok(agencyService.findTrainers());
    }

   /* @ViewAgencyDashboard*/
    @GetMapping(value = "agency-dashboard")
    public ResponseEntity<Integer> findNumberOfAgencies() {
        log.info("Request for agency dashboard");
        return ResponseEntity.ok(agencyService.findNumberOfAgencies());
    }


    /* @ViewShift*/
    @GetMapping(value = "agency-shifts-status/{agencyId}/{page}/{size}/{status}")
    public ResponseEntity<Page<BookingResultDto>> findShiftsByAgencyId(@PathVariable("agencyId") Long agencyId,
                                                                       @PathVariable("page") int page,
                                                                       @PathVariable("size") int size,
                                                                       @PathVariable("status") String status,
                                                                       @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
                                                                       @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
                                                                       @RequestParam(value = "payerId", required = false) Long clientId) {
        log.info("###########Request for agency shifts {} {} {} {}", agencyId, page, size, status);

        return ResponseEntity.ok(agencyService.findAllShiftsAgencyPagedByStatus(agencyId, PageRequest.of(page, size), status, clientId, startDate, endDate));

    }
//
//    @GetMapping(value = "admin-agency-shifts-status/{agencyId}/{page}/{size}/{status}")
//    public ResponseEntity<Page<BookingResultDto>> findAgencyShiftsForAdmin(@PathVariable("agencyId") Long agencyId,
//                                                                     @PathVariable("page") int page,
//                                                                     @PathVariable("size") int size,
//                                                                     @PathVariable("status") String status) {
//        log.info("###########Request for agency shifts {} {} {} {}", agencyId, page, size, status);
//        return ResponseEntity.ok(agencyService.findAdminBillingShifts(agencyId, PageRequest.of(page, size), status));
//    }

    @GetMapping(value = "admin-billing-shifts")
    public ResponseEntity<Page<BookingResultDto>> findAdminBilling(@RequestParam("page") Integer page,
                                                                   @RequestParam("size") Integer size,
                                                                   @RequestParam(value = "agencyId", required = false) Long agencyId,
                                                                   @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
                                                                   @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate
                                                                 ) {
        log.info("Request for agency shifts for admin billing {} {} {}", agencyId, page, size);



        if(startDate==null || endDate==null){
            return ResponseEntity.ok(agencyService.findAdminBillingShifts(agencyId, PageRequest.of(page, size)));
        }else{
            return ResponseEntity.ok(agencyService.findAdminBillingShifts(agencyId,startDate, endDate, PageRequest.of(page, size)));

        }
    }



    /* @ViewShift*/
    @GetMapping(value = "agency-shifts-released/{agencyId}/{page}/{size}")
    public ResponseEntity<Page<BookingResultDto>> findShiftsByClientId(@PathVariable("agencyId") Long agencyId,
                                                                       @PathVariable("page") int page,
                                                                       @PathVariable("size") int size) {
        log.info("###########Request for agency shifts {} {} {}", agencyId, page, size);
        return ResponseEntity.ok(agencyService.findAllReleasedShift(agencyId, PageRequest.of(page, size)));
    }



    /*@ViewShift*/
    @GetMapping(value = "agency-shifts/{agencyId}/{page}/{size}")
    public ResponseEntity<Page<BookingResultDto>> findShiftsByAgencyIdAndStatus(@PathVariable("agencyId") Long agencyId,
                                                                                @PathVariable("page") int page,
                                                                                @PathVariable("size") int size,
                                                                                @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
                                                                                @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
                                                                                @RequestParam(required = false) String status) {
        log.info("#######Request for agency shifts {} {} {}", agencyId, page, size);
        if (status.length()==0){
            return ResponseEntity.ok(agencyService.findAllShiftsAgencyPaged(agencyId, PageRequest.of(page, size), startDate, endDate));
        }else{
            return ResponseEntity.ok(agencyService.findAllShiftsAgencyPaged(agencyId, PageRequest.of(page, size), startDate, endDate, status));
        }

    }

   /* @DeleteAgency*/
    @DeleteMapping(value = "agency/{id}")
    public ResponseEntity delete(@PathVariable("id") Long id) {
        log.info("Request to delete agency id : {}", id);
        agencyService.deleteById(id);
        return ResponseEntity.noContent().build();
    }

   /* @ViewAgency*/
    @PostMapping("agency-search")
    public ResponseEntity<Page<AgencyResultDto>> searchAgency(
            @RequestParam(value = "searchCriteria", required = false) String searchCriteria,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "20") int size,
            @RequestParam(value = "sort", defaultValue = "name") String sort,
            @RequestParam(value = "direction", defaultValue = "ASC") String direction) {

        if(searchCriteria == null || searchCriteria.replaceAll("\\s+","").isEmpty()){
            return ResponseEntity.badRequest().build();
        }
        return  null;

//        org.springframework.data.domain.Sort.Direction sortDirection =
//                direction.equalsIgnoreCase("DESC") ?
//                org.springframework.data.domain.Sort.Direction.DESC :
//                org.springframework.data.domain.Sort.Direction.ASC;
//
//        PageRequest pageRequest = PageRequest.of(page, size, sortDirection, sort);
//
//        return ResponseEntity.ok(agencySearchService.fuzzySearch(searchCriteria, page, size, pageRequest));
    }

    /**
     * @deprecated Use the query parameter version instead: /api/v1/agency-search?searchCriteria=value&page=0&size=20
     */
    @Deprecated
    @PostMapping("agency-search/{page}/{size}")
    public ResponseEntity<Page<AgencyResultDto>> searchAgencyPathVariable(
            @RequestParam(value = "searchCriteria", required = false) String searchCriteria,
            @PathVariable int page, @PathVariable int size) {

        if(searchCriteria == null || searchCriteria.replaceAll("\\s+","").isEmpty()){
            return ResponseEntity.badRequest().build();
        }

        return searchAgency(searchCriteria, page, size, "name", "ASC");
    }

    /*@ViewAgencyDashboard*/
    @GetMapping("agency-stats/{id}")
    public ResponseEntity<AgencyStats> getAgencyStats(@PathVariable("id") Long id){
        return ResponseEntity.ok(agencyService.getStats(id));
    }

    /**
     * Get agency information by agent ID (for user-service integration)
     */
    @GetMapping("agents/{agentId}/agency")
    public ResponseEntity<AgencyResultDto> getAgencyByAgentId(@PathVariable("agentId") Long agentId) {
        log.info("Request to get agency for agent ID: {}", agentId);
        AgencyResultDto agency = agencyService.findAgencyByAgentId(agentId);
        if (agency != null) {
            return ResponseEntity.ok(agency);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Get agency information by client ID (for user-service integration)
     */
    @GetMapping("clients/{clientId}/agency")
    public ResponseEntity<AgencyResultDto> getAgencyByClientId(@PathVariable("clientId") Long clientId) {
        log.info("Request to get agency for client ID: {}", clientId);
        AgencyResultDto agency = agencyService.findAgencyByClientId(clientId);
        if (agency != null) {
            return ResponseEntity.ok(agency);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Get agency information by worker ID (for user-service integration)
     */
    @GetMapping("workers/{workerId}/agency")
    public ResponseEntity<AgencyResultDto> getAgencyByWorkerId(@PathVariable("workerId") Long workerId) {
        log.info("Request to get agency for worker ID: {}", workerId);
        AgencyResultDto agency = agencyService.findAgencyByWorkerId(workerId);
        if (agency != null) {
            return ResponseEntity.ok(agency);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping(value = "agency-worker-properties", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<AgencyWorkerProperties> createorUpdateAgencyWorkerProperties(@RequestBody AgencyWorkerPropertiesCreateDto agencyWorkerPropertiesCreateDto) {
        log.info("Request to add Agency-Worker Properties with : {}", agencyWorkerPropertiesCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        agencyWorkerPropertiesService.addAgencyWorkerProperties(agencyWorkerPropertiesCreateDto);
        return ResponseEntity.created(uri)
                .build();
    }

    @PutMapping(value = "agency-worker-properties/activate-worker", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<AgencyWorkerProperties> activateWorker(@RequestBody AgencyWorkerPropertiesCreateDto agencyWorkerPropertiesCreateDto) {
        log.info("Request to add Agency-Worker Properties with : {}", agencyWorkerPropertiesCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        agencyWorkerPropertiesService.activateAgencyWorker(agencyWorkerPropertiesCreateDto);
        return ResponseEntity.created(uri)
                .build();
    }

    @PutMapping(value = "agency-worker-properties/deactivate-applicant", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> deactivateApplicant(@RequestBody AgencyWorkerPropertiesCreateDto agencyWorkerPropertiesCreateDto) {
        log.info("Request to add Agency-Worker Properties with : {}", agencyWorkerPropertiesCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        agencyWorkerPropertiesService.deactivateAgencyApplicant(agencyWorkerPropertiesCreateDto);
        return ResponseEntity.created(uri)
                .build();
    }





    @PutMapping(value = "agency-worker-properties/activate-applicant", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<AgencyWorkerProperties> activateApplicant(@RequestBody AgencyWorkerPropertiesCreateDto agencyWorkerPropertiesCreateDto) {
        log.info("Request to add Agency-Worker Properties with : {}", agencyWorkerPropertiesCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        agencyWorkerPropertiesService.activateAgencyApplicant(agencyWorkerPropertiesCreateDto);
        return ResponseEntity.created(uri)
                .build();
    }

    @PutMapping(value = "agency-worker-properties/deactivate-worker", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<AgencyWorkerProperties> deactivateWorker(@RequestBody AgencyWorkerPropertiesCreateDto agencyWorkerPropertiesCreateDto) {
        log.info("Request to add Agency-Worker Properties with : {}", agencyWorkerPropertiesCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        agencyWorkerPropertiesService.deactivateAgencyWorker(agencyWorkerPropertiesCreateDto);
        return ResponseEntity.created(uri)
                .build();
    }



    @PostMapping(value = "agency/invite-worker", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> inviteWorker(@RequestBody InviteWorkerRequestDto inviteWorkerRequestDto) {
        log.info("Request to invite worker with : {}", inviteWorkerRequestDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        agencyWorkerPropertiesService.inviteWorker(inviteWorkerRequestDto);
        return ResponseEntity.created(uri).build();
    }







    @GetMapping(value = "agency-worker-properties/{workerId}/{agencyId}")
    public ResponseEntity<IAgencyWorkerProperties> findAgencyWorkerProperties(@PathVariable("workerId") Long workerId, @PathVariable("agencyId") Long agencyId) {
        log.info("Request to get Agency-Worker Properties with workerId : {}, and agencyId: {}", workerId, agencyId);
        return ResponseEntity.ok(agencyWorkerPropertiesService.findProperties(workerId, agencyId));
    }


    @DeleteMapping(value = "agency-worker-properties/{id}")
    public ResponseEntity<Object> deleteAgencyWorkerProperties(@PathVariable("id") Long id) {
        agencyWorkerPropertiesService.deleteAgencyWorkerProperties(id);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/agency/logo")
    public ResponseEntity uploadProfileImage(@RequestParam("file") MultipartFile file,
                                                 @RequestParam("agencyId") Long agencyId
    ) {

        log.info("Request to add worker profile image : {}");
        agencyService.addLogo( agencyId, file);
        return  ResponseEntity.noContent().build();
    }

    // Request DTO for updating base currency
    public static class UpdateBaseCurrencyRequest {
        private String baseCurrency;

        public String getBaseCurrency() {
            return baseCurrency;
        }

        public void setBaseCurrency(String baseCurrency) {
            this.baseCurrency = baseCurrency;
        }
    }
}
