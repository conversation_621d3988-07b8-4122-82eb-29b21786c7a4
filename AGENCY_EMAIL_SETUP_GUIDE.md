# Agency Email Configuration Setup Guide

This guide walks through setting up agency-specific email configuration for white-label email sending.

## Prerequisites

1. Agency must exist in the system
2. SMTP server credentials for the agency
3. Admin or Agency Admin role permissions

## Step-by-Step Setup

### 1. Prepare SMTP Information

Gather the following information from your email provider:

- **SMTP Host**: e.g., `smtp.gmail.com`, `smtp.hostinger.com`
- **SMTP Port**: Usually `587` (TLS) or `465` (SSL)
- **Username**: Usually the email address
- **Password**: App password or account password
- **From Email**: The email address emails will be sent from
- **From Name**: The company/agency name
- **Support Email**: Customer support email address
- **Website URL**: Agency website
- **Logo URL**: Direct link to agency logo image

### 2. Create Email Configuration

Use the REST API to create the configuration:

```bash
curl -X POST "http://localhost:8080/api/v1/agency-email-config/agency/123" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "smtpHost": "smtp.gmail.com",
    "smtpPort": 587,
    "smtpUsername": "<EMAIL>",
    "smtpPassword": "your-app-password",
    "smtpAuth": true,
    "smtpStarttlsEnable": true,
    "smtpStarttlsRequired": true,
    "smtpSslEnable": false,
    "fromEmail": "<EMAIL>",
    "fromName": "Premium Car Rentals",
    "replyToEmail": "<EMAIL>",
    "supportEmail": "<EMAIL>",
    "websiteUrl": "https://premiumrentals.com",
    "logoUrl": "https://premiumrentals.com/assets/logo.png",
    "isActive": true
  }'
```

### 3. Test the Configuration

Test the email configuration to ensure it works:

```bash
curl -X POST "http://localhost:8080/api/v1/agency-email-config/test" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "agencyId": 123,
    "testEmailAddress": "<EMAIL>"
  }'
```

Expected response:
```json
{
  "success": true,
  "message": "Test email sent successfully"
}
```

### 4. Verify Configuration Status

Check the configuration status:

```bash
curl -X GET "http://localhost:8080/api/v1/agency-email-config/agency/123/status" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Expected response:
```json
{
  "agencyId": 123,
  "hasConfiguration": true,
  "hasActiveConfiguration": true,
  "hasVerifiedConfiguration": true,
  "isUsingCustomEmail": true
}
```

## Common SMTP Provider Settings

### Gmail
```json
{
  "smtpHost": "smtp.gmail.com",
  "smtpPort": 587,
  "smtpAuth": true,
  "smtpStarttlsEnable": true,
  "smtpStarttlsRequired": true,
  "smtpSslEnable": false
}
```

**Note**: Use App Passwords for Gmail, not your regular password.

### Microsoft Outlook/Office 365
```json
{
  "smtpHost": "smtp-mail.outlook.com",
  "smtpPort": 587,
  "smtpAuth": true,
  "smtpStarttlsEnable": true,
  "smtpStarttlsRequired": true,
  "smtpSslEnable": false
}
```

### Hostinger
```json
{
  "smtpHost": "smtp.hostinger.com",
  "smtpPort": 587,
  "smtpAuth": true,
  "smtpStarttlsEnable": true,
  "smtpStarttlsRequired": true,
  "smtpSslEnable": false
}
```

### Custom SMTP with SSL
```json
{
  "smtpHost": "mail.yourdomain.com",
  "smtpPort": 465,
  "smtpAuth": true,
  "smtpStarttlsEnable": false,
  "smtpStarttlsRequired": false,
  "smtpSslEnable": true,
  "smtpSslSocketFactoryClass": "javax.net.ssl.SSLSocketFactory"
}
```

## Verification Checklist

- [ ] Configuration saved successfully
- [ ] Test email sent and received
- [ ] Configuration marked as verified
- [ ] Agency emails now use custom branding
- [ ] Fallback to default works when configuration is disabled

## Troubleshooting

### Authentication Failed
- Verify username and password
- For Gmail, ensure you're using an App Password
- Check if 2FA is enabled and configured correctly

### Connection Timeout
- Verify SMTP host and port
- Check firewall settings
- Ensure network connectivity to SMTP server

### SSL/TLS Errors
- Try different port (587 for TLS, 465 for SSL)
- Adjust SSL/TLS settings
- Check certificate validity

### Emails Not Branded
- Ensure configuration is active (`isActive: true`)
- Verify configuration is verified (`isVerified: true`)
- Check that `isReadyToUse()` returns true
- Clear email cache if needed

### Test Email Not Received
- Check spam/junk folder
- Verify test email address is correct
- Check SMTP server logs
- Ensure from email is not blacklisted

## Monitoring and Maintenance

### Regular Checks
1. Monitor email delivery rates
2. Check for authentication failures
3. Verify SSL certificate expiration
4. Update passwords as needed

### Performance Optimization
1. Monitor cache hit rates
2. Clear cache when configuration changes
3. Monitor email sending performance

### Security Best Practices
1. Use strong passwords
2. Enable 2FA where possible
3. Regularly rotate passwords
4. Monitor for unauthorized access
5. Use encrypted connections only

## Support

For additional support:
1. Check application logs for detailed error messages
2. Verify network connectivity
3. Contact your email provider for SMTP issues
4. Review the comprehensive documentation in `AGENCY_EMAIL_CONFIGURATION.md`
