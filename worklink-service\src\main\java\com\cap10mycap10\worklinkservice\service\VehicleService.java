package com.cap10mycap10.worklinkservice.service;


import com.cap10mycap10.worklinkservice.dto.VehicleFilterDto;
import com.cap10mycap10.worklinkservice.dto.VehicleInventoryTaxUpdateDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleAvailabilityDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleSearchDto;
import com.cap10mycap10.worklinkservice.enums.AssetStatus;
import com.cap10mycap10.worklinkservice.model.Vehicle;
import com.cap10mycap10.worklinkservice.model.VehicleFilter;
import com.cap10mycap10.worklinkservice.model.VehicleInventory;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.List;

public interface VehicleService {
    VehicleDto save(VehicleDto asset);

    VehicleDto update(VehicleDto asset, Boolean admin);

    VehicleDto updateRates(@NotNull VehicleDto vehicleDto);

    VehicleDto findById(Long id);
    Vehicle getOne(Long id);

    void addVehicleAvailability(Long id, List<LocalDate> dates);

    void addVehicleUnAvailability(Long id, List<LocalDate> dates);

    Page<VehicleDto> findByAgencyIdAndTypePaged(
            Long agencyId,
            AssetStatus status,
            String searchParams,
            PageRequest pageRequest);
    void addDocument(Long documentId, Long workerId, String docName, LocalDate expiryDate, MultipartFile files,LocalDate lastDate,Integer lastMileage,Integer expiryMileage);

    void addPhoto(Long vehicleId, List<String> files);

    void saveInventory(VehicleInventory vehicleInventory);

    void setMainPhoto(Long id);

    Page<Vehicle> filterVehicles(VehicleFilter filter, PageRequest pageRequest);
    Page<VehicleDto> filterVehiclesDto(VehicleFilterDto filter, PageRequest pageRequest);

    void deletePhoto(Long id);


    Page<VehicleDto> findAllByStatus(AssetStatus type,Long agencyId, PageRequest of);

//    Page<VehicleDto> findTopRated(int page, int size);

    VehicleDto approveVehicle(Long vehicleId);

    VehicleDto enableVehicle(Long vehicleId);

    VehicleDto disableVehicle(Long vehicleId);

    VehicleDto rejectVehicle(Long vehicleId, String comment);

//    Page<VehicleDto> findPublicVehicles(LocalDateTime start, LocalDateTime end, Long location, VehicleType vehicleType, Long agencyId, String searchCriteria, PageRequest of);

    void deleteInventory(Long id);

    void deleteDocuments(Long id);

    Page<VehicleDto> findPublicVehicles(VehicleSearchDto vehicleSearchDto, PageRequest of);

    VehicleAvailabilityDto getVehicleAvailability(Long vehicleId);


    VehicleAvailabilityDto clearAllVehicleAvailability(Long vehicleId);

    // New methods for handling multiple locations
    void addVehicleLocation(Long vehicleId, Long locationId);

    void addVehicleLocation(Long vehicleId, Long locationId, Boolean active);

    void removeVehicleLocation(Long vehicleId, Long locationId);

    void setVehicleLocationActive(Long vehicleId, Long locationId, Boolean active);

    List<Long> getVehicleLocationIds(Long vehicleId);

    List<Long> getActiveVehicleLocationIds(Long vehicleId);

    Page<VehicleDto> findByLocationIds(List<Long> locationIds, Long agencyId, AssetStatus status, PageRequest pageRequest);

    void updateInventoryTaxSettings(Long inventoryId, VehicleInventoryTaxUpdateDto taxUpdateDto);
}
