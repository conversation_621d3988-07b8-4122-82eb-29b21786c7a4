package com.cap10mycap10.worklinkservice.dto.tax;

import lombok.Data;

@Data
public class TaxConfigurationDto {

    private Boolean taxExempt;
    private Boolean taxInclusive;
    private Long customTaxRateId;
    private TaxRateDto customTaxRate;

    public TaxConfigurationDto() {
        this.taxExempt = false;
    }

    public TaxConfigurationDto(Boolean taxExempt, Boolean taxInclusive, Long customTaxRateId) {
        this.taxExempt = taxExempt != null ? taxExempt : false;
        this.taxInclusive = taxInclusive;
        this.customTaxRateId = customTaxRateId;
    }
}
