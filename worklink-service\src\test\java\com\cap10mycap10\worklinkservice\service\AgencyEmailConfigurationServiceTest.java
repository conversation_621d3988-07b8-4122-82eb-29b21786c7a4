package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dao.AgencyEmailConfigurationRepository;
import com.cap10mycap10.worklinkservice.dao.AgencyRepository;
import com.cap10mycap10.worklinkservice.implementation.AgencyEmailConfigurationServiceImpl;
import com.cap10mycap10.worklinkservice.model.AgencyEmailConfiguration;
import com.cap10mycap10.worklinkservice.service.EncryptionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AgencyEmailConfigurationServiceTest {

    @Mock
    private AgencyEmailConfigurationRepository emailConfigRepository;

    @Mock
    private AgencyRepository agencyRepository;

    @InjectMocks
    private AgencyEmailConfigurationServiceImpl emailConfigService;

    private AgencyEmailConfiguration testConfig;

    @BeforeEach
    void setUp() {
        testConfig = AgencyEmailConfiguration.builder()
                .id(1L)
                .agencyId(123L)
                .smtpHost("smtp.gmail.com")
                .smtpPort(587)
                .smtpUsername("<EMAIL>")
                .smtpPassword("password123")
                .smtpAuth(true)
                .smtpStarttlsEnable(true)
                .smtpStarttlsRequired(true)
                .smtpSslEnable(false)
                .fromEmail("<EMAIL>")
                .fromName("Test Agency")
                .replyToEmail("<EMAIL>")
                .supportEmail("<EMAIL>")
                .websiteUrl("https://agency.com")
                .logoUrl("https://agency.com/logo.png")
                .isActive(true)
                .isVerified(true)
                .build();
    }

    @Test
    void testSaveConfiguration_Success() {
        // Given
        when(agencyRepository.existsById(anyLong())).thenReturn(true);
        when(emailConfigRepository.save(any(AgencyEmailConfiguration.class))).thenReturn(testConfig);

        // When
        AgencyEmailConfiguration result = emailConfigService.save(testConfig);

        // Then
        assertNotNull(result);
        assertEquals(testConfig.getAgencyId(), result.getAgencyId());
        assertEquals(testConfig.getFromEmail(), result.getFromEmail());
        assertEquals(testConfig.getFromName(), result.getFromName());
        verify(agencyRepository).existsById(123L);
        verify(emailConfigRepository).save(testConfig);
    }

    @Test
    void testSaveConfiguration_AgencyNotFound() {
        // Given
        when(agencyRepository.existsById(anyLong())).thenReturn(false);

        // When & Then
        assertThrows(RuntimeException.class, () -> emailConfigService.save(testConfig));
        verify(agencyRepository).existsById(123L);
        verify(emailConfigRepository, never()).save(any());
    }

    @Test
    void testFindByAgencyId_Found() {
        // Given
        when(emailConfigRepository.findByAgencyId(anyLong())).thenReturn(Optional.of(testConfig));

        // When
        Optional<AgencyEmailConfiguration> result = emailConfigService.findByAgencyId(123L);

        // Then
        assertTrue(result.isPresent());
        assertEquals(testConfig.getAgencyId(), result.get().getAgencyId());
        verify(emailConfigRepository).findByAgencyId(123L);
    }

    @Test
    void testFindByAgencyId_NotFound() {
        // Given
        when(emailConfigRepository.findByAgencyId(anyLong())).thenReturn(Optional.empty());

        // When
        Optional<AgencyEmailConfiguration> result = emailConfigService.findByAgencyId(123L);

        // Then
        assertFalse(result.isPresent());
        verify(emailConfigRepository).findByAgencyId(123L);
    }

    @Test
    void testFindVerifiedActiveByAgencyId() {
        // Given
        when(emailConfigRepository.findVerifiedActiveByAgencyId(anyLong())).thenReturn(Optional.of(testConfig));

        // When
        Optional<AgencyEmailConfiguration> result = emailConfigService.findVerifiedActiveByAgencyId(123L);

        // Then
        assertTrue(result.isPresent());
        assertTrue(result.get().getIsActive());
        assertTrue(result.get().getIsVerified());
        verify(emailConfigRepository).findVerifiedActiveByAgencyId(123L);
    }

    @Test
    void testHasVerifiedActiveConfiguration_True() {
        // Given
        when(emailConfigRepository.hasVerifiedActiveConfiguration(anyLong())).thenReturn(true);

        // When
        boolean result = emailConfigService.hasVerifiedActiveConfiguration(123L);

        // Then
        assertTrue(result);
        verify(emailConfigRepository).hasVerifiedActiveConfiguration(123L);
    }

    @Test
    void testHasVerifiedActiveConfiguration_False() {
        // Given
        when(emailConfigRepository.hasVerifiedActiveConfiguration(anyLong())).thenReturn(false);

        // When
        boolean result = emailConfigService.hasVerifiedActiveConfiguration(123L);

        // Then
        assertFalse(result);
        verify(emailConfigRepository).hasVerifiedActiveConfiguration(123L);
    }

    @Test
    void testActivateConfiguration() {
        // Given
        AgencyEmailConfiguration inactiveConfig = AgencyEmailConfiguration.builder()
                .agencyId(123L)
                .isActive(false)
                .smtpHost("smtp.gmail.com")
                .smtpPort(587)
                .smtpUsername("<EMAIL>")
                .smtpPassword("password123")
                .fromEmail("<EMAIL>")
                .fromName("Test Agency")
                .build();

        AgencyEmailConfiguration activeConfig = AgencyEmailConfiguration.builder()
                .agencyId(123L)
                .isActive(true)
                .smtpHost("smtp.gmail.com")
                .smtpPort(587)
                .smtpUsername("<EMAIL>")
                .smtpPassword("password123")
                .fromEmail("<EMAIL>")
                .fromName("Test Agency")
                .build();

        when(emailConfigRepository.findByAgencyId(anyLong())).thenReturn(Optional.of(inactiveConfig));
        when(emailConfigRepository.save(any(AgencyEmailConfiguration.class))).thenReturn(activeConfig);
        when(agencyRepository.existsById(anyLong())).thenReturn(true);

        // When
        AgencyEmailConfiguration result = emailConfigService.activateConfiguration(123L);

        // Then
        assertTrue(result.getIsActive());
        verify(emailConfigRepository).findByAgencyId(123L);
        verify(emailConfigRepository).save(any(AgencyEmailConfiguration.class));
    }

    @Test
    void testDeactivateConfiguration() {
        // Given
        AgencyEmailConfiguration activeConfig = AgencyEmailConfiguration.builder()
                .agencyId(123L)
                .isActive(true)
                .smtpHost("smtp.gmail.com")
                .smtpPort(587)
                .smtpUsername("<EMAIL>")
                .smtpPassword("password123")
                .fromEmail("<EMAIL>")
                .fromName("Test Agency")
                .build();

        AgencyEmailConfiguration inactiveConfig = AgencyEmailConfiguration.builder()
                .agencyId(123L)
                .isActive(false)
                .smtpHost("smtp.gmail.com")
                .smtpPort(587)
                .smtpUsername("<EMAIL>")
                .smtpPassword("password123")
                .fromEmail("<EMAIL>")
                .fromName("Test Agency")
                .build();

        when(emailConfigRepository.findByAgencyId(anyLong())).thenReturn(Optional.of(activeConfig));
        when(emailConfigRepository.save(any(AgencyEmailConfiguration.class))).thenReturn(inactiveConfig);
        when(agencyRepository.existsById(anyLong())).thenReturn(true);

        // When
        AgencyEmailConfiguration result = emailConfigService.deactivateConfiguration(123L);

        // Then
        assertFalse(result.getIsActive());
        verify(emailConfigRepository).findByAgencyId(123L);
        verify(emailConfigRepository).save(any(AgencyEmailConfiguration.class));
    }

    @Test
    void testConfigurationValidation() {
        // Test that the configuration validation works
        AgencyEmailConfiguration invalidConfig = AgencyEmailConfiguration.builder()
                .agencyId(null) // Invalid - null agency ID
                .smtpHost("smtp.gmail.com")
                .smtpPort(587)
                .smtpUsername("<EMAIL>")
                .smtpPassword("password123")
                .fromEmail("<EMAIL>")
                .fromName("Test Agency")
                .build();

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> emailConfigService.save(invalidConfig));
        verify(emailConfigRepository, never()).save(any());
    }

    @Test
    void testIsReadyToUse() {
        // Test complete configuration
        assertTrue(testConfig.isReadyToUse());

        // Test incomplete configuration
        AgencyEmailConfiguration incompleteConfig = AgencyEmailConfiguration.builder()
                .agencyId(123L)
                .isActive(true)
                .isVerified(true)
                .smtpHost("smtp.gmail.com")
                .smtpPort(587)
                .smtpUsername("<EMAIL>")
                .smtpPassword("password123")
                .fromEmail("<EMAIL>")
                // Missing fromName
                .build();

        assertFalse(incompleteConfig.isReadyToUse());
    }
}
