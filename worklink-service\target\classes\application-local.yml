spring:
  servlet:
    multipart:
      max-file-size: 2MB
      max-request-size: 10MB
  sleuth:
    sampler:
      probability: 1
  rabbitmq:
    host: localhost
    port: 5672 
    username: guest
    password: guest
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ******************************************************************************************************************************************************************************************************************
    username: 'root'
    password: 'root'
#    password: 'engbanda.com'
    driver-class-name: org.mariadb.jdbc.Driver
    hikari:
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
        useLocalSessionState: true
        rewriteBatchedStatements: true
        cacheResultSetMetadata: true
        cacheServerConfiguration: true
        elideSetAutoCommits: true
        maintainTimeStats: false
      maximum-pool-size: 15
      connection-timeout: 600000
      max-lifetime: 580000


  jpa:
    database-platform: org.hibernate.dialect.MariaDB103Dialect
    database: MYSQL
    hibernate:
      ddl-auto: update
      generate-ddl: true
      show-sql: true


    properties:
      hibernate.search.default.directory_provider: filesystem
      hibernate.search.default.indexBase: indexpath
      hibernate.format_sql: true
      hibernate.use_sql_comments: true
      hibernate.id.new_generator_mappings: true
      hibernate.connection.provider_disables_autocommit: true
      hibernate.cache.use_second_level_cache: false
      hibernate.cache.use_query_cache: false
      hibernate.generate_statistics: true
      hibernate.jdbc.batch_size: 500
      hibernate.order_inserts: true
      hibernate.order_updates: true
      hibernate.query.fail_on_pagination_over_collection_fetch: true
      hibernate.query.in_clause_parameter_padding: true
      hibernate.jdbc.time_zone: UTC




  data:
    jpa:
      repositories:
        bootstrap-mode: lazy

  jackson:
    serialization:
      fail-on-empty-beans: false
springdoc:
  swagger-ui:
    path: /swagger-ui.html
server:
  port: 8300
  max-http-request-header-size: 20000
  max-http-header-size: 65536


gcp:
  config:
    file: gcp-account-file.json
  project:
    id: worklink
  bucket:
    id: worklink
  dir:
    name: worklink

#logging.level.org.hibernate.SQL: DEBUG
logging.level.org.hibernate: ERROR
logging.level.org.hibernate.type.descriptor.sql: error
#logging.level.org.springframework.data: DEBUG


logging.logstash:
  enabled: true
  url: **************:5601

security:
  oauth2:
    resource:
      jwt:
        key-value: JWTKey@1234
        key-uri: http://localhost:8202/oauth/token_key
      id: 'carInventory1'
    client:
      client-id: appclient
      client-secret: appclient@123
#Eureka Client Configurations
eureka:         #tells about the Eureka server details and its refresh time
  instance:
    leaseRenewalIntervalInSeconds: 1
    leaseExpirationDurationInSeconds: 2
    prefer-ip-address: true
  client:
    serviceUrl:
      defaultZone: http://127.0.0.1:8761/eureka/
    healthcheck:
      enabled: true
    lease:
      duration: 5

management:
  security:
    enabled: false  #disable the spring security on the management endpoints like /env, /refresh etc.


user.url: http://localhost:8202

api:
  url: https://qa-api.myworklink.uk/worklink-api/api/v1

iosApp:
  url: https://app.myworklink.uk/

androidApp:
  url: https://play.google.com/store/apps/details?id=uk.co.myworklink.myworklink

storage:
  volume:
    path: C:\\Users\\<USER>\\Videos

env:
  companyName: MyKarLink
  email: <EMAIL>
  supportEmail: <EMAIL>
  website: https://mykarlink.com
  mailPassword: GK28~N!$80p>
  paynowIntegrationId: 19936
  paynowIntegrationKey: 3d28c59b-2013-45a4-b5e2-6329af68a03c
  paynowResultUrl: http://localhost:8765/karlink-service/api/v1/vehicle-booking/paynow-paid
  paynowReturnUrl: http://localhost:3000/bookings/checkout/success
  companyLogo: https://admin.mykarlink.com/assets/images/svg/new-logo.svg
  systemCurrency: $
  systemCurrency3: usd
  STRIPE_SECRET_KEY: sk_test_51Qjd3G2LpuY6cK8k5PcwTf6gXK8ftBjww6WufIwWaG13xaqw3DaQa4K5tslmLzYu4VU1E27abn3Uw2CcgWvqbvH400jlFEunZq
  STRIPE_PUBLIC_KEY: pk_test_51Qjd3G2LpuY6cK8kwmYArWqPUluSwOxE8xOvRN8mvLbGQWz0LSRs4icx3nWmfJs6PoBvbrC8AkTt2pk3MeH7lgiQ005iWawvXE
  STRIPE_WEBHOOK_SECRET: whsec_0b2cb3dccaf03ca027963bb7921dda2ac84301f4fa9073d894d12e1034e8e973
