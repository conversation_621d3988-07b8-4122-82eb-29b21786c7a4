package com.cap10mycap10.worklinkservice.dto.asset.admin;

import com.cap10mycap10.worklinkservice.dto.tax.TaxRateDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class VehicleInventoryDto {
    
    private Long id;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate dateInstalled;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate nextCheckDate;
    
    private String name;
    private String description;
    private String photoUrl1;
    private String photoUrl2;
    private String photoUrl3;
    private String photoUrl4;
    private Float price;
    
    // Tax-related fields
    private Boolean taxExempt = false; // Whether this addon is exempt from tax
    private Boolean taxInclusive; // Whether the price is tax-inclusive (null = use agency default)
    
    // Custom tax rate as DTO to avoid Hibernate proxy issues
    private TaxRateDto customTaxRate; // Custom tax rate for this addon (null = use agency default)
    
    private Long vehicleId;
    
    // Audit fields from AbstractAuditingEntity
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    private LocalDateTime createdDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    private LocalDateTime lastModifiedDate;
    
    private Integer version;
}
