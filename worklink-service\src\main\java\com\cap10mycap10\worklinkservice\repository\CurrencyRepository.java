package com.cap10mycap10.worklinkservice.repository;

import com.cap10mycap10.worklinkservice.model.Currency;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CurrencyRepository extends JpaRepository<Currency, String> {

    /**
     * Find all active currencies
     */
    List<Currency> findByActiveTrue();

    /**
     * Find currency by code (case insensitive)
     */
    @Query("SELECT c FROM Currency c WHERE UPPER(c.code) = UPPER(?1)")
    Optional<Currency> findByCodeIgnoreCase(String code);

    /**
     * Check if a currency code exists and is active
     */
    boolean existsByCodeAndActiveTrue(String code);
}
