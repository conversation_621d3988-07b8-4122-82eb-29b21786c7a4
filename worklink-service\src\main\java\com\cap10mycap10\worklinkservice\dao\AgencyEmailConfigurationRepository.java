package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.model.AgencyEmailConfiguration;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository interface for managing AgencyEmailConfiguration entities.
 */
@Repository
public interface AgencyEmailConfigurationRepository extends JpaRepository<AgencyEmailConfiguration, Long> {

    /**
     * Find email configuration by agency ID
     * @param agencyId the agency ID
     * @return Optional containing the email configuration if found
     */
    Optional<AgencyEmailConfiguration> findByAgencyId(Long agencyId);

    /**
     * Find active email configuration by agency ID
     * @param agencyId the agency ID
     * @return Optional containing the active email configuration if found
     */
    @Query("SELECT aec FROM AgencyEmailConfiguration aec WHERE aec.agencyId = :agencyId AND aec.isActive = true")
    Optional<AgencyEmailConfiguration> findActiveByAgencyId(@Param("agencyId") Long agencyId);

    /**
     * Find verified and active email configuration by agency ID
     * @param agencyId the agency ID
     * @return Optional containing the verified and active email configuration if found
     */
    @Query("SELECT aec FROM AgencyEmailConfiguration aec WHERE aec.agencyId = :agencyId AND aec.isActive = true AND aec.isVerified = true")
    Optional<AgencyEmailConfiguration> findVerifiedActiveByAgencyId(@Param("agencyId") Long agencyId);

    /**
     * Check if an agency has an active email configuration
     * @param agencyId the agency ID
     * @return true if the agency has an active email configuration
     */
    @Query("SELECT COUNT(aec) > 0 FROM AgencyEmailConfiguration aec WHERE aec.agencyId = :agencyId AND aec.isActive = true")
    boolean hasActiveConfiguration(@Param("agencyId") Long agencyId);

    /**
     * Check if an agency has a verified and active email configuration
     * @param agencyId the agency ID
     * @return true if the agency has a verified and active email configuration
     */
    @Query("SELECT COUNT(aec) > 0 FROM AgencyEmailConfiguration aec WHERE aec.agencyId = :agencyId AND aec.isActive = true AND aec.isVerified = true")
    boolean hasVerifiedActiveConfiguration(@Param("agencyId") Long agencyId);

    /**
     * Delete email configuration by agency ID
     * @param agencyId the agency ID
     */
    void deleteByAgencyId(Long agencyId);

    /**
     * Check if email configuration exists for agency
     * @param agencyId the agency ID
     * @return true if configuration exists
     */
    boolean existsByAgencyId(Long agencyId);
}
