package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.WorkerTrainingSessionRepository;
import com.cap10mycap10.worklinkservice.dto.shift.BookingResultDto;
import com.cap10mycap10.worklinkservice.dto.workertrainingsession.WorkerTrainingSessionResultsDto;
import com.cap10mycap10.worklinkservice.enums.BookingType;
import com.cap10mycap10.worklinkservice.enums.TrainingSessionStatus;
import com.cap10mycap10.worklinkservice.enums.WorkerTrainingSessionStatus;
import com.cap10mycap10.worklinkservice.events.email.EmailService;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.helpers.WorkerTrainingSessionComparator;
import com.cap10mycap10.worklinkservice.mapper.workertrainingsession.WorkerTrainingSessionToWorkerTrainingSessionResultsDto;
import com.cap10mycap10.worklinkservice.model.*;
import com.cap10mycap10.worklinkservice.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional
public class WorkerTrainingSessionServiceImpl implements WorkerTrainingSessionService {
    @Autowired
    WorkerTrainingSessionRepository workerTrainingSessionRepository;
    @Autowired
    WorkerService workerService;

    @Autowired
    private DeviceService deviceService;
    @Autowired
    private  WorkerTrainingSessionToWorkerTrainingSessionResultsDto toWorkerTrainingSessionResultsDto;

    @Autowired
    private AgencyService agencyService;

    @Autowired
    private EmailService emailService;


    @Override
    @Transactional
    public WorkerTrainingSession getOne(Long id) {
        return workerTrainingSessionRepository.findById(id).orElseThrow(
                ()-> new BusinessValidationException(String.format("Worker training Session with id: %s not found", id))
        );
    }

    @Override
    public WorkerTrainingSession findByWorkerAndTrainingSession(Worker worker, TrainingSession trainingSession) {
        return workerTrainingSessionRepository.findByWorkerAndTrainingSession(  worker,   trainingSession);
    }

    @Override
    public Optional<WorkerTrainingSession> checkPending() {
        return  workerTrainingSessionRepository
                .findFeedbackPending(workerService.getAuthenticatedWorker().getId()                )
                .stream()
                .findFirst();
    }



    @Override
    @Transactional
    public Training getTraining(Long id) {
        return workerTrainingSessionRepository.getOne(id).getTrainingSession().getTraining();
    }

    @Override
    public List<Long> findByAgencyIdAndNotAdminBilled(Long trainerId) {
        return workerTrainingSessionRepository.findByAgencyIdAndNotAdminBilled(trainerId);
    }

    @Override
    public List<BookingResultDto> findWorkerBookings(Long workerId, WorkerTrainingSessionStatus status) {
        return workerTrainingSessionRepository
                .findByWorkerAndTrainingStatus(workerService.getOne(workerId), status )
                .stream()
                .map( this::convert)
                .collect(Collectors.toList());
    }

    @Override
    public void save(WorkerTrainingSession workerTrainingSession) {

//        boolean isAvailable = workerService.getWorkerConflicts(workerTrainingSession.getWorker(), workerSpec.getTransport().getDateTimeRequired(), workerSpec.getTransport().getDateTimeRequired().plusMinutes(10));
//
//        if(!isAvailable)
//            throw new BusinessValidationException("Trainee "+workerTrainingSession.getWorker().getFirstname()+" "+workerTrainingSession.getWorker().getLastname()+" is not available on this date and time");
//
        workerTrainingSessionRepository.save(workerTrainingSession);
    }

    @Override
    public void showCertificate(Long id, Boolean show) {
        WorkerTrainingSession workerTrainingSession = getOne(id);
        workerTrainingSession.setShowCertificate(show);
        workerTrainingSessionRepository.save(workerTrainingSession);
    }


    @Override
    @Transactional
    public Page<WorkerTrainingSessionResultsDto> findForAgency(Long agencyId, WorkerTrainingSessionStatus trainingStatus, Long trainingId, LocalDate startDate, LocalDate endDate, PageRequest of) {
        List<WorkerTrainingSession> workerTrainingSessionsList = workerTrainingSessionRepository.findByAgencyIdAndTrainingStatus(agencyId, trainingStatus);
        Collections.sort(workerTrainingSessionsList, new WorkerTrainingSessionComparator());

        if(startDate!=null) workerTrainingSessionsList = workerTrainingSessionsList.stream()
                .filter(p -> checkAfter(p.getTrainingSession().getStartDateTime().toLocalDate().toString(), startDate))
                .collect(Collectors.toList());

        if(endDate!=null) workerTrainingSessionsList = workerTrainingSessionsList.stream()
                .filter(p -> checkBefore(p.getTrainingSession().getEndDateTime().toLocalDate().toString(), endDate))
                .collect(Collectors.toList());

        if(trainingId!=null) workerTrainingSessionsList = workerTrainingSessionsList.stream()
                .filter(p -> Objects.equals(p.getTrainingSession().getTraining().getId(), trainingId))
                .collect(Collectors.toList());

        List<WorkerTrainingSession> filteredList = workerTrainingSessionsList.stream()
                .filter((workerTrainingSession) -> {
                    LocalDateTime now = LocalDateTime.now().minusDays(30);
                    boolean isClosedAndOld = workerTrainingSession.getTrainingSession().getTrainingStatus()
                            .equals(TrainingSessionStatus.CLOSED) &&
                            now.isAfter(workerTrainingSession.getTrainingSession().getEndDateTime());
                    return !isClosedAndOld;
                })
                .collect(Collectors.toList());

        Page<WorkerTrainingSession> page = PaginationUtil.paginateWorkerTrainingSession(of, filteredList);
        return page.map(toWorkerTrainingSessionResultsDto);
    }

    @Override
    @Transactional
    public Page<WorkerTrainingSessionResultsDto> findForTrainer(Long agencyId, WorkerTrainingSessionStatus trainingStatus, Long trainingId, LocalDate startDate, LocalDate endDate, PageRequest of) {

        List<WorkerTrainingSession> workerTrainingSessionsList = workerTrainingSessionRepository.findForTrainer(agencyId, trainingStatus.toString());

        Collections.sort(workerTrainingSessionsList, new WorkerTrainingSessionComparator());

        if(startDate!=null) workerTrainingSessionsList = workerTrainingSessionsList.stream()
                .filter(p -> checkAfter(p.getTrainingSession().getStartDateTime().toLocalDate().toString(), startDate))
                .collect(Collectors.toList());

        if(endDate!=null) workerTrainingSessionsList = workerTrainingSessionsList.stream()
                .filter(p -> checkBefore(p.getTrainingSession().getEndDateTime().toLocalDate().toString(), endDate))
                .collect(Collectors.toList());

        if(trainingId!=null) workerTrainingSessionsList = workerTrainingSessionsList.stream()
                .filter(p -> Objects.equals(p.getTrainingSession().getTraining().getId(), trainingId))
                .collect(Collectors.toList());

        List<WorkerTrainingSession> filteredList = workerTrainingSessionsList.stream()
                .filter((workerTrainingSession) -> {
                    LocalDateTime now = LocalDateTime.now().minusDays(30);
                    return !trainingStatus.equals(WorkerTrainingSessionStatus.CLOSED) && now.isAfter(workerTrainingSession.getTrainingSession().getEndDateTime());
                })
                .collect(Collectors.toList());

        Page<WorkerTrainingSession> page = PaginationUtil.paginateWorkerTrainingSession(of, filteredList);
        return page.map(toWorkerTrainingSessionResultsDto);
    }

    @Override
    public String authorizationReminder(Long id) {
        WorkerTrainingSession workerTrainingSession = getOne(id);

        if(workerTrainingSession.getTrainingStatus().equals(WorkerTrainingSessionStatus.WAITING_AUTHORIZATION) ||
            workerTrainingSession.getTrainingStatus().equals(WorkerTrainingSessionStatus.NEW)
        ){
            String title = "Request to authorize a booking";
            String body = "Hi, " + workerTrainingSession.getWorker().getFirstname() + " is requesting you to authorize their training booking" +
                    "Training Start Date:" + workerTrainingSession.getTrainingSession().getStartDateTime()+ "\n" +
                    "Training End Date:" + workerTrainingSession.getTrainingSession().getEndDateTime() + "\n" +
                    "Assignment Code:" + workerTrainingSession.getWorker().getAssignmentCode() + "\n" +
                    "Location:" + workerTrainingSession.getTrainingSession().getAddress() + "\n" +
                    "";

            Agency agency = workerTrainingSession.getAgency();
            CompletableFuture.runAsync(()-> emailService.sendSimpleMessage(agency.getEmail(), title, body, agency.getId()));

        }
        else {
            throw new BusinessValidationException("You can only request authorization for a booking which is waiting for authorization or has not been authorized before.");
        }
        log.info("Authorization Request Notification for worker: {}", workerTrainingSession.getWorker().getFirstname());
        return "Training Booking Request for Authorization for " + workerTrainingSession.getWorker().getFirstname() + "has been sent";
    }


    @Transactional
    public BookingResultDto convert(WorkerTrainingSession workerTrainingSession) {

        BookingResultDto bookingResultDto = new BookingResultDto();
        bookingResultDto.setId(workerTrainingSession.getId());
        bookingResultDto.setId(workerTrainingSession.getId());
        bookingResultDto.setDirectorate(workerTrainingSession.getTrainingSession().getAddress());
        Location location = workerTrainingSession.getTrainingSession().getLocation();
        bookingResultDto.setShiftLocation(location.getCity());
        bookingResultDto.setPostCode(workerTrainingSession.getTrainingSession().getPostCode());

        bookingResultDto.setCost(workerTrainingSession.getTrainingSession().getTrainingCost().toString());
        bookingResultDto.setTrainer(workerTrainingSession.getTrainingSession().getTrainer().getName());
        bookingResultDto.setTrainingName(workerTrainingSession.getTrainingSession().getTraining().getName());
        bookingResultDto.setPayer(workerTrainingSession
                .getTrainingSession()
                .getIsAgencyPaying()?
                "Sponsored"
                :"Self");

        bookingResultDto.setPhoneNumber(workerTrainingSession.getTrainingSession().getTrainer().getTelephone());
        DateTimeFormatter pattern = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        DateTimeFormatter patternTime = DateTimeFormatter.ofPattern("HH:mm");
        bookingResultDto.setStart(workerTrainingSession.getTrainingSession().getStartDateTime());

        bookingResultDto.setEnd(workerTrainingSession.getTrainingSession().getEndDateTime());

        bookingResultDto.setBreakTime(String.valueOf(workerTrainingSession.getTrainingSession().getBreakTimeMins()));

        bookingResultDto.setBookingType(BookingType.TRAINING);


        bookingResultDto.setShiftStatus(workerTrainingSession.getTrainingStatus().toString());
        bookingResultDto.setLastModifiedDate(workerTrainingSession.getLastModifiedDate());

        if (workerTrainingSession.getAgency() != null) {
            bookingResultDto.setAgency(workerTrainingSession.getAgency().getName());
        }
        if (workerTrainingSession.getWorker() != null) {
            bookingResultDto.setWorker(
                    workerTrainingSession.getWorker().getFirstname()+ " "+ workerTrainingSession.getWorker().getLastname()
            );
        }
        return bookingResultDto;

    }



    private boolean checkAfter(String leftDate, LocalDate rightDate) {
        return !convertFromString(leftDate).isBefore(rightDate);
    }

    private boolean checkBefore(String leftDate, LocalDate rightDate) {
        return !convertFromString(leftDate).isAfter(rightDate);
    }

    private LocalDate convertFromString(String aDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            return LocalDate.parse(aDate, formatter);
        } catch (DateTimeParseException e) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            return LocalDate.parse(aDate, formatter);
        }
    }
}


