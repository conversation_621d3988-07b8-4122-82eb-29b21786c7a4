package com.cap10mycap10.worklinkservice.dao;


import com.cap10mycap10.worklinkservice.enums.AgencyType;
import com.cap10mycap10.worklinkservice.model.Agency;
import org.apache.commons.lang3.Streams;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface AgencyRepository extends JpaRepository<Agency, Long> {
    Page<Agency>  findAllByIsTransporter(Boolean transporter, Pageable of);

    @Query(value = "select count(*) from agency", nativeQuery = true)
    int findNumberOfAgents();


    @Query("SELECT c.agencies FROM Client c WHERE c.id = :clientId")
    Page<Agency> findAllByClient(@Param("clientId")Long id, Pageable pageable);




    List<Agency> findAllByIsTrainer(boolean trainer);

    @Query(value = "select  *\n" +
            "from agency\n" +
            "         inner join agency_worker aa on agency.id = aa.agency_id\n" +
            "where aa.worker_id =?1", nativeQuery = true)
    Page<Agency> findAllAgenciesByWorkerId(Long workerId, PageRequest of);


    @Query(value = "select DISTINCT agency.*" +
            "from agency\n" +
            "left join agency_worker_properties a on a.worker_id = ?1\n" +
            "where agency.id in (\n" +
            "select agency_id from agency_worker\n" +
            "where worker_id = ?1  order by agency.name asc) ", nativeQuery = true)
    Page<Agency> getMyAgencies(Long workerId, Pageable of);

//    @Query("SELECT a FROM Agency a WHERE " +
//            "(:agencyType IS NULL OR a.agencyType = :agencyType) AND " +
//            "(:searchQuery IS NULL OR LOWER(a.name) LIKE LOWER(CONCAT('%', :searchQuery, '%')) OR " +
//            "LOWER(a.email) LIKE LOWER(CONCAT('%', :searchQuery, '%')))")
//    Page<Agency> findAgencies(
//            @Param("agencyType") AgencyType agencyType,
//            @Param("searchQuery") String searchQuery,
//            Pageable pageable
//    );

    @Query(value = "select * from agency where (?1 is null or agency_type = ?1) and (?2 is null or name like %?2% or email like %?2% or billing_email like %?2% or telephone like %?2%) ",nativeQuery = true)
    Page<Agency> findAgenciesWithFilters(String agencyType, String searchQuery, Pageable of);

    @Query(value = "select * from agency where agency_type = ?1  order by agency.name asc",nativeQuery = true)
    Page<Agency> findByAgencyType(String agencyType, Pageable of);


    Page<Agency> findAllByAgencyType(AgencyType agencyType, Pageable of);

    @Query(value = "select count(*)from agency where client_id =?1",nativeQuery = true)
    int countAgenciesByClientId(Long id);

    @Query(value = "SELECT agency.* FROM agency JOIN worker  WHERE worker.id = ?1", nativeQuery = true)
    List<Agency> findByWorkerId(Long workerId);

    @Query(value = "SELECT * FROM agency WHERE agency.is_transporter = ?1 order by agency.name asc", nativeQuery = true)
    List<Agency> findByIsTransporter(boolean isTransporter);

    List<Agency> findAllByDeputyEnabled(boolean b);

    Agency findByEmail(String email);

    /**
     * Find agency by agent ID (for user-service integration)
     * Note: This assumes there's a relationship between agents and agencies
     * You may need to adjust this query based on your actual data model
     */
    @Query(value = "SELECT agency.* FROM agency WHERE agency.id = " +
            "(SELECT agent.agency_id FROM agent WHERE agent.id = ?1)", nativeQuery = true)
    Agency findAgencyByAgentId(Long agentId);
}
