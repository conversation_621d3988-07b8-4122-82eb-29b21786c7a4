package com.cap10mycap10.worklinkservice.dto.email;

import com.cap10mycap10.worklinkservice.model.AgencyEmailConfiguration;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.time.LocalDateTime;

/**
 * DTO for Agency Email Configuration
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgencyEmailConfigurationDto {

    private Long id;

    @NotNull(message = "Agency ID is required")
    private Long agencyId;

    @NotBlank(message = "SMTP host is required")
    private String smtpHost;

    @NotNull(message = "SMTP port is required")
    @Positive(message = "SMTP port must be positive")
    private Integer smtpPort;

    @NotBlank(message = "SMTP username is required")
    private String smtpUsername;

    @NotBlank(message = "SMTP password is required")
    private String smtpPassword;

    @Builder.Default
    private Boolean smtpAuth = true;

    @Builder.Default
    private Boolean smtpStarttlsEnable = true;

    @Builder.Default
    private Boolean smtpStarttlsRequired = true;

    @Builder.Default
    private Boolean smtpSslEnable = false;

    private String smtpSslSocketFactoryClass;

    @Email(message = "From email must be valid")
    @NotBlank(message = "From email is required")
    private String fromEmail;

    @NotBlank(message = "From name is required")
    private String fromName;

    @Email(message = "Reply-to email must be valid")
    private String replyToEmail;

    @Email(message = "Support email must be valid")
    private String supportEmail;

    private String websiteUrl;

    private String logoUrl;

    @Builder.Default
    private Boolean isActive = true;

    private Boolean isVerified;

    private LocalDateTime lastTestDate;

    private String lastTestResult;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private String createdBy;

    private String updatedBy;

    private String notes;

    /**
     * Convert DTO to Entity
     */
    public AgencyEmailConfiguration toEntity() {
        return AgencyEmailConfiguration.builder()
                .id(this.id)
                .agencyId(this.agencyId)
                .smtpHost(this.smtpHost)
                .smtpPort(this.smtpPort)
                .smtpUsername(this.smtpUsername)
                .smtpPassword(this.smtpPassword)
                .smtpAuth(this.smtpAuth)
                .smtpStarttlsEnable(this.smtpStarttlsEnable)
                .smtpStarttlsRequired(this.smtpStarttlsRequired)
                .smtpSslEnable(this.smtpSslEnable)
                .smtpSslSocketFactoryClass(this.smtpSslSocketFactoryClass)
                .fromEmail(this.fromEmail)
                .fromName(this.fromName)
                .replyToEmail(this.replyToEmail)
                .supportEmail(this.supportEmail)
                .websiteUrl(this.websiteUrl)
                .logoUrl(this.logoUrl)
                .isActive(this.isActive)
                .isVerified(this.isVerified)
                .lastTestDate(this.lastTestDate)
                .lastTestResult(this.lastTestResult)
                .createdBy(this.createdBy)
                .updatedBy(this.updatedBy)
                .notes(this.notes)
                .build();
    }

    /**
     * Create DTO from Entity
     */
    public static AgencyEmailConfigurationDto fromEntity(AgencyEmailConfiguration entity) {
        return AgencyEmailConfigurationDto.builder()
                .id(entity.getId())
                .agencyId(entity.getAgencyId())
                .smtpHost(entity.getSmtpHost())
                .smtpPort(entity.getSmtpPort())
                .smtpUsername(entity.getSmtpUsername())
                .smtpPassword("***HIDDEN***") // Never expose password in response
                .smtpAuth(entity.getSmtpAuth())
                .smtpStarttlsEnable(entity.getSmtpStarttlsEnable())
                .smtpStarttlsRequired(entity.getSmtpStarttlsRequired())
                .smtpSslEnable(entity.getSmtpSslEnable())
                .smtpSslSocketFactoryClass(entity.getSmtpSslSocketFactoryClass())
                .fromEmail(entity.getFromEmail())
                .fromName(entity.getFromName())
                .replyToEmail(entity.getReplyToEmail())
                .supportEmail(entity.getSupportEmail())
                .websiteUrl(entity.getWebsiteUrl())
                .logoUrl(entity.getLogoUrl())
                .isActive(entity.getIsActive())
                .isVerified(entity.getIsVerified())
                .lastTestDate(entity.getLastTestDate())
                .lastTestResult(entity.getLastTestResult())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .createdBy(entity.getCreatedBy())
                .updatedBy(entity.getUpdatedBy())
                .notes(entity.getNotes())
                .build();
    }

    /**
     * Create DTO from Entity for update operations (includes password)
     */
    public static AgencyEmailConfigurationDto fromEntityForUpdate(AgencyEmailConfiguration entity) {
        AgencyEmailConfigurationDto dto = fromEntity(entity);
        dto.setSmtpPassword(entity.getSmtpPassword()); // Include actual password for updates
        return dto;
    }
}
