package com.cap10mycap10.worklinkservice.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class StripeFxQuoteResponse {
    private String id;
    private String object;
    private long created;
    @JsonProperty("lock_duration")
    private String lockDuration;
    @JsonProperty("lock_expires_at")
    private Long lockExpiresAt;
    @JsonProperty("lock_status")
    private String lockStatus;
    private Map<String, Rate> rates;
    @JsonProperty("to_currency")
    private String toCurrency;
    private Usage usage;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Rate {
        @JsonProperty("exchange_rate")
        private double exchangeRate;
        @JsonProperty("rate_details")
        private RateDetails rateDetails;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RateDetails {
        @JsonProperty("base_rate")
        private double baseRate;
        @JsonProperty("duration_premium")
        private double durationPremium;
        @JsonProperty("fx_fee_rate")
        private double fxFeeRate;
        @JsonProperty("reference_rate")
        private double referenceRate;
        @JsonProperty("reference_rate_provider")
        private String referenceRateProvider;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Usage {
        private Payment payment;
        private Object transfer;
        private String type;

        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Payment {
            private Object destination;
            @JsonProperty("on_behalf_of")
            private Object onBehalfOf;
        }
    }
}
