worklink-service http://localhost:8300/swagger-ui.html#/
oauth-service http://localhost:8201/swagger-ui

./mvnw package 
java -jar target/gs-spring-boot-docker-0.1.0.jar

-DskipTests



[//]: # (Just push worklink api live script)
cd Worklink-Live-Server/worklink-backend/worklink-service && git pull && mvn clean install -DskipTests && cd ../ && docker compose build &&  cd .. && docker compose -f docker-compose-worklink.yml up -d && docker logs -f wk_worklink-service

[//]: # (Just push karlink api live script)
cd Worklink-Live-Server/worklink-backend/worklink-service && git pull && mvn clean install -DskipTests && cd ../ && docker compose build &&  cd .. && docker compose up -d karlink-service && docker logs -f karlink-service

[//]: # (Just push worklink api script)
cd Worklink-Live-Server/worklink-backend/worklink-service && git pull && mvn clean install -DskipTests && cd ../ && docker compose build &&  cd ../test-server && docker compose up -d worklink-service && docker system prune


[//]: # (Full deployment script)
cd Worklink-Live-Server/worklink-backend && git pull && mvn clean install -DskipTests && docker compose build && cd ../worklink-webapp  && git pull  && docker compose -f docker-compose-karlinktest.yml build && cd ../karlink-webapp && git pull && docker compose build && cd ../test-server && docker compose up -d && docker system prune

[//]: # (Full worklink deployment script)
cd Worklink-Live-Server/worklink-backend && git pull && mvn clean install -DskipTests && docker compose build && cd ../worklink-webapp  && git pull  && docker compose build && docker compose -f docker-compose-karlink.yml build && docker compose -f docker-compose-murare.yml build  && cd ../karlink-webapp && git pull && docker compose build && docker compose -f docker-compose-murare.yml build && cd .. && docker compose -f docker-compose-worklink.yml up -d && docker logs -f wk_worklink-service
[//]: # (Full live KArlink deployment script)
cd Worklink-Live-Server/worklink-backend && git pull && mvn clean install -DskipTests && docker compose build && cd ../worklink-webapp  && git pull  && docker compose build && docker compose -f docker-compose-karlink.yml build && docker compose -f docker-compose-murare.yml build  && cd ../karlink-webapp && git pull && docker compose build && docker compose -f docker-compose-murare.yml build && docker compose -f docker-compose-khenauto.yml build && cd .. && docker compose up -d && docker log -f worklink-service

[//]: # (Full live deployment script) 

git pull  && docker compose -f docker-compose-karlink.yml build

[//]: # (Push live )
cd worklink-webapp && git pull && docker compose build && docker compose push && cd .. && cd worklinkback && git pull && mvn clean install -DskipTests && docker compose build &&  docker compose push

[//]: # (DB Backup)
docker exec mariadb-db mariadb-dump --user root  --password=skdcnwauicn2ucnaecasdsajdnizucawencascdc --all-databases > $PWD/db_backups/db_bkp+date +'%d-%m-%Y'+.sql

stripe listen --forward-to localhost:8300/api/v1/stripe/webhook


stripe trigger payment_intent.succeeded
stripe trigger charge.updated
Running fixture for: payment_intent
Trigger succeeded! Check dashboard for event details.
