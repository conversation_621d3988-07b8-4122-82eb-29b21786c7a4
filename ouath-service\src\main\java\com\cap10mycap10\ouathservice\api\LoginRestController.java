package com.cap10mycap10.ouathservice.api;


import com.cap10mycap10.ouathservice.dto.UserLogin;
import com.cap10mycap10.ouathservice.dto.UserLoginRequest;
import com.cap10mycap10.ouathservice.entity.ResultDTO;
import com.cap10mycap10.ouathservice.exception.BusinessValidationException;
import com.cap10mycap10.ouathservice.service.LoginService;
import com.maxmind.geoip2.exception.GeoIp2Exception;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;


@Slf4j
@RestController
@CrossOrigin
@RequestMapping("api/v1/user-permission")
public class LoginRestController {

    private final LoginService loginService;

    public LoginRestController(LoginService loginService) {
        this.loginService = loginService;
    }

    @PostMapping("/login")
    public ResponseEntity<Object> login(HttpServletRequest request, @RequestBody UserLogin userLogin, Long agencyId ) throws BusinessValidationException, IOException, GeoIp2Exception {
        log.info("### Sending login Request");
        ResultDTO resultDTO = loginService.userLogin(userLogin, request, agencyId);

        return ResponseEntity.ok(resultDTO);
    }

    @PostMapping("/client-login")
    public ResponseEntity<Object> login(@RequestBody UserLoginRequest request) {
        log.info("Sending Request");
        ResultDTO resultDTO = loginService.clientLogin(request);
        return ResponseEntity.ok(resultDTO);
    }

    @PostMapping("/refresh-token/{token}")
    public ResponseEntity<ResultDTO> refreshToken(@PathVariable("token") String token
                                                  ) {
        log.info("Sending Request");
        ResultDTO resultDTO = loginService.refreshToken(token);
        return ResponseEntity.ok(resultDTO);
    }


}
