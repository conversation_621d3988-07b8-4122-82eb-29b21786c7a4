-- Migration script to add agency email configuration support
-- This script creates the agency_email_configuration table for white-label email sending

CREATE TABLE agency_email_configuration (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    agency_id BIGINT NOT NULL,
    
    -- SMTP Configuration
    smtp_host VA<PERSON>HA<PERSON>(255) NOT NULL,
    smtp_port INT NOT NULL,
    smtp_username VA<PERSON>HA<PERSON>(255) NOT NULL,
    smtp_password VARCHAR(500) NOT NULL, -- Encrypted password storage
    smtp_auth BOOLEAN NOT NULL DEFAULT TRUE,
    smtp_starttls_enable BOOLEAN NOT NULL DEFAULT TRUE,
    smtp_starttls_required BOOLEAN NOT NULL DEFAULT TRUE,
    smtp_ssl_enable BOOLEAN NOT NULL DEFAULT FALSE,
    smtp_ssl_socket_factory_class VARCHAR(255),
    
    -- Email Configuration
    from_email VARCHAR(255) NOT NULL,
    from_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    reply_to_email VARCHAR(255),
    support_email VARCHAR(255),
    website_url VARCHAR(500),
    logo_url VARCHAR(500),
    
    -- Status and Verification
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_verified BOOLEAN NOT NULL DEFAULT FALSE,
    last_test_date DATETIME,
    last_test_result TEXT,
    
    -- Audit Fields
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    
    -- Additional Information
    notes TEXT,
    
    -- Constraints
    UNIQUE KEY unique_agency_email_config (agency_id),
    INDEX idx_agency_id (agency_id),
    INDEX idx_active (is_active),
    INDEX idx_verified (is_verified),
    INDEX idx_active_verified (is_active, is_verified),
    INDEX idx_created_at (created_at),
    INDEX idx_updated_at (updated_at)
);

-- Add foreign key constraint to ensure agency exists
-- Note: Uncomment this if you want to enforce referential integrity
-- ALTER TABLE agency_email_configuration 
--     ADD CONSTRAINT fk_agency_email_config_agency 
--     FOREIGN KEY (agency_id) REFERENCES agency(id) ON DELETE CASCADE;

-- Add email validation constraints
ALTER TABLE agency_email_configuration 
    ADD CONSTRAINT chk_from_email_format 
    CHECK (from_email REGEXP '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$');

ALTER TABLE agency_email_configuration 
    ADD CONSTRAINT chk_reply_to_email_format 
    CHECK (reply_to_email IS NULL OR reply_to_email REGEXP '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$');

ALTER TABLE agency_email_configuration 
    ADD CONSTRAINT chk_support_email_format 
    CHECK (support_email IS NULL OR support_email REGEXP '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$');

-- Add port range constraint
ALTER TABLE agency_email_configuration 
    ADD CONSTRAINT chk_smtp_port_range 
    CHECK (smtp_port > 0 AND smtp_port <= 65535);

-- Add URL format constraints (basic validation)
ALTER TABLE agency_email_configuration 
    ADD CONSTRAINT chk_website_url_format 
    CHECK (website_url IS NULL OR website_url REGEXP '^https?://.*');

ALTER TABLE agency_email_configuration 
    ADD CONSTRAINT chk_logo_url_format 
    CHECK (logo_url IS NULL OR logo_url REGEXP '^https?://.*');

-- Create a view for easy querying of active configurations
CREATE VIEW active_agency_email_configurations AS
SELECT 
    aec.*,
    a.name as agency_name,
    a.email as agency_primary_email
FROM agency_email_configuration aec
JOIN agency a ON aec.agency_id = a.id
WHERE aec.is_active = TRUE;

-- Create a view for verified configurations only
CREATE VIEW verified_agency_email_configurations AS
SELECT 
    aec.*,
    a.name as agency_name,
    a.email as agency_primary_email
FROM agency_email_configuration aec
JOIN agency a ON aec.agency_id = a.id
WHERE aec.is_active = TRUE AND aec.is_verified = TRUE;
