package com.cap10mycap10.worklinkservice.service;


import com.cap10mycap10.worklinkservice.dto.agency.AgencyResultDto;
import com.cap10mycap10.worklinkservice.dto.client.ClientDto;
import com.cap10mycap10.worklinkservice.dto.shift.AgencyList;
import com.cap10mycap10.worklinkservice.dto.worker.*;
import com.cap10mycap10.worklinkservice.model.Worker;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

public interface WorkerService {

    void save(WorkerCreateDto workerCreateDto);

    WorkerResultDto findById(Long id);

    List<WorkerResultDto> findAll();

    boolean getWorkerConflicts(Long worker, LocalDateTime start, LocalDateTime end);

    Page<WorkerResultDto> findAllPaged(PageRequest of);

    void deleteById(Long id);

    void save(WorkerUpdateDto workerUpdateDto);

    Page<AgencyResultDto> findAllAgenciesPaged(Long workerId, PageRequest of);

    Worker getOne(Long workerId);
    Worker getByDeputyId(Long workerId);

    Worker getAuthenticatedWorker();

    Worker findByIHascoId(Long workerId);

    @Transactional
    Worker findByEmail(String id);

    Integer findNumberOfWorkers();

    Page<AgencyResultDto> getMyAgency(Long workerId, Pageable of);

    WorkerStats getStats(Long id);

    Page<WorkerResultDto> findWorkersForClient(Long clientId, Long assignmentCodeId, String gender, PageRequest of);

    Set<Worker> getAgencyWorkers(Long agencyId);

    void linkAgencyWorker(Long workerId, Long agencyId);

    Page<ClientDto> findAllClientsPaged(Long workerId, PageRequest of);

    Object findAllWorkersUnderClientBasedOnGender(Long workerId, PageRequest of);

    List<WorkerResultDto> findWorkersForAgencies(Long assignmentCode, String gender, AgencyList agencyList);

    List<WorkerResultDto> convertWorkers(List<Worker> worker);

    List<WorkerResultDto> getAgencyApplicantsByShiftId(Long shiftId, Long agencyId);

    List<WorkerResultDto> getApplicantsByShiftId(Long shiftId);

    void addProfilePic(Long workerId, MultipartFile file);

    Page<WorkerResultDto> search(String searchCriteria, Long agencyId, PageRequest of);
    Page<WorkerResultDto> searchApplicant(String searchCriteria, Long agencyId, PageRequest of);
    Page<WorkerResultDto> searchWorkerClient(String searchCriteria, Long agencyId, PageRequest of);
}
