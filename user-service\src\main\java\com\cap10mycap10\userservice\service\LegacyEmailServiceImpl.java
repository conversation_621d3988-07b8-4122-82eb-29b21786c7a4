package com.cap10mycap10.userservice.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.UnsupportedEncodingException;
import java.util.List;

import static java.util.Objects.nonNull;

/**
 * Legacy email service implementation (kept for backward compatibility)
 */
@Service("legacyEmailService")
@Slf4j
public class LegacyEmailServiceImpl {

    @Value("${env.companyName}")
    private String companyName;

    @Value("${env.email}")
    private String NOREPLY_ADDRESS;

    @Autowired
    private JavaMailSender emailSender;

    @Async("threadPoolTaskExecutor")
    public void sendEmail(List<String> to, String subject, String text, MultipartFile file) {
        try {
            MimeMessage message = emailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            String[] myArray = new String[to.size()];
            to.toArray(myArray);
            try {
                helper.setFrom(NOREPLY_ADDRESS, companyName);
            } catch (UnsupportedEncodingException e) {
                helper.setFrom(NOREPLY_ADDRESS);
            }

            helper.setTo(myArray);
            helper.setSubject(subject);
            helper.setText(text, "<html><body>"+text+"<br></body></html>");

            helper.addAttachment("File.jpg", file);

            emailSender.send(message);
        } catch (MessagingException e) {
            e.printStackTrace();
        }
    }

    @Async("threadPoolTaskExecutor")
    public void sendEmailAsUser(List<String> to, String subject, String text, String replyToEmail, String replyToName) {
        text = text.replaceAll("(\r\n|\n)", "<br />");

        try {
            MimeMessage message = emailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            String[] myArray = new String[to.size()];
            to.toArray(myArray);
            try {
                if(nonNull(replyToEmail)&&nonNull(replyToName))
                    helper.setReplyTo(replyToEmail, replyToName);
                if(nonNull(replyToName))
                    helper.setFrom(NOREPLY_ADDRESS, replyToName);
                else
                    helper.setFrom(NOREPLY_ADDRESS, companyName+" Team");
            } catch (UnsupportedEncodingException e) {
                helper.setFrom(NOREPLY_ADDRESS);
            }
            helper.setTo(myArray);
            helper.setSubject(subject);
            helper.setText( getHtmlEmail(text, subject), true);
            emailSender.send(message);
        } catch (MessagingException e) {
            e.printStackTrace();
        }
    }

    @Async("threadPoolTaskExecutor")
    public void sendEmailAsUser(List<String> to, String subject, String text) {
        sendEmailAsUser(to, subject, text, null, null);
    }

    private String getHtmlEmail(String text, String subject) {
        return "<!DOCTYPE html>" +
                "<html>" +
                "<head>" +
                "<meta charset='UTF-8'>" +
                "<title>" + subject + "</title>" +
                "</head>" +
                "<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>" +
                "<div style='max-width: 600px; margin: 0 auto; padding: 20px;'>" +
                "<div style='background-color: #f9f9f9; padding: 20px; border-radius: 5px;'>" +
                text +
                "</div>" +
                "<div style='margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;'>" +
                "<p>This email was sent by " + companyName + "</p>" +
                "</div>" +
                "</div>" +
                "</body>" +
                "</html>";
    }
}
