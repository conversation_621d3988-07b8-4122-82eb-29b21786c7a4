package com.cap10mycap10.ouathservice.dto;

import lombok.Data;

/**
 * DTO for agency email configuration
 */
@Data
public class AgencyEmailConfigurationDto {
    private Long id;
    private Long agencyId;
    
    // SMTP Configuration
    private String smtpHost;
    private Integer smtpPort;
    private String smtpUsername;
    private String smtpPassword;
    private Boolean smtpAuth;
    private Boolean smtpStarttlsEnable;
    private Boolean smtpStarttlsRequired;
    private Boolean smtpSslEnable;
    private String smtpSslSocketFactoryClass;
    
    // Email Configuration
    private String fromEmail;
    private String fromName;
    private String replyToEmail;
    private String supportEmail;
    private String websiteUrl;
    private String logoUrl;
    
    // Status
    private Boolean isActive;
    private Boolean isVerified;
    
    /**
     * Check if this configuration is ready to use
     */
    public boolean isReadyToUse() {
        return Boolean.TRUE.equals(isActive) && 
               Boolean.TRUE.equals(isVerified) &&
               smtpHost != null && 
               smtpPort != null && 
               smtpUsername != null && 
               smtpPassword != null &&
               fromEmail != null &&
               fromName != null;
    }
}
