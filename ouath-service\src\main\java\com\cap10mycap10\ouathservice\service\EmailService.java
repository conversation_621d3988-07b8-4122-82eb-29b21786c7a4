package com.cap10mycap10.ouathservice.service;

import com.cap10mycap10.ouathservice.entity.User;

import java.util.List;

/**
 * Enhanced email service interface that supports agency-specific email configuration
 */
public interface EmailService {
    
    /**
     * Send email using default configuration
     */
    void sendEmail(List<String> to, String subject, String text, Long agencyId);
    

    /**
     * Send unknown device notification email with agency branding
     */
    void sendUnknownDeviceNotificationEmail(User user, String deviceInfo, String location, Long agencyId);
}
