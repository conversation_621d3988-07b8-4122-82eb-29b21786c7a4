package com.cap10mycap10.worklinkservice.dto.asset.admin;

import com.cap10mycap10.worklinkservice.dto.client.ClientDto;
import com.cap10mycap10.worklinkservice.dto.invoice.InvoiceResult;
import com.cap10mycap10.worklinkservice.dto.promocode.PromotionDto;
import com.cap10mycap10.worklinkservice.enums.FrontPlatform;
import com.cap10mycap10.worklinkservice.enums.PaymentGatewayType;
import com.cap10mycap10.worklinkservice.enums.VehicleBookingStatus;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.model.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Data
@NoArgsConstructor
public class VehicleBookingDto {
    private Long id;
    private Set<InvoiceResult> invoices = new HashSet<>();
    private String firstname;
    private String surname;
    private String phone;
    private Long vehicleId;
    private Long locationId;
    private Boolean byAgency;
    private FrontPlatform source;
    private Boolean byMainSite;
    private Boolean skipEmails = false;
    private Double paidAmount =0.0;
    private String paymentRedirectUrl;
    private String cancelReason;
    private boolean fullPayment = false;
    private VehicleDto vehicle;
    private Rating rating;
    private Set<Rating> ratings;
    private String promoCode;
    private PromotionDto promotion;

    Set<VehicleInventoryDto> vehicleAddons= new HashSet<>();
    Set<Long> vehicleAddonsIds= new HashSet<>();

    private String checkoutItems;
    private String returnItems;
    private PaymentGatewayType gatewayType;
    private String hirerCurrency = "USD"; // Hirer's preferred currency for payment display
    private byte fuelOut;
    private byte fuelIn;
    private Integer mileageOut;
    private Integer mileageIn;
    private String damageImage;
    private String damageImageIn;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime signatureOutDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime signatureInDate;

    // Legacy signature fields - kept for backward compatibility
    @Deprecated
    private String  signatureOut;
    @Deprecated
    private String  signatureOutName;
    @Deprecated
    private String  signatureIn;
    @Deprecated
    private String  signatureInName;

    // New signature fields for handover process (signatureOut)
    private String signatureOutHirer;
    private String signatureOutHirerName;
    private String signatureOutCarRental;
    private String signatureOutCarRentalName;

    // New signature fields for return process (signatureIn)
    private String signatureInHirer;
    private String signatureInHirerName;
    private String signatureInCarRental;
    private String signatureInCarRentalName;
    private Set<DamageInfo> damageInfoOut ;
    private Set<DamageInfo> damageInfoIn;
    private Set<DamageInfo> extraCharges;
    private Set<VehicleBookingPhoto> damagePhotos;

    private ClientDto client;
    @JsonIgnore
    private Client clientModel;
    private Long clientId;
    private String email;
    private Location location;
    private BigDecimal discount;
    private BigDecimal deposit;
    private VehicleBookingStatus status;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime end;
//    private String endLocal;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime start;
//    private String startLocal;


    public VehicleBooking toAsset(Vehicle vehicle) {
        VehicleBooking vehicleBooking = new VehicleBooking(this, vehicle);
        return vehicleBooking;
    }

    public void validateBooking() {
        if (!nonNull(this.getEmail())) {
            throw new BusinessValidationException("Please specify the hirer email to use");
        }

        if(isNull(this.getLocationId())) throw new BusinessValidationException("Location id is required to place a booking");

    }


//    public Set<VehicleInventory> getVehicleAddons() {
//        if(nonNull(this.vehicle) && this.vehicle.getVehicleAddons().size() >0 && this.vehicleAddonsIds.size()>0) {
//            return this.vehicle.getVehicleAddons().stream().filter(a->this.vehicleAddonsIds.contains(a.getId())).collect(Collectors.toSet());
//        }
//        else if(this.vehicleAddonsIds.size()>0 && this.vehicle.getVehicleAddons().size() == 0)
//            throw new BusinessValidationException("Selected vehicle addons are not found on vehicle or vehicle has not yet been initialized");
//        else return new HashSet<>();
//    }
}
