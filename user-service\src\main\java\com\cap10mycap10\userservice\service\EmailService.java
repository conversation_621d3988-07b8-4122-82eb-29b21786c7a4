package com.cap10mycap10.userservice.service;

import com.cap10mycap10.userservice.model.User;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Enhanced email service interface that supports agency-specific email configuration
 */
public interface EmailService {


    /**
     * Send email for a specific user with reply-to configuration
     */
    void sendEmailForUser(User user, List<String> to, String subject, String text, String replyToEmail, String replyToName, Long agencyId);
    
    /**
     * Send password reset email with agency branding
     */
    void sendPasswordResetEmail(User user, String resetUrl, Long agencyId);
    
    /**
     * Send user registration email with agency branding
     */
    void sendUserRegistrationEmail(User user, String resetPasswordUrl, String workerUrl, String androidAppUrl, String iosAppUrl, Long agencyId);
    
    /**
     * Send registration confirmation email with agency branding
     */
    void sendRegistrationConfirmationEmail(User user, String confirmationUrl, Long agencyId);
}
