package com.cap10mycap10.worklinkservice.dto.settings;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class AgencySettingsCreateDto {

    private boolean chargeVat;

    private BigDecimal vatPercentage;

    private Long agencyId;

    // New tax-related fields
    private boolean defaultTaxInclusive = true;
    private boolean vehiclesTaxableByDefault = true;
    private boolean addonsTaxableByDefault = true;

}
