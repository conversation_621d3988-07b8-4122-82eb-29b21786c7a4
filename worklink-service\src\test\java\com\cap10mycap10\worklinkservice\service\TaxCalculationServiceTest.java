package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.implementation.TaxCalculationServiceImpl;
import com.cap10mycap10.worklinkservice.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class TaxCalculationServiceTest {

    @InjectMocks
    private TaxCalculationServiceImpl taxCalculationService;

    private AgencySettings agencySettings;
    private Vehicle vehicle;
    private VehicleInventory addon;
    private TaxRate customTaxRate;

    @BeforeEach
    void setUp() {
        // Setup agency settings with 15% VAT
        agencySettings = new AgencySettings();
        agencySettings.setChargeVat(true);
        agencySettings.setVatPercentage(new BigDecimal("15.00"));
        agencySettings.setDefaultTaxInclusive(true);
        agencySettings.setVehiclesTaxableByDefault(true);
        agencySettings.setAddonsTaxableByDefault(true);

        // Setup custom tax rate
        customTaxRate = new TaxRate();
        customTaxRate.setId(1L);
        customTaxRate.setName("Reduced VAT");
        customTaxRate.setPercentage(new BigDecimal("5.00"));
        customTaxRate.setActive(true);

        // Setup vehicle
        vehicle = new Vehicle();
        vehicle.setId(1L);
        vehicle.setTaxExempt(false);
        vehicle.setTaxInclusive(null); // Use agency default

        // Setup addon
        addon = new VehicleInventory();
        addon.setId(1L);
        addon.setName("GPS");
        addon.setPrice(10.0f);
        addon.setTaxExempt(false);
        addon.setTaxInclusive(null); // Use agency default
    }

    @Test
    void testCalculateVehicleTax_TaxInclusive() {
        // Given: Vehicle with tax-inclusive pricing
        BigDecimal rate = new BigDecimal("115.00"); // $115 including 15% tax
        
        // When
        TaxCalculationService.TaxCalculationResult result = 
            taxCalculationService.calculateVehicleTax(vehicle, rate, agencySettings);

        // Then
        assertFalse(result.isTaxExempt());
        assertTrue(result.isTaxInclusive());
        assertEquals(new BigDecimal("15.00"), result.getTaxRate());
        assertEquals(new BigDecimal("100.00"), result.getNetAmount());
        assertEquals(new BigDecimal("15.00"), result.getTaxAmount());
        assertEquals(new BigDecimal("115.00"), result.getTotalAmount());
    }

    @Test
    void testCalculateVehicleTax_TaxExclusive() {
        // Given: Vehicle with tax-exclusive pricing
        agencySettings.setDefaultTaxInclusive(false);
        BigDecimal rate = new BigDecimal("100.00"); // $100 excluding tax
        
        // When
        TaxCalculationService.TaxCalculationResult result = 
            taxCalculationService.calculateVehicleTax(vehicle, rate, agencySettings);

        // Then
        assertFalse(result.isTaxExempt());
        assertFalse(result.isTaxInclusive());
        assertEquals(new BigDecimal("15.00"), result.getTaxRate());
        assertEquals(new BigDecimal("100.00"), result.getNetAmount());
        assertEquals(new BigDecimal("15.00"), result.getTaxAmount());
        assertEquals(new BigDecimal("115.00"), result.getTotalAmount());
    }

    @Test
    void testCalculateVehicleTax_TaxExempt() {
        // Given: Tax-exempt vehicle
        vehicle.setTaxExempt(true);
        BigDecimal rate = new BigDecimal("100.00");
        
        // When
        TaxCalculationService.TaxCalculationResult result = 
            taxCalculationService.calculateVehicleTax(vehicle, rate, agencySettings);

        // Then
        assertTrue(result.isTaxExempt());
        assertEquals(BigDecimal.ZERO, result.getTaxRate());
        assertEquals(new BigDecimal("100.00"), result.getNetAmount());
        assertEquals(BigDecimal.ZERO, result.getTaxAmount());
        assertEquals(new BigDecimal("100.00"), result.getTotalAmount());
    }

    @Test
    void testCalculateVehicleTax_CustomTaxRate() {
        // Given: Vehicle with custom tax rate
        vehicle.setCustomTaxRate(customTaxRate);
        BigDecimal rate = new BigDecimal("105.00"); // $105 including 5% tax
        
        // When
        TaxCalculationService.TaxCalculationResult result = 
            taxCalculationService.calculateVehicleTax(vehicle, rate, agencySettings);

        // Then
        assertFalse(result.isTaxExempt());
        assertTrue(result.isTaxInclusive());
        assertEquals(new BigDecimal("5.00"), result.getTaxRate());
        assertEquals(new BigDecimal("100.00"), result.getNetAmount());
        assertEquals(new BigDecimal("5.00"), result.getTaxAmount());
        assertEquals(new BigDecimal("105.00"), result.getTotalAmount());
    }

    @Test
    void testCalculateAddonTax_TaxInclusive() {
        // Given: Addon with tax-inclusive pricing
        BigDecimal rate = new BigDecimal("11.50"); // $11.50 including 15% tax
        
        // When
        TaxCalculationService.TaxCalculationResult result = 
            taxCalculationService.calculateAddonTax(addon, rate, agencySettings);

        // Then
        assertFalse(result.isTaxExempt());
        assertTrue(result.isTaxInclusive());
        assertEquals(new BigDecimal("15.00"), result.getTaxRate());
        assertEquals(new BigDecimal("10.00"), result.getNetAmount());
        assertEquals(new BigDecimal("1.50"), result.getTaxAmount());
        assertEquals(new BigDecimal("11.50"), result.getTotalAmount());
    }

    @Test
    void testCalculateAddonTax_TaxExempt() {
        // Given: Tax-exempt addon
        addon.setTaxExempt(true);
        BigDecimal rate = new BigDecimal("10.00");
        
        // When
        TaxCalculationService.TaxCalculationResult result = 
            taxCalculationService.calculateAddonTax(addon, rate, agencySettings);

        // Then
        assertTrue(result.isTaxExempt());
        assertEquals(BigDecimal.ZERO, result.getTaxRate());
        assertEquals(new BigDecimal("10.00"), result.getNetAmount());
        assertEquals(BigDecimal.ZERO, result.getTaxAmount());
        assertEquals(new BigDecimal("10.00"), result.getTotalAmount());
    }

    @Test
    void testCalculateAddonTax_CustomTaxRate() {
        // Given: Addon with custom tax rate
        addon.setCustomTaxRate(customTaxRate);
        BigDecimal rate = new BigDecimal("10.50"); // $10.50 including 5% tax
        
        // When
        TaxCalculationService.TaxCalculationResult result = 
            taxCalculationService.calculateAddonTax(addon, rate, agencySettings);

        // Then
        assertFalse(result.isTaxExempt());
        assertTrue(result.isTaxInclusive());
        assertEquals(new BigDecimal("5.00"), result.getTaxRate());
        assertEquals(new BigDecimal("10.00"), result.getNetAmount());
        assertEquals(new BigDecimal("0.50"), result.getTaxAmount());
        assertEquals(new BigDecimal("10.50"), result.getTotalAmount());
    }

    @Test
    void testCalculateTax_NoVatCharged() {
        // Given: Agency doesn't charge VAT
        agencySettings.setChargeVat(false);
        BigDecimal rate = new BigDecimal("100.00");
        
        // When
        TaxCalculationService.TaxCalculationResult result = 
            taxCalculationService.calculateVehicleTax(vehicle, rate, agencySettings);

        // Then
        assertTrue(result.isTaxExempt());
        assertEquals(BigDecimal.ZERO, result.getTaxRate());
        assertEquals(new BigDecimal("100.00"), result.getNetAmount());
        assertEquals(BigDecimal.ZERO, result.getTaxAmount());
        assertEquals(new BigDecimal("100.00"), result.getTotalAmount());
    }

    @Test
    void testApplyTaxToInvoiceItem_Vehicle() {
        // Given: Invoice item for vehicle
        InvoiceItem invoiceItem = new InvoiceItem();
        invoiceItem.setTotal(new BigDecimal("115.00"));
        invoiceItem.setDescription("Car rental for MONDAY Toyota Camry");
        
        // When
        taxCalculationService.applyTaxToInvoiceItem(invoiceItem, vehicle, null, agencySettings);

        // Then
        assertFalse(invoiceItem.getTaxExempt());
        assertEquals(new BigDecimal("15.00"), invoiceItem.getTaxRate());
        assertEquals(new BigDecimal("15.00"), invoiceItem.getTaxAmount());
        assertEquals(new BigDecimal("100.00"), invoiceItem.getNetAmount());
        assertTrue(invoiceItem.getTaxInclusive());
        assertEquals(new BigDecimal("115.00"), invoiceItem.getTotal());
    }

    @Test
    void testApplyTaxToInvoiceItem_Addon() {
        // Given: Invoice item for addon
        InvoiceItem invoiceItem = new InvoiceItem();
        invoiceItem.setTotal(new BigDecimal("11.50"));
        invoiceItem.setDescription("GPS");
        
        // When
        taxCalculationService.applyTaxToInvoiceItem(invoiceItem, null, addon, agencySettings);

        // Then
        assertFalse(invoiceItem.getTaxExempt());
        assertEquals(new BigDecimal("15.00"), invoiceItem.getTaxRate());
        assertEquals(new BigDecimal("1.50"), invoiceItem.getTaxAmount());
        assertEquals(new BigDecimal("10.00"), invoiceItem.getNetAmount());
        assertTrue(invoiceItem.getTaxInclusive());
        assertEquals(new BigDecimal("11.50"), invoiceItem.getTotal());
    }

    @Test
    void testCalculateTax_ZeroRate() {
        // Given: Zero tax rate
        BigDecimal rate = new BigDecimal("100.00");
        agencySettings.setVatPercentage(BigDecimal.ZERO);

        // When
        TaxCalculationService.TaxCalculationResult result =
            taxCalculationService.calculateVehicleTax(vehicle, rate, agencySettings);

        // Then
        assertTrue(result.isTaxExempt());
        assertEquals(BigDecimal.ZERO, result.getTaxRate());
        assertEquals(new BigDecimal("100.00"), result.getNetAmount());
        assertEquals(BigDecimal.ZERO, result.getTaxAmount());
        assertEquals(new BigDecimal("100.00"), result.getTotalAmount());
    }

    @Test
    void testCalculateTax_HighTaxRate() {
        // Given: High tax rate (25%)
        agencySettings.setVatPercentage(new BigDecimal("25.00"));
        BigDecimal rate = new BigDecimal("125.00"); // $125 including 25% tax

        // When
        TaxCalculationService.TaxCalculationResult result =
            taxCalculationService.calculateVehicleTax(vehicle, rate, agencySettings);

        // Then
        assertFalse(result.isTaxExempt());
        assertTrue(result.isTaxInclusive());
        assertEquals(new BigDecimal("25.00"), result.getTaxRate());
        assertEquals(new BigDecimal("100.00"), result.getNetAmount());
        assertEquals(new BigDecimal("25.00"), result.getTaxAmount());
        assertEquals(new BigDecimal("125.00"), result.getTotalAmount());
    }

    @Test
    void testCalculateTax_SmallAmount() {
        // Given: Small amount with tax
        BigDecimal rate = new BigDecimal("1.15"); // $1.15 including 15% tax

        // When
        TaxCalculationService.TaxCalculationResult result =
            taxCalculationService.calculateVehicleTax(vehicle, rate, agencySettings);

        // Then
        assertFalse(result.isTaxExempt());
        assertTrue(result.isTaxInclusive());
        assertEquals(new BigDecimal("15.00"), result.getTaxRate());
        assertEquals(new BigDecimal("1.00"), result.getNetAmount());
        assertEquals(new BigDecimal("0.15"), result.getTaxAmount());
        assertEquals(new BigDecimal("1.15"), result.getTotalAmount());
    }

    @Test
    void testIsVehicleTaxExempt_ExplicitExemption() {
        // Given: Vehicle explicitly marked as tax exempt
        vehicle.setTaxExempt(true);

        // When
        boolean result = taxCalculationService.isVehicleTaxExempt(vehicle, agencySettings);

        // Then
        assertTrue(result);
    }

    @Test
    void testIsVehicleTaxExempt_AgencyDefault() {
        // Given: Agency default is vehicles not taxable
        agencySettings.setVehiclesTaxableByDefault(false);
        vehicle.setTaxExempt(null);

        // When
        boolean result = taxCalculationService.isVehicleTaxExempt(vehicle, agencySettings);

        // Then
        assertTrue(result);
    }

    @Test
    void testIsAddonTaxExempt_ExplicitExemption() {
        // Given: Addon explicitly marked as tax exempt
        addon.setTaxExempt(true);

        // When
        boolean result = taxCalculationService.isAddonTaxExempt(addon, agencySettings);

        // Then
        assertTrue(result);
    }

    @Test
    void testGetEffectiveVehicleTaxRate_CustomRate() {
        // Given: Vehicle with custom tax rate
        vehicle.setCustomTaxRate(customTaxRate);

        // When
        BigDecimal result = taxCalculationService.getEffectiveVehicleTaxRate(vehicle, agencySettings);

        // Then
        assertEquals(new BigDecimal("5.00"), result);
    }

    @Test
    void testGetEffectiveVehicleTaxRate_AgencyDefault() {
        // Given: Vehicle without custom tax rate
        vehicle.setCustomTaxRate(null);

        // When
        BigDecimal result = taxCalculationService.getEffectiveVehicleTaxRate(vehicle, agencySettings);

        // Then
        assertEquals(new BigDecimal("15.00"), result);
    }
}
