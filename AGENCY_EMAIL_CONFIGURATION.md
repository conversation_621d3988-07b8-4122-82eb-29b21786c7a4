# Agency Email Configuration System

## Overview

The Agency Email Configuration System enables white-label email sending for agencies using the ERP system. Instead of all emails being sent from the system's default email server, agencies can configure their own SMTP settings to send emails with their own branding and domain.

## Features

- **Agency-Specific SMTP Configuration**: Each agency can configure their own email server settings
- **Automatic Fallback**: If an agency doesn't have email configuration or it's not verified, the system falls back to the default email server
- **Complete White-Label Branding**: Agencies can customize:
  - From name and email address
  - Reply-to address and support email
  - Website URL and company logo
  - Email content branding (system name replacement)
- **Content Personalization**: All email content automatically replaces system names (MyWorklink, KarLink) with agency-specific branding
- **Configuration Testing**: Built-in test functionality to verify SMTP settings work correctly
- **Caching**: Email senders are cached for performance optimization
- **Security**: SMTP passwords are encrypted in the database
- **Validation**: Comprehensive validation of email configurations

## Database Schema

### agency_email_configuration Table

```sql
CREATE TABLE agency_email_configuration (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    agency_id BIGINT NOT NULL UNIQUE,
    
    -- SMTP Configuration
    smtp_host VARCHAR(255) NOT NULL,
    smtp_port INT NOT NULL,
    smtp_username VARCHAR(255) NOT NULL,
    smtp_password VARCHAR(500) NOT NULL,
    smtp_auth BOOLEAN NOT NULL DEFAULT TRUE,
    smtp_starttls_enable BOOLEAN NOT NULL DEFAULT TRUE,
    smtp_starttls_required BOOLEAN NOT NULL DEFAULT TRUE,
    smtp_ssl_enable BOOLEAN NOT NULL DEFAULT FALSE,
    smtp_ssl_socket_factory_class VARCHAR(255),
    
    -- Email Configuration
    from_email VARCHAR(255) NOT NULL,
    from_name VARCHAR(255) NOT NULL,
    reply_to_email VARCHAR(255),
    support_email VARCHAR(255),
    website_url VARCHAR(500),
    logo_url VARCHAR(500),
    
    -- Status and Verification
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_verified BOOLEAN NOT NULL DEFAULT FALSE,
    last_test_date DATETIME,
    last_test_result TEXT,
    
    -- Audit Fields
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    notes TEXT
);
```

## API Endpoints

### Get Agency Email Configuration
```http
GET /api/v1/agency-email-config/agency/{agencyId}
```

### Create/Update Agency Email Configuration
```http
POST /api/v1/agency-email-config/agency/{agencyId}
Content-Type: application/json

{
    "smtpHost": "smtp.gmail.com",
    "smtpPort": 587,
    "smtpUsername": "<EMAIL>",
    "smtpPassword": "app-password",
    "smtpAuth": true,
    "smtpStarttlsEnable": true,
    "smtpStarttlsRequired": true,
    "smtpSslEnable": false,
    "fromEmail": "<EMAIL>",
    "fromName": "Agency Name",
    "replyToEmail": "<EMAIL>",
    "supportEmail": "<EMAIL>",
    "websiteUrl": "https://agency.com",
    "logoUrl": "https://agency.com/logo.png",
    "isActive": true
}
```

### Test Email Configuration
```http
POST /api/v1/agency-email-config/test
Content-Type: application/json

{
    "agencyId": 123,
    "testEmailAddress": "<EMAIL>"
}
```

### Activate/Deactivate Configuration
```http
PUT /api/v1/agency-email-config/agency/{agencyId}/activate
PUT /api/v1/agency-email-config/agency/{agencyId}/deactivate
```

### Get Configuration Status
```http
GET /api/v1/agency-email-config/agency/{agencyId}/status
```

## Usage Examples

### Sending Agency-Specific Emails

```java
// In your service class
@Autowired
private EmailService emailService;

// Send email with agency branding
Long agencyId = 123L;
List<String> recipients = Arrays.asList("<EMAIL>");
String subject = "Welcome to Our Service";
String message = "Thank you for joining us!";

// This will use agency-specific email configuration if available
emailService.sendEmailAsUser(recipients, subject, message, agencyId);

// For shift notifications
emailService.sendShiftWithAttachment(shift, agencyEmails, workerEmails);
// The method automatically detects agency from shift and uses appropriate configuration
```

### Configuration Management

```java
@Autowired
private AgencyEmailConfigurationService emailConfigService;

// Create new configuration
AgencyEmailConfiguration config = AgencyEmailConfiguration.builder()
    .agencyId(123L)
    .smtpHost("smtp.gmail.com")
    .smtpPort(587)
    .smtpUsername("<EMAIL>")
    .smtpPassword("app-password")
    .fromEmail("<EMAIL>")
    .fromName("Agency Name")
    .isActive(true)
    .build();

emailConfigService.save(config);

// Test configuration
boolean testResult = emailConfigService.testConfiguration(123L, "<EMAIL>");
```

## Common SMTP Configurations

### Gmail
```json
{
    "smtpHost": "smtp.gmail.com",
    "smtpPort": 587,
    "smtpAuth": true,
    "smtpStarttlsEnable": true,
    "smtpStarttlsRequired": true,
    "smtpSslEnable": false
}
```

### Outlook/Hotmail
```json
{
    "smtpHost": "smtp-mail.outlook.com",
    "smtpPort": 587,
    "smtpAuth": true,
    "smtpStarttlsEnable": true,
    "smtpStarttlsRequired": true,
    "smtpSslEnable": false
}
```

### Custom SMTP (SSL)
```json
{
    "smtpHost": "mail.yourdomain.com",
    "smtpPort": 465,
    "smtpAuth": true,
    "smtpStarttlsEnable": false,
    "smtpStarttlsRequired": false,
    "smtpSslEnable": true,
    "smtpSslSocketFactoryClass": "javax.net.ssl.SSLSocketFactory"
}
```

## Security Considerations

1. **Password Encryption**: SMTP passwords should be encrypted before storing in the database
2. **Access Control**: Only authorized users should be able to configure email settings
3. **Validation**: All email configurations are validated before saving
4. **Testing**: Always test configurations before marking them as verified

## Troubleshooting

### Common Issues

1. **Authentication Failed**: Check username/password and ensure app passwords are used for Gmail
2. **Connection Timeout**: Verify SMTP host and port settings
3. **SSL/TLS Issues**: Check SSL/TLS configuration and socket factory settings
4. **Emails Not Branded**: Ensure configuration is active and verified

### Logging

The system provides comprehensive logging for email operations:
- Configuration creation/updates
- Email sending attempts
- Test results
- Fallback scenarios

Check application logs for detailed information about email operations.

## Migration

To migrate existing systems:

1. Run the database migration script `V3__Add_Agency_Email_Configuration.sql`
2. Existing email functionality continues to work with default configuration
3. Agencies can gradually configure their own email settings
4. No downtime required for migration

## Performance

- Email senders are cached to avoid recreating them for each email
- Cache can be cleared per agency or globally via API endpoints
- Fallback to default sender is fast and transparent

## White-Label Content Transformation

The system automatically transforms email content to use agency-specific branding:

### System Name Replacement
- **MyWorklink** → Agency Name (e.g., "Premium Car Rentals")
- **KarLink** → Agency Name
- **myworklink.uk** → Agency Website URL
- **Default Company** → Agency Name

### Email Content Examples

**Before (Default System):**
```
"Thank you for choosing MyWorklink for your booking!"
"Please login to https://online.myworklink.uk to view details"
"We hope you had a pleasant trip with your rental car from MyWorklink"
```

**After (Agency Branded):**
```
"Thank you for choosing Premium Car Rentals for your booking!"
"Please login to https://premiumrentals.com to view details"
"We hope you had a pleasant trip with your rental car from Premium Car Rentals"
```

### Affected Email Types
- Vehicle booking confirmations
- Payment confirmations
- Shift notifications
- Worker invitations
- Booking cancellations
- Rating requests
- System notifications

## Future Enhancements

- Email template customization per agency
- Bounce handling and tracking
- Email analytics and reporting
- Bulk email configuration import/export
- Integration with email service providers (SendGrid, Mailgun, etc.)
- Custom email templates per agency
- Multi-language support with agency-specific translations
