package com.cap10mycap10.ouathservice.service;

import com.cap10mycap10.ouathservice.dto.AgencyEmailConfigurationDto;
import com.cap10mycap10.ouathservice.entity.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.MailException;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * Enhanced email service implementation that supports agency-specific email configuration
 */
@Service("emailService")
@Slf4j
public class EnhancedEmailServiceImpl implements EmailService {

    @Autowired
    private AgencyService agencyService;

    @Autowired
    private EmailSenderFactory emailSenderFactory;

    @Override
    @Async("threadPoolTaskExecutor")
    public void sendEmail(List<String> to, String subject, String text, Long agencyId) {
        AgencyEmailConfigurationDto agencyConfig = agencyService.getAgencyEmailConfiguration(agencyId);
        JavaMailSender sender = emailSenderFactory.getEmailSender(agencyConfig);
        EmailSenderFactory.EmailConfiguration config = emailSenderFactory.getEmailConfiguration(agencyConfig);

        sendEmailInternal(sender, config, to, subject, text);



    }

    @Override
    @Async("threadPoolTaskExecutor")
    public void sendUnknownDeviceNotificationEmail(User user, String deviceInfo, String location, Long agencyId) {
        log.info("Sending unknown device notification email for user: {}", user.getEmail());

        AgencyEmailConfigurationDto agencyConfig = agencyService.getAgencyEmailConfiguration(agencyId);
        EmailSenderFactory.EmailConfiguration config = emailSenderFactory.getEmailConfiguration(agencyConfig);

        String subject = "New Device Login - " + config.getFromName();
        String message = buildUnknownDeviceMessage(user, deviceInfo, location, config);

        JavaMailSender sender = emailSenderFactory.getEmailSender(agencyConfig);
        sendEmailInternal(sender, config, List.of(user.getEmail()), subject, message);
    }

    private void sendEmailInternal(JavaMailSender sender, EmailSenderFactory.EmailConfiguration config, 
                                 List<String> to, String subject, String text) {
        try {
            MimeMessage message = sender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            
            String[] recipients = to.toArray(new String[0]);
            helper.setTo(recipients);
            helper.setSubject(subject);

            // Set from address and name
            try {
                helper.setFrom(config.getFromEmail(), config.getFromName());
            } catch (UnsupportedEncodingException e) {
                helper.setFrom(config.getFromEmail());
            }

            // Set reply-to if available
            if (config.getReplyToEmail() != null) {
                helper.setReplyTo(config.getReplyToEmail());
            }

            // Set email content
            String htmlContent = getHtmlEmail(text, subject, config);
            helper.setText(htmlContent, true);

            sender.send(message);
            log.info("Email sent successfully to: {}", to);
        } catch (MessagingException | MailException e) {
            log.error("Failed to send email to: {}", to, e);
        }
    }

    private String transformContentForAgency(String content, EmailSenderFactory.EmailConfiguration config) {
        if (content == null) return content;

        // Replace system names with agency name
        content = content.replaceAll("(?i)MyWorklink|KarLink|MyKarlink", config.getFromName());
        
        // Replace website URLs
        if (config.getWebsiteUrl() != null) {
            content = content.replaceAll("https://online\\.myworklink\\.uk", config.getWebsiteUrl());
            content = content.replaceAll("https://myworklink\\.uk", config.getWebsiteUrl());
        }
        
        // Replace support email
        if (config.getSupportEmail() != null) {
            content = content.replaceAll("support@myworklink\\.uk", config.getSupportEmail());
        }

        return content;
    }

    private String transformSubjectForAgency(String subject, EmailSenderFactory.EmailConfiguration config) {
        if (subject == null) return subject;
        
        // Replace system names in subject
        return subject.replaceAll("(?i)MyWorklink|KarLink|MyKarlink", config.getFromName());
    }

    private String buildOtpMessage(User user, String otp, EmailSenderFactory.EmailConfiguration config) {
        return "<p>Hi " + user.getFirstName() + ",</p>" +
               "<p>Your login code for " + config.getFromName() + " is:</p>" +
               "<div style='text-align: center; margin: 20px 0;'>" +
               "<span style='font-size: 24px; font-weight: bold; background-color: #f0f0f0; padding: 10px 20px; border-radius: 5px; letter-spacing: 3px;'>" + otp + "</span>" +
               "</div>" +
               "<p><strong>Important:</strong> This code will expire in 10 minutes for security reasons.</p>" +
               "<p>If you didn't request this code, please ignore this email or contact our support team at <a href='mailto:" + config.getSupportEmail() + "'>" + config.getSupportEmail() + "</a>.</p>" +
               "<p>Best regards,<br>" + config.getFromName() + " Security Team</p>";
    }

    private String buildUnknownDeviceMessage(User user, String deviceInfo, String location, EmailSenderFactory.EmailConfiguration config) {
        return "<p>Hi " + user.getFirstName() + ",</p>" +
               "<p>We noticed a login to your " + config.getFromName() + " account from a new device:</p>" +
               "<div style='background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 15px 0;'>" +
               "<p><strong>Device:</strong> " + deviceInfo + "</p>" +
               "<p><strong>Location:</strong> " + location + "</p>" +
               "<p><strong>Time:</strong> " + java.time.LocalDateTime.now().toString() + "</p>" +
               "</div>" +
               "<p>If this was you, you can safely ignore this email. If you don't recognize this activity, please:</p>" +
               "<ul>" +
               "<li>Change your password immediately</li>" +
               "<li>Review your account for any unauthorized changes</li>" +
               "<li>Contact our support team at <a href='mailto:" + config.getSupportEmail() + "'>" + config.getSupportEmail() + "</a></li>" +
               "</ul>" +
               "<p>For your security, we recommend enabling two-factor authentication if you haven't already.</p>" +
               "<p>Best regards,<br>" + config.getFromName() + " Security Team</p>";
    }

    private String getHtmlEmail(String text, String subject, EmailSenderFactory.EmailConfiguration config) {
        text = text.replaceAll("(\r\n|\n)", "<br />");
        
        return "<!DOCTYPE html>" +
               "<html>" +
               "<head>" +
               "<meta charset='UTF-8'>" +
               "<title>" + subject + "</title>" +
               "</head>" +
               "<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>" +
               "<div style='max-width: 600px; margin: 0 auto; padding: 20px;'>" +
               (config.getLogoUrl() != null ? 
                   "<div style='text-align: center; margin-bottom: 20px;'>" +
                   "<img src='" + config.getLogoUrl() + "' alt='" + config.getFromName() + "' style='max-height: 80px;'>" +
                   "</div>" : "") +
               "<div style='background-color: #f9f9f9; padding: 20px; border-radius: 5px;'>" +
               text +
               "</div>" +
               "<div style='margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;'>" +
               "<p>This email was sent by " + config.getFromName() + "</p>" +
               (config.getWebsiteUrl() != null ? "<p>Visit our website: <a href='" + config.getWebsiteUrl() + "'>" + config.getWebsiteUrl() + "</a></p>" : "") +
               "</div>" +
               "</div>" +
               "</body>" +
               "</html>";
    }
}
