-------------------------------------------------------------------------------
Test set: com.cap10mycap10.worklinkservice.service.TaxCalculationServiceTest
-------------------------------------------------------------------------------
Tests run: 18, Failures: 7, Errors: 0, Skipped: 0, Time elapsed: 0.023 s <<< FAILURE! - in com.cap10mycap10.worklinkservice.service.TaxCalculationServiceTest
testCalculateTax_SmallAmount  Time elapsed: 0 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <1.00> but was: <1.0000000000>
	at com.cap10mycap10.worklinkservice.service.TaxCalculationServiceTest.testCalculateTax_SmallAmount(TaxCalculationServiceTest.java:293)

testCalculateAddonTax_CustomTaxRate  Time elapsed: 0.001 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <10.00> but was: <10.0000000000>
	at com.cap10mycap10.worklinkservice.service.TaxCalculationServiceTest.testCalculateAddonTax_CustomTaxRate(TaxCalculationServiceTest.java:182)

testCalculateVehicleTax_TaxExclusive  Time elapsed: 0 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <15.00> but was: <15.0000000000>
	at com.cap10mycap10.worklinkservice.service.TaxCalculationServiceTest.testCalculateVehicleTax_TaxExclusive(TaxCalculationServiceTest.java:91)

testCalculateVehicleTax_CustomTaxRate  Time elapsed: 0.003 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <100.00> but was: <100.0000000000>
	at com.cap10mycap10.worklinkservice.service.TaxCalculationServiceTest.testCalculateVehicleTax_CustomTaxRate(TaxCalculationServiceTest.java:127)

testCalculateVehicleTax_TaxInclusive  Time elapsed: 0.001 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <100.00> but was: <100.0000000000>
	at com.cap10mycap10.worklinkservice.service.TaxCalculationServiceTest.testCalculateVehicleTax_TaxInclusive(TaxCalculationServiceTest.java:71)

testCalculateAddonTax_TaxInclusive  Time elapsed: 0.001 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <10.00> but was: <10.0000000000>
	at com.cap10mycap10.worklinkservice.service.TaxCalculationServiceTest.testCalculateAddonTax_TaxInclusive(TaxCalculationServiceTest.java:145)

testCalculateTax_HighTaxRate  Time elapsed: 0 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <100.00> but was: <100.0000000000>
	at com.cap10mycap10.worklinkservice.service.TaxCalculationServiceTest.testCalculateTax_HighTaxRate(TaxCalculationServiceTest.java:275)

