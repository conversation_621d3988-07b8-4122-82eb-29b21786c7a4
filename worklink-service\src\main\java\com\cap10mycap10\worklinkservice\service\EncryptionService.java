package com.cap10mycap10.worklinkservice.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * Service for encrypting and decrypting sensitive data using AES-GCM encryption.
 * This service is used to protect sensitive information like SMTP passwords in the database.
 */
@Service
@Slf4j
public class EncryptionService {

    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/GCM/NoPadding";
    private static final int GCM_IV_LENGTH = 12; // 96 bits
    private static final int GCM_TAG_LENGTH = 16; // 128 bits

    @Value("${app.encryption.key:MySecretEncryptionKey123456789012}")
    private String encryptionKey;

    /**
     * Encrypts the given plaintext using AES-GCM encryption.
     *
     * @param plaintext the text to encrypt
     * @return encrypted text encoded in Base64, or null if input is null/empty
     * @throws RuntimeException if encryption fails
     */
    public String encrypt(String plaintext) {
        if (plaintext == null || plaintext.trim().isEmpty()) {
            return plaintext;
        }

        try {
            // Generate a random IV for each encryption
            byte[] iv = new byte[GCM_IV_LENGTH];
            new SecureRandom().nextBytes(iv);

            // Create cipher instance
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            SecretKeySpec keySpec = new SecretKeySpec(getKeyBytes(), ALGORITHM);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);

            // Initialize cipher for encryption
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, gcmSpec);

            // Encrypt the plaintext
            byte[] encryptedData = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));

            // Combine IV and encrypted data
            byte[] encryptedWithIv = new byte[GCM_IV_LENGTH + encryptedData.length];
            System.arraycopy(iv, 0, encryptedWithIv, 0, GCM_IV_LENGTH);
            System.arraycopy(encryptedData, 0, encryptedWithIv, GCM_IV_LENGTH, encryptedData.length);

            // Encode to Base64 for storage
            String encrypted = Base64.getEncoder().encodeToString(encryptedWithIv);
            log.debug("Successfully encrypted data of length: {}", plaintext.length());
            return encrypted;

        } catch (Exception e) {
            log.error("Failed to encrypt data", e);
            throw new RuntimeException("Encryption failed", e);
        }
    }

    /**
     * Decrypts the given encrypted text using AES-GCM decryption.
     *
     * @param encryptedText the Base64 encoded encrypted text
     * @return decrypted plaintext, or null if input is null/empty
     * @throws RuntimeException if decryption fails
     */
    public String decrypt(String encryptedText) {
        if (encryptedText == null || encryptedText.trim().isEmpty()) {
            return encryptedText;
        }

        try {
            // Decode from Base64
            byte[] encryptedWithIv = Base64.getDecoder().decode(encryptedText);

            // Extract IV and encrypted data
            byte[] iv = new byte[GCM_IV_LENGTH];
            byte[] encryptedData = new byte[encryptedWithIv.length - GCM_IV_LENGTH];
            System.arraycopy(encryptedWithIv, 0, iv, 0, GCM_IV_LENGTH);
            System.arraycopy(encryptedWithIv, GCM_IV_LENGTH, encryptedData, 0, encryptedData.length);

            // Create cipher instance
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            SecretKeySpec keySpec = new SecretKeySpec(getKeyBytes(), ALGORITHM);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);

            // Initialize cipher for decryption
            cipher.init(Cipher.DECRYPT_MODE, keySpec, gcmSpec);

            // Decrypt the data
            byte[] decryptedData = cipher.doFinal(encryptedData);
            String decrypted = new String(decryptedData, StandardCharsets.UTF_8);
            log.debug("Successfully decrypted data");
            return decrypted;

        } catch (Exception e) {
            log.error("Failed to decrypt data", e);
            throw new RuntimeException("Decryption failed", e);
        }
    }

    /**
     * Checks if the given text appears to be encrypted (Base64 encoded).
     *
     * @param text the text to check
     * @return true if the text appears to be encrypted, false otherwise
     */
    public boolean isEncrypted(String text) {
        if (text == null || text.trim().isEmpty()) {
            return false;
        }

        try {
            // Check if it's valid Base64 and has the expected minimum length
            byte[] decoded = Base64.getDecoder().decode(text);
            return decoded.length > GCM_IV_LENGTH + GCM_TAG_LENGTH;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    /**
     * Gets the encryption key as bytes, ensuring it's the correct length for AES-256.
     *
     * @return 32-byte key for AES-256
     */
    private byte[] getKeyBytes() {
        byte[] key = encryptionKey.getBytes(StandardCharsets.UTF_8);
        
        // Ensure key is exactly 32 bytes for AES-256
        if (key.length < 32) {
            byte[] paddedKey = new byte[32];
            System.arraycopy(key, 0, paddedKey, 0, key.length);
            // Fill remaining bytes with a pattern
            for (int i = key.length; i < 32; i++) {
                paddedKey[i] = (byte) (i % 256);
            }
            return paddedKey;
        } else if (key.length > 32) {
            byte[] truncatedKey = new byte[32];
            System.arraycopy(key, 0, truncatedKey, 0, 32);
            return truncatedKey;
        }
        
        return key;
    }

    /**
     * Generates a new random encryption key for AES-256.
     * This method can be used to generate secure keys for production use.
     *
     * @return Base64 encoded random key
     */
    public static String generateRandomKey() {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
            keyGenerator.init(256); // AES-256
            SecretKey secretKey = keyGenerator.generateKey();
            return Base64.getEncoder().encodeToString(secretKey.getEncoded());
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate encryption key", e);
        }
    }
}
