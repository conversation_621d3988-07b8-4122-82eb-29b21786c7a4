package com.cap10mycap10.ouathservice.service;


import com.cap10mycap10.ouathservice.dao.DeviceMetadataRepository;
import com.cap10mycap10.ouathservice.dao.UserRepository;
import com.cap10mycap10.ouathservice.entity.*;
import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.exception.AddressNotFoundException;
import com.maxmind.geoip2.exception.GeoIp2Exception;
import com.maxmind.geoip2.model.CityResponse;
import com.netflix.servo.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;
import ua_parser.Client;
import ua_parser.Parser;

import javax.mail.MessagingException;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.net.InetAddress;
import java.util.*;

import static com.netflix.eureka.ServerRequestAuthFilter.UNKNOWN;
import static java.util.Objects.nonNull;

@Service
@Slf4j
public class DeviceMetaDataServiceImpl implements DeviceMetaDataService {

    @Value("${env.companyName}")
    private String companyName;
    @Autowired
    private DeviceMetadataRepository deviceMetadataRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private EmailService emailService;

    @Override
    public String extractIp(HttpServletRequest request) {
        String clientIp;
        String clientXForwardedForIp = request
                .getHeader("x-forwarded-for");
//        if (nonNull(clientXForwardedForIp)) {
//            clientIp = parseXForwardedHeader(clientXForwardedForIp);
//        } else {
            clientIp = request.getRemoteAddr();
//        }
        return clientIp;
    }

    @Override
    public String getIpLocation(String ip) throws IOException, GeoIp2Exception {
        return "unknown";

//        the code below is working on local machine but not in a docker container. The Gerlite2-city file cant be found in docker container

//        ClassPathResource resource = new ClassPathResource("GeoLite2-City.mmdb");
//
//        File database = resource.getFile();
//
//
//        DatabaseReader dbReader = new DatabaseReader.Builder(database).build();
//
//        String location = UNKNOWN;
//        InetAddress ipAddress = InetAddress.getByName(ip);
//
//        try{
//            CityResponse cityResponse = dbReader
//                    .city(ipAddress);
//
//            if (nonNull(cityResponse) &&
//                    nonNull(cityResponse.getCity()) &&
//                    !Strings.isNullOrEmpty(cityResponse.getCity().getName())) {
//                location = cityResponse.getCity().getName();
//            }
//        }catch (AddressNotFoundException e){
//            log.debug("User location not found:{}", ipAddress);
//        }
//
//
//
//        return location;
    }

    @Override
    public String getDeviceDetails(String userAgent) {
        String deviceDetails = UNKNOWN;

        Parser parser = new Parser();

        Client client = parser.parse(userAgent);
        if (nonNull(client.userAgent)) {
            deviceDetails = client.userAgent.family
                    + " " + client.userAgent.major + "."
                    + client.userAgent.minor + " - "
                    + client.os.family + " " + client.os.major
                    + "." + client.os.minor;
        }
        return deviceDetails;
    }

    @Override
    public void verifyDevice(User user, HttpServletRequest request, Long agencyId) throws IOException, GeoIp2Exception {

        String ip = extractIp(request);
        String location = getIpLocation(ip);

        String deviceDetails = getDeviceDetails(request.getHeader("user-agency"));

        DeviceMetadata existingDevice
                = findExistingDevice(user.getId(), deviceDetails, location);

        if (Objects.isNull(existingDevice)) {

            unknownDeviceNotification(deviceDetails, location,
                    ip, user.getEmail(), request.getLocale(),  agencyId);

            DeviceMetadata deviceMetadata = new DeviceMetadata();
            deviceMetadata.setUserId(user.getId());
            deviceMetadata.setLocation(location);
            deviceMetadata.setDeviceDetails(deviceDetails);
            deviceMetadata.setLastLoggedIn(new Date());
            deviceMetadataRepository.save(deviceMetadata);
        } else {
            existingDevice.setLastLoggedIn(new Date());
            deviceMetadataRepository.save(existingDevice);
        }
    }
    @Override
    public void unknownDeviceNotification(String deviceDetails, String location, String ip, String email, Locale locale, Long agencyId) {
        // Find the user by username (email) to get agency-specific configuration
        User user = userRepository.findByUsername(email).orElse(null);

        if (user != null) {
            // Use enhanced email service with agency branding
            emailService.sendUnknownDeviceNotificationEmail(user, deviceDetails, location, agencyId);
        } else {
            // Fallback to basic email if user not found
            final String message = "An unknown device just logged into your "+companyName+" account. If this is not you please reset your password, this will automatically log you out of all sessions.";
            emailService.sendEmail(Collections.singletonList(email), companyName+" new login", message, agencyId);
        }
    }


    @Override
    public void loginNotification(User user, HttpServletRequest request, Long agencyId) throws IOException, GeoIp2Exception {

                verifyDevice(user, request, agencyId);

    }

    @Override
    public DeviceMetadata findExistingDevice(
            Long userId, String deviceDetails, String location) {
        List<DeviceMetadata> knownDevices
                = deviceMetadataRepository.findByUserId(userId);

        for (DeviceMetadata existingDevice : knownDevices) {
            if (existingDevice.getDeviceDetails().equals(deviceDetails)
                    && existingDevice.getLocation().equals(location)) {
                return existingDevice;
            }
        }
        return null;
    }

}
