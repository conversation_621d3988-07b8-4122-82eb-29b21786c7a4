-- Migration script to add comprehensive tax system support
-- This script adds new tables and columns to support the enhanced tax calculation system

-- Create TaxRate table
CREATE TABLE IF NOT EXISTS tax_rate (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    percentage DECIMAL(5,2) NOT NULL,
    description TEXT,
    active BOOLEAN NOT NULL DEFAULT TRUE,
    agency_id BIGINT NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    last_modified_by VA<PERSON><PERSON><PERSON>(255),
    UNIQUE KEY unique_tax_rate_name_agency (name, agency_id),
    INDEX idx_tax_rate_agency_active (agency_id, active)
);

-- Add new tax-related columns to agency_settings table
ALTER TABLE agency_settings 
ADD COLUMN IF NOT EXISTS default_tax_inclusive BOOLEAN NOT NULL DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS vehicles_taxable_by_default BOOLEAN NOT NULL DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS addons_taxable_by_default BOOLEAN NOT NULL DEFAULT TRUE;

-- Add tax-related columns to vehicle table
ALTER TABLE vehicle 
ADD COLUMN IF NOT EXISTS tax_exempt BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS tax_inclusive BOOLEAN,
ADD COLUMN IF NOT EXISTS custom_tax_rate_id BIGINT,
ADD CONSTRAINT fk_vehicle_custom_tax_rate FOREIGN KEY (custom_tax_rate_id) REFERENCES tax_rate(id);

-- Add tax-related columns to vehicle_inventory table
ALTER TABLE vehicle_inventory 
ADD COLUMN IF NOT EXISTS tax_exempt BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS tax_inclusive BOOLEAN,
ADD COLUMN IF NOT EXISTS custom_tax_rate_id BIGINT,
ADD CONSTRAINT fk_vehicle_inventory_custom_tax_rate FOREIGN KEY (custom_tax_rate_id) REFERENCES tax_rate(id);

-- Add tax-related columns to invoice_item table
ALTER TABLE invoice_item 
ADD COLUMN IF NOT EXISTS tax_exempt BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS tax_rate DECIMAL(5,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS tax_amount DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS net_amount DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS tax_inclusive BOOLEAN DEFAULT FALSE;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_vehicle_tax_exempt ON vehicle(tax_exempt);
CREATE INDEX IF NOT EXISTS idx_vehicle_inventory_tax_exempt ON vehicle_inventory(tax_exempt);
CREATE INDEX IF NOT EXISTS idx_invoice_item_tax_exempt ON invoice_item(tax_exempt);
CREATE INDEX IF NOT EXISTS idx_vehicle_custom_tax_rate ON vehicle(custom_tax_rate_id);
CREATE INDEX IF NOT EXISTS idx_vehicle_inventory_custom_tax_rate ON vehicle_inventory(custom_tax_rate_id);
CREATE INDEX IF NOT EXISTS idx_tax_rate_percentage ON tax_rate(percentage);
CREATE INDEX IF NOT EXISTS idx_tax_rate_created_date ON tax_rate(created_date);

-- Add constraints to ensure data integrity
ALTER TABLE tax_rate
ADD CONSTRAINT chk_tax_rate_percentage_range CHECK (percentage >= 0.00 AND percentage <= 100.00),
ADD CONSTRAINT chk_tax_rate_name_not_empty CHECK (TRIM(name) != ''),
ADD CONSTRAINT chk_tax_rate_agency_id_positive CHECK (agency_id > 0);

-- Add constraints for vehicle tax settings
ALTER TABLE vehicle
ADD CONSTRAINT chk_vehicle_tax_rate_when_not_exempt CHECK (
    tax_exempt = TRUE OR custom_tax_rate_id IS NULL OR
    EXISTS (SELECT 1 FROM tax_rate WHERE id = custom_tax_rate_id AND active = TRUE)
);

-- Add constraints for vehicle_inventory tax settings
ALTER TABLE vehicle_inventory
ADD CONSTRAINT chk_vehicle_inventory_tax_rate_when_not_exempt CHECK (
    tax_exempt = TRUE OR custom_tax_rate_id IS NULL OR
    EXISTS (SELECT 1 FROM tax_rate WHERE id = custom_tax_rate_id AND active = TRUE)
);

-- Insert default tax rates for existing agencies
INSERT INTO tax_rate (name, percentage, description, agency_id, active)
SELECT 
    'Standard VAT' as name,
    COALESCE(vat_percentage, 15.00) as percentage,
    'Standard VAT rate' as description,
    agency_id,
    TRUE as active
FROM agency_settings 
WHERE NOT EXISTS (
    SELECT 1 FROM tax_rate 
    WHERE tax_rate.agency_id = agency_settings.agency_id 
    AND tax_rate.name = 'Standard VAT'
);

-- Insert zero rate for all agencies
INSERT INTO tax_rate (name, percentage, description, agency_id, active)
SELECT 
    'Zero Rate' as name,
    0.00 as percentage,
    'Zero tax rate for exempt items' as description,
    agency_id,
    TRUE as active
FROM agency_settings 
WHERE NOT EXISTS (
    SELECT 1 FROM tax_rate 
    WHERE tax_rate.agency_id = agency_settings.agency_id 
    AND tax_rate.name = 'Zero Rate'
);

-- Update existing invoice_item records to set net_amount equal to total for backward compatibility
UPDATE invoice_item 
SET net_amount = total, 
    tax_amount = 0.00, 
    tax_rate = 0.00,
    tax_exempt = FALSE,
    tax_inclusive = FALSE
WHERE net_amount IS NULL OR net_amount = 0.00;

-- Update agency_settings with default values for new columns
UPDATE agency_settings 
SET default_tax_inclusive = TRUE,
    vehicles_taxable_by_default = TRUE,
    addons_taxable_by_default = TRUE
WHERE default_tax_inclusive IS NULL;

-- Update vehicles with default tax settings
UPDATE vehicle 
SET tax_exempt = FALSE
WHERE tax_exempt IS NULL;

-- Update vehicle_inventory with default tax settings  
UPDATE vehicle_inventory 
SET tax_exempt = FALSE
WHERE tax_exempt IS NULL;
