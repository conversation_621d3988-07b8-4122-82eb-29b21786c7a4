package com.cap10mycap10.ouathservice.service;


import com.cap10mycap10.ouathservice.dto.UserLogin;
import com.cap10mycap10.ouathservice.dto.UserLoginRequest;
import com.cap10mycap10.ouathservice.entity.DeviceMetadata;
import com.cap10mycap10.ouathservice.entity.ResultDTO;
import com.cap10mycap10.ouathservice.entity.User;
import com.cap10mycap10.ouathservice.exception.BusinessValidationException;
import com.maxmind.geoip2.exception.GeoIp2Exception;
import org.springframework.security.core.Authentication;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Locale;

public interface DeviceMetaDataService {
    String extractIp(HttpServletRequest request);

    String getIpLocation(String ip) throws IOException, GeoIp2Exception;

    String getDeviceDetails(String userAgent);

    void verifyDevice(User user, HttpServletRequest request, Long agencyId) throws IOException, GeoIp2Exception;

    void unknownDeviceNotification(String deviceDetails, String location, String ip, String email, Locale locale, Long agencyId);

    void loginNotification(User user,  HttpServletRequest request, Long agencyId) throws IOException, GeoIp2Exception;

    DeviceMetadata findExistingDevice(
            Long userId, String deviceDetails, String location);
}
