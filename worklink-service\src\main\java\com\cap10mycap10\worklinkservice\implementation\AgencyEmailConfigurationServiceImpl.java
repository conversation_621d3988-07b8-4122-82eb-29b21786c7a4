package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.AgencyEmailConfigurationRepository;
import com.cap10mycap10.worklinkservice.dao.AgencyRepository;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.model.AgencyEmailConfiguration;
import com.cap10mycap10.worklinkservice.service.AgencyEmailConfigurationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Properties;

/**
 * Service implementation for managing agency email configurations.
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class AgencyEmailConfigurationServiceImpl implements AgencyEmailConfigurationService {

    private final AgencyEmailConfigurationRepository emailConfigRepository;
    private final AgencyRepository agencyRepository;

    @Override
    public AgencyEmailConfiguration save(AgencyEmailConfiguration configuration) {
        log.info("Saving email configuration for agency ID: {}", configuration.getAgencyId());

        // Validate the configuration
        try {
            configuration.validateConfiguration();
        } catch (IllegalArgumentException e) {
            log.error("Invalid email configuration for agency ID: {}", configuration.getAgencyId(), e);
            throw new IllegalArgumentException("Email configuration validation failed: " + e.getMessage());
        }

        // Validate that the agency exists
        if (!agencyRepository.existsById(configuration.getAgencyId())) {
            throw new RecordNotFoundException("Agency with ID " + configuration.getAgencyId() + " not found");
        }

        return emailConfigRepository.save(configuration);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AgencyEmailConfiguration> findByAgencyId(Long agencyId) {
        return emailConfigRepository.findByAgencyId(agencyId);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AgencyEmailConfiguration> findActiveByAgencyId(Long agencyId) {
        return emailConfigRepository.findActiveByAgencyId(agencyId);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AgencyEmailConfiguration> findVerifiedActiveByAgencyId(Long agencyId) {
        return emailConfigRepository.findVerifiedActiveByAgencyId(agencyId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AgencyEmailConfiguration> findAll() {
        return emailConfigRepository.findAll();
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AgencyEmailConfiguration> findById(Long id) {
        return emailConfigRepository.findById(id);
    }

    @Override
    public void deleteById(Long id) {
        log.info("Deleting email configuration with ID: {}", id);
        emailConfigRepository.deleteById(id);
    }

    @Override
    public void deleteByAgencyId(Long agencyId) {
        log.info("Deleting email configuration for agency ID: {}", agencyId);
        emailConfigRepository.deleteByAgencyId(agencyId);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasActiveConfiguration(Long agencyId) {
        return emailConfigRepository.hasActiveConfiguration(agencyId);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasVerifiedActiveConfiguration(Long agencyId) {
        return emailConfigRepository.hasVerifiedActiveConfiguration(agencyId);
    }

    @Override
    public boolean testConfiguration(Long agencyId, String testEmailAddress) {
        log.info("Testing email configuration for agency ID: {} with test email: {}", agencyId, testEmailAddress);
        
        Optional<AgencyEmailConfiguration> configOpt = findActiveByAgencyId(agencyId);
        if (configOpt.isEmpty()) {
            log.warn("No active email configuration found for agency ID: {}", agencyId);
            return false;
        }

        AgencyEmailConfiguration config = configOpt.get();
        
        try {
            JavaMailSender testSender = createMailSender(config);
            
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(config.getFromEmail());
            message.setTo(testEmailAddress);
            message.setSubject("Email Configuration Test - " + config.getFromName());
            message.setText("This is a test email to verify your email configuration is working correctly.\n\n" +
                          "Agency: " + config.getFromName() + "\n" +
                          "SMTP Host: " + config.getSmtpHost() + "\n" +
                          "SMTP Port: " + config.getSmtpPort() + "\n" +
                          "Test Date: " + LocalDateTime.now() + "\n\n" +
                          "If you received this email, your configuration is working properly!");
            
            testSender.send(message);
            
            // Mark as verified
            markAsVerified(agencyId, "Test email sent successfully to " + testEmailAddress);
            
            log.info("Email configuration test successful for agency ID: {}", agencyId);
            return true;
            
        } catch (Exception e) {
            log.error("Email configuration test failed for agency ID: {}", agencyId, e);
            markAsUnverified(agencyId, "Test failed: " + e.getMessage());
            return false;
        }
    }

    @Override
    public AgencyEmailConfiguration activateConfiguration(Long agencyId) {
        log.info("Activating email configuration for agency ID: {}", agencyId);
        
        AgencyEmailConfiguration config = findByAgencyId(agencyId)
            .orElseThrow(() -> new RecordNotFoundException("Email configuration not found for agency ID: " + agencyId));
        
        config.setIsActive(true);
        return save(config);
    }

    @Override
    public AgencyEmailConfiguration deactivateConfiguration(Long agencyId) {
        log.info("Deactivating email configuration for agency ID: {}", agencyId);
        
        AgencyEmailConfiguration config = findByAgencyId(agencyId)
            .orElseThrow(() -> new RecordNotFoundException("Email configuration not found for agency ID: " + agencyId));
        
        config.setIsActive(false);
        return save(config);
    }

    @Override
    public AgencyEmailConfiguration markAsVerified(Long agencyId, String testResult) {
        AgencyEmailConfiguration config = findByAgencyId(agencyId)
            .orElseThrow(() -> new RecordNotFoundException("Email configuration not found for agency ID: " + agencyId));
        
        config.setIsVerified(true);
        config.setLastTestDate(LocalDateTime.now());
        config.setLastTestResult(testResult);
        
        return save(config);
    }

    @Override
    public AgencyEmailConfiguration markAsUnverified(Long agencyId, String testResult) {
        AgencyEmailConfiguration config = findByAgencyId(agencyId)
            .orElseThrow(() -> new RecordNotFoundException("Email configuration not found for agency ID: " + agencyId));
        
        config.setIsVerified(false);
        config.setLastTestDate(LocalDateTime.now());
        config.setLastTestResult(testResult);
        
        return save(config);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByAgencyId(Long agencyId) {
        return emailConfigRepository.existsByAgencyId(agencyId);
    }

    /**
     * Create a JavaMailSender instance from agency email configuration
     */
    private JavaMailSender createMailSender(AgencyEmailConfiguration config) {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();

        mailSender.setHost(config.getSmtpHost());
        mailSender.setPort(config.getSmtpPort());
        mailSender.setUsername(config.getSmtpUsername());
        mailSender.setPassword(config.getDecryptedSmtpPassword()); // Use decrypted password
        
        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", config.getSmtpAuth().toString());
        props.put("mail.smtp.starttls.enable", config.getSmtpStarttlsEnable().toString());
        props.put("mail.smtp.starttls.required", config.getSmtpStarttlsRequired().toString());
        props.put("mail.debug", "false"); // Set to true for debugging
        
        if (config.getSmtpSslEnable()) {
            props.put("mail.smtp.ssl.enable", "true");
            String socketFactoryClass = config.getEffectiveSocketFactoryClass();
            if (socketFactoryClass != null) {
                props.put("mail.smtp.socketFactory.class", socketFactoryClass);
            }
        }
        
        return mailSender;
    }
}
