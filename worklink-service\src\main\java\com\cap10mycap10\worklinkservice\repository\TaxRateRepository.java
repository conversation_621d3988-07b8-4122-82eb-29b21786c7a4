package com.cap10mycap10.worklinkservice.repository;

import com.cap10mycap10.worklinkservice.model.TaxRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TaxRateRepository extends JpaRepository<TaxRate, Long> {

    /**
     * Find all active tax rates for an agency
     */
    List<TaxRate> findByAgencyIdAndActiveTrue(Long agencyId);

    /**
     * Find all tax rates for an agency (active and inactive)
     */
    List<TaxRate> findByAgencyId(Long agencyId);

    /**
     * Find a tax rate by name and agency
     */
    Optional<TaxRate> findByNameAndAgencyId(String name, Long agencyId);

    /**
     * Check if a tax rate name exists for an agency
     */
    boolean existsByNameAndAgencyId(String name, Long agencyId);

    /**
     * Find the default tax rate for an agency (usually the one with highest usage or marked as default)
     */
    @Query("SELECT tr FROM TaxRate tr WHERE tr.agencyId = :agencyId AND tr.active = true ORDER BY tr.createdDate ASC")
    Optional<TaxRate> findDefaultTaxRateByAgencyId(@Param("agencyId") Long agencyId);

    /**
     * Check if a tax rate is in use by any vehicles
     */
    @Query("SELECT COUNT(v) > 0 FROM Vehicle v WHERE v.customTaxRate.id = :taxRateId")
    boolean isUsedByVehicles(@Param("taxRateId") Long taxRateId);

    /**
     * Check if a tax rate is in use by any vehicle addons
     */
    @Query("SELECT COUNT(vi) > 0 FROM VehicleInventory vi WHERE vi.customTaxRate.id = :taxRateId")
    boolean isUsedByVehicleInventory(@Param("taxRateId") Long taxRateId);

    /**
     * Check if a tax rate is in use by any invoice items
     */
    @Query("SELECT COUNT(ii) > 0 FROM InvoiceItem ii WHERE ii.taxRate = (SELECT tr.percentage FROM TaxRate tr WHERE tr.id = :taxRateId)")
    boolean isUsedByInvoiceItems(@Param("taxRateId") Long taxRateId);
}
