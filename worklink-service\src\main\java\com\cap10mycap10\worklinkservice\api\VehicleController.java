package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.VehicleFilterDto;
import com.cap10mycap10.worklinkservice.dto.VehicleInventoryTaxUpdateDto;
import com.cap10mycap10.worklinkservice.dto.VehiclePhotoDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.CommentDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleAvailabilityDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleAvailabilityRemoveDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleAvailabilityUpdateDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleSearchDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.vehiclelog.VehicleLogDto;
import com.cap10mycap10.worklinkservice.enums.*;
import com.cap10mycap10.worklinkservice.model.VehicleFilter;
import com.cap10mycap10.worklinkservice.model.VehicleInventory;
import com.cap10mycap10.worklinkservice.model.VehiclePhoto;
import com.cap10mycap10.worklinkservice.service.VehicleService;
import com.cap10mycap10.worklinkservice.service.VehicleLogService;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.maps.model.Vehicle;
import lombok.extern.slf4j.Slf4j;
import org.exolab.castor.types.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Objects.nonNull;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class VehicleController {
    @Autowired
    private VehicleService vehicleService;
    @Autowired
    private VehicleLogService logService;

    @PostMapping(value = "vehicle", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<VehicleDto> create(@RequestBody VehicleDto vehicle) {
        log.info("Request to add vehicle with : {}", vehicle);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri).body(vehicleService.save(vehicle));
    }
    @PutMapping(value = "vehicle", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<VehicleDto> update(@RequestBody VehicleDto vehicle) {
        log.info("Request to update vehicle with : {}", vehicle);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication != null) {
            for (GrantedAuthority authority : authentication.getAuthorities()) {
                if ("ROLE_ADMIN_ADMIN".equals(authority.getAuthority())) {
                    return ResponseEntity.created(uri).body(vehicleService.update(vehicle, true));
                }
            }
        }


        return ResponseEntity.created(uri).body(vehicleService.update(vehicle, false));
    }

    @GetMapping(value = "vehicle/{id}")
    public ResponseEntity<VehicleDto> findById(@PathVariable("id") Long id) {
        return ResponseEntity.ok(vehicleService.findById(id));
    }


    @PostMapping(value = "vehicle/search/filtered/{page}/{size}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Page<VehicleDto>> getFilteredVehicles(@PathVariable("page") int page,
                                                   @PathVariable("size") int size,@RequestBody VehicleFilterDto filter) {
        log.info("Request to view filtered vehicles with : {}", filter);
        return ResponseEntity.ok(vehicleService.filterVehiclesDto(filter,  PageRequest.of(page, size)));
    }


    @PutMapping(value = "vehicle/approve/{vehicleId}")
    public ResponseEntity<VehicleDto> approveVehicle(@PathVariable("vehicleId") Long vehicleId) {
        return ResponseEntity.ok(vehicleService.approveVehicle(vehicleId));
    }
    @PutMapping(value = "vehicle/enable/{vehicleId}")
    public ResponseEntity<VehicleDto> enableVehicle(@PathVariable("vehicleId") Long vehicleId) {
        return ResponseEntity.ok(vehicleService.enableVehicle(vehicleId));
    }
    @PutMapping(value = "vehicle/disable/{vehicleId}")
    public ResponseEntity<VehicleDto> disableVehicle(@PathVariable("vehicleId") Long vehicleId) {
        return ResponseEntity.ok(vehicleService.disableVehicle(vehicleId));
    }

    @PutMapping(value = "vehicle/reject/{vehicleId}",consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<VehicleDto> rejectVehicle(@PathVariable("vehicleId") Long vehicleId, @RequestBody CommentDto comment) {
        return ResponseEntity.ok(vehicleService.rejectVehicle(vehicleId, comment.getComment()));
    }


    @GetMapping(value = "vehicle/public-search/{page}/{size}")
    public ResponseEntity<Page<VehicleDto>> findPublicVehicles(@PathVariable("page") int page,
                                                               @PathVariable("size") int size,
                                                               @RequestParam(value = "agencyId", required = false) Long agencyId,
                                                               @RequestParam(value = "searchCriteria", required = false) String searchCriteria,
                                                               @RequestParam(value = "start") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime start,
                                                               @RequestParam(value = "end") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime end,
                                                               @RequestParam(value = "location") Long location,
                                                               @RequestParam(value = "vehicleType", required = false) VehicleType vehicleType,
                                                               @RequestParam(value = "minPrice", required = false) Float minPrice,
                                                               @RequestParam(value = "maxPrice", required = false) Float maxPrice,
                                                               @RequestParam(value = "maxDeposit", required = false) Float maxDeposit,
                                                               @RequestParam(value = "lowMileageLimit", required = false) Float lowMileageLimit,
                                                               @RequestParam(value = "transmission", required = false) TransmissionType transmission,
                                                               @RequestParam(value = "hasPromotion", required = false) Boolean hasPromotion,
                                                               @RequestParam(value = "promotionType", required = false) PromotionType promotionType,
                                                               @RequestParam(value = "sortBy", defaultValue = "random") String sortBy,
                                                               @RequestParam(value = "sortDirection", defaultValue = "ASC") String sortDirection
    ) {
        VehicleSearchDto vehicleSearchDto = new VehicleSearchDto();

        vehicleSearchDto.setAgencyId(agencyId);
        vehicleSearchDto.setSearchCriteria(searchCriteria);
        vehicleSearchDto.setStart(start);
        vehicleSearchDto.setEnd(end);
        vehicleSearchDto.setLocation(location);
        vehicleSearchDto.setVehicleType(vehicleType);
        vehicleSearchDto.setMinPrice(minPrice);
        vehicleSearchDto.setMaxPrice(maxPrice);
        vehicleSearchDto.setMaxDeposit(maxDeposit);
        vehicleSearchDto.setLowMileageLimit(lowMileageLimit);
        vehicleSearchDto.setTransmission(transmission);
        vehicleSearchDto.setHasPromotion(hasPromotion);
        vehicleSearchDto.setPromotionType(promotionType);
        vehicleSearchDto.setSortBy(sortBy);
        vehicleSearchDto.setSortDirection(sortDirection);

        // Create sort object based on sortBy and sortDirection
        Sort.Direction direction = sortDirection.equalsIgnoreCase("DESC") ?
                Sort.Direction.DESC : Sort.Direction.ASC;

        PageRequest pageRequest;
        if ("random".equalsIgnoreCase(sortBy)) {
            // For random sorting, don't apply any sort to PageRequest
            pageRequest = PageRequest.of(page, size);
        } else {
            // For other sorting options, create appropriate sort
            String sortField = mapSortField(sortBy);
            pageRequest = PageRequest.of(page, size, direction, sortField);
        }

        return ResponseEntity.ok(vehicleService.findPublicVehicles(vehicleSearchDto, pageRequest));
    }

    /**
     * Maps user-friendly sort field names to actual entity field names
     */
    private String mapSortField(String sortBy) {
        switch (sortBy.toLowerCase()) {
            case "dailyrate":
            case "daily_rate":
            case "rate":
                return "minDailyRate"; // This will be handled specially in the repository
            case "rating":
            case "ratings":
                return "avgRating"; // This will be handled specially in the repository
            case "name":
                return "name";
            case "model":
                return "model";
            case "price":
                return "minDailyRate";
            default:
                return "id"; // Default fallback
        }
    }

    @GetMapping(value = "vehicle/agency/find-available/{agencyId}/{page}/{size}")
    public ResponseEntity<Page<VehicleDto>> findAgencyAvailableVehicles(@PathVariable("page") int page,
                                                               @PathVariable("size") int size,
                                                               @PathVariable(value = "agencyId") Long agencyId,
                                                               @RequestParam(value = "searchCriteria", required = false) String searchCriteria,
                                                               @RequestParam(value = "start") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime start,
                                                               @RequestParam(value = "end") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime end,
                                                               @RequestParam(value = "location", required = false) Long location,
                                                               @RequestParam(value = "vehicleType", required = false) VehicleType vehicleType,
                                                               @RequestParam(value = "hasPromotion", required = false) Boolean hasPromotion,
                                                               @RequestParam(value = "promotionType", required = false) PromotionType promotionType
    ) {

        VehicleSearchDto vehicleSearchDto = new VehicleSearchDto();

        vehicleSearchDto.setAgencyId(agencyId);
        vehicleSearchDto.setSearchCriteria(searchCriteria);
        vehicleSearchDto.setStart(start);
        vehicleSearchDto.setEnd(end);
        vehicleSearchDto.setLocation(location);
        vehicleSearchDto.setVehicleType(vehicleType);
        vehicleSearchDto.setHasPromotion(hasPromotion);
        vehicleSearchDto.setPromotionType(promotionType);
        // Set default sorting for agency available vehicles
        vehicleSearchDto.setSortBy("random");
        vehicleSearchDto.setSortDirection("ASC");

        return ResponseEntity.ok(vehicleService.findPublicVehicles(vehicleSearchDto, PageRequest.of(page, size)));
    }

    @GetMapping(value = "vehicle/{id}/{status}/{page}/{size}")
    public ResponseEntity<Page<VehicleLogDto>> findVehicleLogs(@PathVariable("id") Long id,
                                                               @PathVariable("page") int page,
                                                               @PathVariable("size") int size,
                                                               @PathVariable("status") LogStatus status) {
        return ResponseEntity.ok(logService.findForVehicle(id, status, PageRequest.of(page, size)));
    }

    @GetMapping(value = "vehicle/agency/type/{agencyId}/{page}/{size}")
    public ResponseEntity<Page<VehicleDto>> findByAgencyId(
            @PathVariable("agencyId") Long agencyId,
            @PathVariable("page") int page,
            @PathVariable("size") int size,
            @RequestParam(value = "status", required = false) AssetStatus status,
            @RequestParam(value = "searchQuery", required = false) String searchQuery,
            @RequestParam(value = "sort", defaultValue = "id") String sort,
            @RequestParam(value = "direction", defaultValue = "ASC") String direction) {



        // Create sort object
        Sort.Direction sortDirection = direction.equalsIgnoreCase("DESC") ?
                Sort.Direction.DESC : Sort.Direction.ASC;
        PageRequest pageRequest = PageRequest.of(page, size, sortDirection, sort);

        return ResponseEntity.ok(vehicleService.findByAgencyIdAndTypePaged(
                agencyId,
                status,
                searchQuery,
                pageRequest));
    }

//    @GetMapping(value = "vehicle/agency/type/{agencyId}/{status}/{page}/{size}")
//    public ResponseEntity<Page<VehicleDto>> findByAgencyId(@PathVariable("agencyId") Long agencyId,
//                                                           @PathVariable("page") int page,
//                                                           @PathVariable("size") int size,
//                                                           @PathVariable("status") AssetStatus status,
//                                                           @RequestParam(value = "searchCriteria", required = false) String searchCriteria) {
//        return ResponseEntity.ok(vehicleService.findByAgencyIdAndTypePaged(agencyId, status,"", PageRequest.of(page, size)));
//    }

    @GetMapping(value = "vehicle/admin/{page}/{size}")
    public ResponseEntity<Page<VehicleDto>> findByAdminId(
            @PathVariable("page") int page,
            @PathVariable("size") int size,
            @RequestParam(value = "agencyId", required = false) Long agencyId,
    @RequestParam(value = "status", required = false) AssetStatus status,
    @RequestParam(value = "searchQuery", required = false) String searchQuery,
    @RequestParam(value = "sort", defaultValue = "id") String sort,
    @RequestParam(value = "direction", defaultValue = "ASC") String direction) {



        // Create sort object
        Sort.Direction sortDirection = direction.equalsIgnoreCase("DESC") ?
                Sort.Direction.DESC : Sort.Direction.ASC;
        PageRequest pageRequest = PageRequest.of(page, size, sortDirection, sort);

        return ResponseEntity.ok(vehicleService.findByAgencyIdAndTypePaged(
                agencyId,
                status,
                searchQuery,
                pageRequest));
    }


    @PostMapping(value = "vehicle/addons/document",consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity<Object> addDocument(@RequestParam(value = "file", required = false) MultipartFile file,
                                             @RequestParam("vehicleId") Long vehicleId,
                                             @RequestParam("expiryDate") String expiryDate,
                                             @RequestParam(value = "lastDate", required = false) String lastDate,
                                             @RequestParam(value = "lastMileage", required = false) Integer lastMileage,
                                             @RequestParam(value = "expiryMileage", required = false) Integer expiryMileage,
                                             @RequestParam(value = "documentId", required = false) Long documentId,
                                             @RequestParam("docName") String docName) {

        log.info("Request to add vehicleDocument : {}",vehicleId);

        var expiryDate1 = nonNull(expiryDate)&&!expiryDate.isBlank()?LocalDate.parse(expiryDate):null;
        var expiryDate2 = nonNull(lastDate)&&!lastDate.isBlank()?LocalDate.parse(lastDate):null;
        vehicleService.addDocument(documentId, vehicleId,docName,expiryDate1, file, expiryDate2,
                lastMileage,
                expiryMileage);
        return  ResponseEntity.noContent().build();
    }

    @PostMapping(value = "vehicle/addons/photo/{vehicleId}")
    public ResponseEntity<Object> addPhoto(@RequestBody VehiclePhotoDto file,
                                             @PathVariable("vehicleId") Long vehicleId) {

        log.info("Request to add vehicleDocument : {}", vehicleId);
        vehicleService.addPhoto( vehicleId, file.getUrls());
        return  ResponseEntity.noContent().build();
    }



    @PutMapping(value = "vehicle/addons/set-main-photo/{id}")
    public ResponseEntity<Object> setMainPhoto(
                                             @PathVariable("id") Long id) {

        log.info("Request to add vehicleDocument : {}", id);
        vehicleService.setMainPhoto( id);
        return  ResponseEntity.noContent().build();
    }

    @DeleteMapping(value = "vehicle/addons/photo/{id}")
    public ResponseEntity<Object> deletePhoto(@PathVariable("id") Long id) {
        log.info("Request to add vehicleDocument : {}", id);
        vehicleService.deletePhoto( id);
        return  ResponseEntity.noContent().build();
    }

    @DeleteMapping(value = "vehicle/addons/inventory/{id}")
    public ResponseEntity<Object> deleteInventory(@PathVariable("id") Long id) {
        log.info("Request to delete inventory : {}", id);
        vehicleService.deleteInventory(id);
        return  ResponseEntity.noContent().build();
    }


    @DeleteMapping(value = "vehicle/addons/document/{id}")
    public ResponseEntity<Object> deleteDocuments(@PathVariable("id") Long id) {
        log.info("Request to delete documents : {}", id);
        vehicleService.deleteDocuments(id);
        return  ResponseEntity.noContent().build();
    }


    @PutMapping(value = "vehicle/rates", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<VehicleDto> updateRates(@RequestBody VehicleDto asset) {
        log.info("Request to update asset rates with : {}", asset);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri).body(vehicleService.updateRates(asset));
    }

    @PostMapping(value = "vehicle/addons/inventory",consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> addInventory(            @RequestBody VehicleInventory vehicleInventory) {
        vehicleService.saveInventory( vehicleInventory);
        return  ResponseEntity.noContent().build();
    }

    @PutMapping(value = "vehicle-inventory/{id}/tax-settings", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> updateInventoryTaxSettings(
            @PathVariable("id") Long id,
            @RequestBody VehicleInventoryTaxUpdateDto taxUpdateDto) {
        log.info("Request to update tax settings for inventory item: {}", id);
        vehicleService.updateInventoryTaxSettings(id, taxUpdateDto);
        return ResponseEntity.ok().build();
    }

    @GetMapping(value = "vehicle/{vehicleId}/availability")
    public ResponseEntity<VehicleAvailabilityDto> getVehicleAvailability(@PathVariable("vehicleId") Long vehicleId) {
        log.info("Request to get availability for vehicle: {}", vehicleId);
        return ResponseEntity.ok(vehicleService.getVehicleAvailability(vehicleId));
    }

    @PostMapping(value = "vehicle/{vehicleId}/availability", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> updateVehicleAvailability(
            @PathVariable("vehicleId") Long vehicleId,
            @RequestParam("isAvailable") Boolean isAvailable,
            @RequestBody VehicleAvailabilityUpdateDto updateDto) {
        log.info("Request to update availability for vehicle: {}", vehicleId);


        if (isAvailable) {
            vehicleService.addVehicleAvailability(vehicleId, updateDto.getDates());
        } else {
            vehicleService.addVehicleUnAvailability(vehicleId, updateDto.getDates());
        }
        return  ResponseEntity.noContent().build();
    }



    @DeleteMapping(value = "vehicle/{vehicleId}/availability/all")
    public ResponseEntity<VehicleAvailabilityDto> clearAllVehicleAvailability(
            @PathVariable("vehicleId") Long vehicleId) {
        log.info("Request to clear all unavailable dates for vehicle: {}", vehicleId);
        return ResponseEntity.ok(vehicleService.clearAllVehicleAvailability(vehicleId));
    }

    // New endpoints for managing vehicle locations

    @PostMapping(value = "vehicle/{vehicleId}/locations/{locationId}")
    public ResponseEntity<Object> addVehicleLocation(
            @PathVariable("vehicleId") Long vehicleId,
            @PathVariable("locationId") Long locationId,
            @RequestParam(value = "active", defaultValue = "true") Boolean active) {
        log.info("Request to add location {} to vehicle {}", locationId, vehicleId);
        vehicleService.addVehicleLocation(vehicleId, locationId, active);
        return ResponseEntity.noContent().build();
    }

    @DeleteMapping(value = "vehicle/{vehicleId}/locations/{locationId}")
    public ResponseEntity<Object> removeVehicleLocation(
            @PathVariable("vehicleId") Long vehicleId,
            @PathVariable("locationId") Long locationId) {
        log.info("Request to remove location {} from vehicle {}", locationId, vehicleId);
        vehicleService.removeVehicleLocation(vehicleId, locationId);
        return ResponseEntity.noContent().build();
    }

    @PutMapping(value = "vehicle/{vehicleId}/locations/{locationId}/active")
    public ResponseEntity<Object> setVehicleLocationActive(
            @PathVariable("vehicleId") Long vehicleId,
            @PathVariable("locationId") Long locationId,
            @RequestParam("active") Boolean active) {
        log.info("Request to set location {} active status to {} for vehicle {}", locationId, active, vehicleId);
        vehicleService.setVehicleLocationActive(vehicleId, locationId, active);
        return ResponseEntity.noContent().build();
    }

    @GetMapping(value = "vehicle/{vehicleId}/locations")
    public ResponseEntity<List<Long>> getVehicleLocations(@PathVariable("vehicleId") Long vehicleId) {
        log.info("Request to get all locations for vehicle {}", vehicleId);
        return ResponseEntity.ok(vehicleService.getVehicleLocationIds(vehicleId));
    }

    @GetMapping(value = "vehicle/{vehicleId}/locations/active")
    public ResponseEntity<List<Long>> getActiveVehicleLocations(@PathVariable("vehicleId") Long vehicleId) {
        log.info("Request to get active locations for vehicle {}", vehicleId);
        return ResponseEntity.ok(vehicleService.getActiveVehicleLocationIds(vehicleId));
    }

//    @PostMapping(value = "vehicle/search/by-locations/{page}/{size}")
//    public ResponseEntity<Page<VehicleDto>> findVehiclesByLocations(
//            @PathVariable("page") int page,
//            @PathVariable("size") int size,
//            @RequestParam("locationIds") List<Long> locationIds,
//            @RequestParam(value = "agencyId", required = false) Long agencyId,
//            @RequestParam(value = "status", required = false) AssetStatus status) {
//        log.info("Request to find vehicles by locations: {}", locationIds);
//        PageRequest pageRequest = PageRequest.of(page, size);
//        return ResponseEntity.ok(vehicleService.findByLocationIds(locationIds, agencyId, status, pageRequest));
//    }

}
