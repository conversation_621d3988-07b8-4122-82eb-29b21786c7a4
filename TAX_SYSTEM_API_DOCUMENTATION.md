# Tax System API Documentation

## Overview

The Worklink tax system provides comprehensive tax calculation capabilities for vehicle bookings and shift invoices. It supports tax-exempt items, custom tax rates, and both tax-inclusive and tax-exclusive pricing.

## Tax Rate Management

### Get Tax Rates by Agency

**GET** `/api/tax-rates/agency/{agencyId}`

Returns all tax rates for a specific agency.

**Parameters:**
- `agencyId` (path): The agency ID

**Response:**
```json
[
  {
    "id": 1,
    "name": "Standard VAT",
    "percentage": 15.00,
    "description": "Standard VAT rate",
    "active": true,
    "agencyId": 123
  }
]
```

### Get Active Tax Rates by Agency

**GET** `/api/tax-rates/agency/{agencyId}/active`

Returns only active tax rates for a specific agency.

### Create Tax Rate

**POST** `/api/tax-rates`

Creates a new tax rate.

**Request Body:**
```json
{
  "name": "Reduced VAT",
  "percentage": 5.00,
  "description": "Reduced VAT rate for specific items",
  "active": true,
  "agencyId": 123
}
```

### Update Tax Rate

**PUT** `/api/tax-rates/{id}`

Updates an existing tax rate.

### Activate/Deactivate Tax Rate

**PATCH** `/api/tax-rates/{id}/activate`
**PATCH** `/api/tax-rates/{id}/deactivate`

Activates or deactivates a tax rate.

## Vehicle Inventory Tax Settings

### Update Vehicle Inventory Tax Settings

**PUT** `/api/v1/vehicle-inventory/{id}/tax-settings`

Updates tax configuration for a vehicle inventory item (addon).

**Request Body:**
```json
{
  "id": 456,
  "taxExempt": false,
  "taxInclusive": true,
  "customTaxRateId": 2
}
```

**Parameters:**
- `taxExempt`: Whether the item is exempt from tax
- `taxInclusive`: Whether the price includes tax (null = use agency default)
- `customTaxRateId`: Custom tax rate ID (null = use agency default)

## Agency Settings

### Update Agency Tax Settings

**PUT** `/api/agency-settings`

Updates agency tax configuration including new tax behavior settings.

**Request Body:**
```json
{
  "agencyId": 123,
  "chargeVat": true,
  "vatPercentage": 15.00,
  "defaultTaxInclusive": true,
  "vehiclesTaxableByDefault": true,
  "addonsTaxableByDefault": true
}
```

**New Fields:**
- `defaultTaxInclusive`: Default pricing type (true = tax-inclusive, false = tax-exclusive)
- `vehiclesTaxableByDefault`: Whether new vehicles are taxable by default
- `addonsTaxableByDefault`: Whether new addons are taxable by default

## Tax Calculation Business Rules

### Tax Calculation Logic

1. **Tax Exemption Check**: If an item is marked as tax-exempt, no tax is applied
2. **Tax Rate Determination**: 
   - Use custom tax rate if specified
   - Otherwise use agency default VAT percentage
3. **Tax Calculation**:
   - **Tax-Inclusive**: `netAmount = total / (1 + taxRate/100)`, `taxAmount = total - netAmount`
   - **Tax-Exclusive**: `netAmount = total`, `taxAmount = total * taxRate/100`, `total = netAmount + taxAmount`

### Invoice Item Tax Fields

Enhanced invoice items now include:
```json
{
  "id": 789,
  "description": "GPS Navigation",
  "total": 11.50,
  "taxExempt": false,
  "taxRate": 15.00,
  "taxAmount": 1.50,
  "netAmount": 10.00,
  "taxInclusive": true
}
```

### Vehicle Tax Configuration

Vehicles can be configured with:
- `taxExempt`: Boolean flag for tax exemption
- `taxInclusive`: Pricing type (null = use agency default)
- `customTaxRateId`: Reference to custom tax rate

### Vehicle Addon Tax Configuration

Vehicle addons (inventory items) support the same tax configuration as vehicles.

## Error Responses

### 404 Not Found
```json
{
  "error": "Tax rate not found with id: 123"
}
```

### 400 Bad Request
```json
{
  "error": "Tax percentage must be between 0 and 100"
}
```

## Migration Notes

- Existing data is automatically migrated to support new tax fields
- Default tax rates are created for all agencies
- Backward compatibility is maintained for existing invoices
- New tax calculations apply to invoices created after the migration

## Testing

The system includes comprehensive tests covering:
- Tax-inclusive pricing calculations
- Tax-exclusive pricing calculations
- Tax-exempt items
- Custom tax rates
- Edge cases (zero rates, high rates, small amounts)
- Agency default behaviors

## Integration Notes

- Tax calculations are applied automatically during invoice generation
- Vehicle booking invoices use vehicle and addon tax configurations
- Shift invoices use agency default tax settings
- All tax amounts are rounded to 2 decimal places using HALF_EVEN rounding
