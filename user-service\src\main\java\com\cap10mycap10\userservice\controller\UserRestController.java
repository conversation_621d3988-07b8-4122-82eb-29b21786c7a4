package com.cap10mycap10.userservice.controller;


import com.cap10mycap10.userservice.dto.*;
import com.cap10mycap10.userservice.exception.BusinessValidationException;
import com.cap10mycap10.userservice.model.Role;
import com.cap10mycap10.userservice.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import javax.mail.MessagingException;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.net.URI;
import java.util.List;
import java.util.Locale;

@Slf4j
@RestController
@RequestMapping("api/v1/user-management")
public class UserRestController {


    private final UserService userService;

    public UserRestController(final UserService userService) {
        this.userService = userService;
    }

    // Registration
    @PostMapping("/user/registration")
    public ResponseEntity<UserResponse> registerUserAccount(@Valid @RequestBody final UserDto accountDto,
                                                            final HttpServletRequest request,@RequestParam(required = false) Long agencyId) throws MessagingException {
        UserResponse userResponse = userService.registerNewUserAccount(accountDto, request, agencyId);
        return ResponseEntity.ok(userResponse);
    }

    // Registration
    @PostMapping("/user/role")
    public ResponseEntity<UserResponse> editUserRole(@Valid @RequestBody final AddRoleToUserDto accountDto,
                                                            final HttpServletRequest request) throws MessagingException {
        UserResponse userResponse = userService.addRoleToUser(accountDto, request);
        return ResponseEntity.ok(userResponse);
    }

    // Registration

    @PutMapping("/user")
    public ResponseEntity<UserResponse> editTellerAccount( @Valid @RequestBody final EditTellerRequest request) {
        UserResponse userResponse = userService.editUser(request, request.getId());
        return ResponseEntity.ok(userResponse);
    }

    // User activation - verification
    @GetMapping("/user/resendRegistrationToken")
    public GenericResponse resendRegistrationToken(final HttpServletRequest request, @RequestParam("token") final String existingToken,@RequestParam(required = false) Long agencyId) throws MessagingException {
        userService.resendRegistrationToken(request, existingToken, agencyId);
        return new GenericResponse("We will send an email with a new registration token to your email account");
    }

    // Reset password
    @PostMapping("/user/resetPassword")
    public GenericResponse resetPassword(final HttpServletRequest request, @RequestParam("email") final String userEmail,@RequestParam(required = false) Long agencyId) throws MessagingException {
        return userService.resetPassword(request, userEmail, agencyId);
    }

    // Save password
    @PostMapping("/user/savePassword")
    public ResponseEntity<Object> savePassword(final Locale locale, @Valid @RequestBody PasswordDto passwordDto) throws BusinessValidationException {
        log.info("Request: {}", passwordDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();

        return ResponseEntity.ok(userService.savePassword(locale, passwordDto));
    }

    @PostMapping("/user/change-password")
    public GenericResponse changePassword(@Valid @RequestBody ChangePasswordRequest request) {
        userService.changeUserPassword(request);
        return new GenericResponse("Password updated successfully");
    }

    @GetMapping("/users/roles/{role}")
    public ResponseEntity<List<UserResponse>> getAllUsersWithRole(@PathVariable("role") String role) {
        return ResponseEntity.ok(userService.findByRole(role));
    }

    //==============activation=============================================
    @PutMapping("/user/activate/{id}")
    public ResponseEntity<UserResponse> activate(@Valid @PathVariable("id") Long id) {
        return ResponseEntity.ok(userService.activate(id));
    }

    @PutMapping("/user/deactivate/{id}")
    public ResponseEntity<UserResponse> deactivate(@Valid @PathVariable("id") Long id) {

        return ResponseEntity.ok(userService.deactivate(id));
    }

    @PostMapping("/enable-user")
    public ResponseEntity<UserResponse> enableAdminUsers(@RequestBody EnableAdminUserRequest request) {
        return ResponseEntity.ok(userService.enableUser(request));
    }

    @GetMapping("/users/{page}/{size}")
    public ResponseEntity<Page<UserResponse>> findALl(@PathVariable("page") int page,
                                                      @PathVariable("size") int size) {
        return ResponseEntity.ok(userService.findAll(PageRequest.of(page, size)));
    }

    @GetMapping("/users")
    public ResponseEntity<List<UserResponse>> findALl() {
        return ResponseEntity.ok(userService.findAll());
    }

    @GetMapping("/users/admin/{page}/{size}")
    public ResponseEntity<Page<UserResponse>> findAllAdminUsers(@PathVariable("page") int page,
                                                                @PathVariable("size") int size,
                                                                @RequestParam(required = false) String search) {
        return ResponseEntity.ok(userService.findAllAdminUsers(PageRequest.of(page, size), search));
    }

    @GetMapping("/users/agency/{page}/{size}/{payeeId}")
    public ResponseEntity<List<UserResponse>> findAllAgentUsers(@PathVariable("page") int page,
                                                                @PathVariable("size") int size,
                                                                @PathVariable("payeeId") Long agentId) {
        return ResponseEntity.ok(userService.findAllAgentUsers(PageRequest.of(page, size), agentId));
    }

    //User for a specific client.

    @GetMapping("/users/client/{page}/{size}/{payerId}")
    public ResponseEntity<Page<UserResponse>> findAllClientUsers(@PathVariable("page") int page,
                                                                @PathVariable("size") int size,
                                                                @PathVariable("payerId") Long clientId) {
        return ResponseEntity.ok(userService.findAllClientUsers(PageRequest.of(page, size), clientId));
    }

    @GetMapping("/users/worker/{page}/{size}")
    public ResponseEntity<Page<UserResponse>> findAllClientUsers(@PathVariable("page") int page,
                                                                 @PathVariable("size") int size,
                                                                 @RequestParam(required = false) String search) {
        return ResponseEntity.ok(userService.findAllWorkerUsers(PageRequest.of(page, size), search));
    }


    @GetMapping("/user/roles")
    public ResponseEntity<List<Role>> getAllRoles() {
        return ResponseEntity.ok(userService.findAllRoles());
    }

    @GetMapping("/users/{id}")
    public ResponseEntity<UserResponse> getUserById(@PathVariable Long id) {
        return ResponseEntity.ok(userService.findById(id));
    }

    @GetMapping("/users/logged-in")
    public ResponseEntity<UserResponse> getLoggedUser() {
        return ResponseEntity.ok(userService.findByEmail(getAuthUsername()));
    }


    public String getAuthUsername() {

        Object principal = SecurityContextHolder
                .getContext()
                .getAuthentication()
                .getPrincipal();
        String username;
        if (principal instanceof UserDetails) {
            username = ((UserDetails) principal).getUsername();
        } else {
            username = principal.toString();
        }
        return username;
    }



    @GetMapping("/users-client/{payerId}")
    public UserResponse getUserByClientId(@PathVariable("payerId") Long clientId) {
        return userService.findByClientId(clientId);
    }

    @GetMapping("/users/username/{username}")
    public ResponseEntity<UserSearchResponse> getByUsername(@PathVariable String username) {
        return ResponseEntity.ok(userService.findByUsername(username));
    }

    @GetMapping("/users/activate-agency-users/{agentId}")
    public void activateAgentUsers(@PathVariable Long agentId, @RequestParam boolean active) {
        userService.activateAgentUsers(agentId, active);
    }

    @GetMapping("/users/activate-client-users/{payerId}")
    public void activateClientUsers(@PathVariable Long clientId, @RequestParam boolean active) {
        userService.activateClientUsers(clientId, active);
    }

    @GetMapping("/users/activate-worker-users/{workerId}")
    public void activateWorkerUsers(@PathVariable Long workerId, @RequestParam boolean active) {
        userService.activateWorkerUsers(workerId, active);
    }

    @PostMapping("/user/create-client-account")
    public ResponseEntity<UserResponse> registerClientAccount(@Valid @RequestBody final ClientAccountRequest request) {
        UserResponse userResponse = userService.registerClientAccount(request);
        return ResponseEntity.ok(userResponse);
    }


    @PostMapping("/user/feignregistration")
    public UserResponse registerUserAccountFeign(@Valid @RequestBody final UserDto accountDto,@RequestParam(required = false) Long agencyId ) throws MessagingException {
        return userService.registerNewUserFeignAccount(accountDto, agencyId);
    }



}