package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.model.AgencyEmailConfiguration;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for managing agency email configurations.
 */
public interface AgencyEmailConfigurationService {

    /**
     * Create or update email configuration for an agency
     * @param configuration the email configuration to save
     * @return the saved configuration
     */
    AgencyEmailConfiguration save(AgencyEmailConfiguration configuration);

    /**
     * Find email configuration by agency ID
     * @param agencyId the agency ID
     * @return Optional containing the email configuration if found
     */
    Optional<AgencyEmailConfiguration> findByAgencyId(Long agencyId);

    /**
     * Find active email configuration by agency ID
     * @param agencyId the agency ID
     * @return Optional containing the active email configuration if found
     */
    Optional<AgencyEmailConfiguration> findActiveByAgencyId(Long agencyId);

    /**
     * Find verified and active email configuration by agency ID
     * @param agencyId the agency ID
     * @return Optional containing the verified and active email configuration if found
     */
    Optional<AgencyEmailConfiguration> findVerifiedActiveByAgencyId(Long agencyId);

    /**
     * Get all email configurations
     * @return list of all email configurations
     */
    List<AgencyEmailConfiguration> findAll();

    /**
     * Find email configuration by ID
     * @param id the configuration ID
     * @return Optional containing the email configuration if found
     */
    Optional<AgencyEmailConfiguration> findById(Long id);

    /**
     * Delete email configuration by ID
     * @param id the configuration ID
     */
    void deleteById(Long id);

    /**
     * Delete email configuration by agency ID
     * @param agencyId the agency ID
     */
    void deleteByAgencyId(Long agencyId);

    /**
     * Check if an agency has an active email configuration
     * @param agencyId the agency ID
     * @return true if the agency has an active email configuration
     */
    boolean hasActiveConfiguration(Long agencyId);

    /**
     * Check if an agency has a verified and active email configuration
     * @param agencyId the agency ID
     * @return true if the agency has a verified and active email configuration
     */
    boolean hasVerifiedActiveConfiguration(Long agencyId);

    /**
     * Test email configuration by sending a test email
     * @param agencyId the agency ID
     * @param testEmailAddress the email address to send test email to
     * @return true if test was successful
     */
    boolean testConfiguration(Long agencyId, String testEmailAddress);

    /**
     * Activate email configuration for an agency
     * @param agencyId the agency ID
     * @return the updated configuration
     */
    AgencyEmailConfiguration activateConfiguration(Long agencyId);

    /**
     * Deactivate email configuration for an agency
     * @param agencyId the agency ID
     * @return the updated configuration
     */
    AgencyEmailConfiguration deactivateConfiguration(Long agencyId);

    /**
     * Mark configuration as verified after successful test
     * @param agencyId the agency ID
     * @param testResult the test result message
     * @return the updated configuration
     */
    AgencyEmailConfiguration markAsVerified(Long agencyId, String testResult);

    /**
     * Mark configuration as unverified
     * @param agencyId the agency ID
     * @param testResult the test result message
     * @return the updated configuration
     */
    AgencyEmailConfiguration markAsUnverified(Long agencyId, String testResult);

    /**
     * Check if email configuration exists for agency
     * @param agencyId the agency ID
     * @return true if configuration exists
     */
    boolean existsByAgencyId(Long agencyId);
}
