package com.cap10mycap10.ouathservice.service;


import com.cap10mycap10.ouathservice.entity.*;
import com.cap10mycap10.ouathservice.enums.UserType;
import com.maxmind.geoip2.exception.GeoIp2Exception;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import com.cap10mycap10.ouathservice.authclient.model.AuthClient;
import com.cap10mycap10.ouathservice.authclient.repository.AuthClientRepository;
import com.cap10mycap10.ouathservice.dao.UserRepository;
import com.cap10mycap10.ouathservice.dto.UserLogin;
import com.cap10mycap10.ouathservice.dto.UserLoginRequest;
import com.cap10mycap10.ouathservice.enums.ClientID;
import com.cap10mycap10.ouathservice.enums.Roles;
import com.cap10mycap10.ouathservice.exception.AccessDeniedException;
import com.cap10mycap10.ouathservice.exception.BusinessValidationException;
import com.cap10mycap10.ouathservice.exception.RecordNotFoundException;
import com.cap10mycap10.ouathservice.feignclient.OauthFeignClientService;
import com.cap10mycap10.ouathservice.feignclient.WorklinkServiceFeignClient;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;

 import static java.util.Objects.nonNull;

@Service
@Slf4j
public class LoginServiceImpl implements LoginService {
    @Value("${env.companyName}")
    private String companyName;
    private final OauthFeignClientService oauthFeignClientService;

    private final UserRepository userRepository;

    @Value( "${oauth-service.url}")
    private String  oauth_service_url;
    @Autowired
    private EmailService emailService;

    @Autowired
    private DeviceMetaDataService deviceMetaDataService;

    private final AuthClientRepository authClientRepository;

    @Autowired
    private WorklinkServiceFeignClient worklinkServiceFeignClient;

    public LoginServiceImpl(OauthFeignClientService oauthFeignClientService, UserRepository userRepository,
                            AuthClientRepository authClientRepository) {
        this.oauthFeignClientService = oauthFeignClientService;
        this.userRepository = userRepository;
        this.authClientRepository = authClientRepository;
    }

    @Override
    public ResultDTO userLogin(UserLogin userLogin,  HttpServletRequest req, Long agencyId) throws BusinessValidationException, IOException, GeoIp2Exception {
        User user = userRepository.findByUsername(userLogin.getUsername())
                .orElseThrow(() -> new BusinessValidationException("Username or password incorrect"));

        log.info("user status: {}", user.isEnabled());



        if (user.isEnabled()) {
            // Check if user belongs to an agency and if that agency is active
            if (user.getAgentId() != null && user.getUserType() != UserType.WORKER) {
                try {
                    WorklinkServiceFeignClient.AgencyStatusResponse agency = worklinkServiceFeignClient.getAgencyById(user.getAgentId());
                    if (agency != null && "INACTIVE".equalsIgnoreCase(agency.getStatus())) {
                        throw new BusinessValidationException("Your agency account has been deactivated. Please contact support for assistance.");
                    }
                } catch (Exception e) {
                    if (e instanceof BusinessValidationException) {
                        throw e;
                    }
                    log.warn("Could not verify agency status for user {}: {}", user.getUsername(), e.getMessage());
                    // Continue with login if agency service is unavailable
                }
            }

            ResultDTO resultDTO;
            if(user.getUserType()==UserType.WORKER){
                UserLoginRequest login = new UserLoginRequest();
                login.setUsername(userLogin.getUsername());
                login.setPassword(userLogin.getPassword());
                login.setClientId("appclient");
                login.setClientSecret("appclient@123");
                resultDTO = getToken(login);
            }else{
                resultDTO = getToken(userLogin);
            }
            resultDTO.setId(user.getId());
            resultDTO.setUserType(user.getUserType());
            resultDTO.setFirstname(user.getFirstName());
            resultDTO.setLastname(user.getLastName());
            resultDTO.setRoles(user.getRoles());
            resultDTO.setAgentId(user.getAgentId());
            resultDTO.setClientId(user.getClientId());
            resultDTO.setWorkerId(user.getWorkerId());


//            if( user.getLastPasswordChange()!=null &&  LocalDate.now().isBefore(user.getLastPasswordChange().plusDays(120))
//                    && !Objects.equals(user.getPassword(), user.getLastPassword())
//            ){
//
//            }else{
//                resultDTO.setAccessToken("PASSWORD_NEEDS_RESET");
//                resultDTO.setRefreshToken("PASSWORD_NEEDS_RESET");
//                return resultDTO;
//            }



//            This code is for sending an otp. It has been disbled to reduce complexity


//            if(user.getUserType() != UserType.WORKER && userLogin.getOTP()==null ){
//
//                String deviceDetails = deviceMetaDataService.getDeviceDetails(req.getHeader("user-agency"));
//
//                DeviceMetadata existingDevice
//                        = deviceMetaDataService.findExistingDevice(user.getId(), deviceDetails, "unknown");
//
//                if (Objects.isNull(existingDevice)) {
//                    String newOTP =(generateOTP(4));
//                    user.setOtp(newOTP);
//                    user.setOtpExpiry(LocalDateTime.now().plusMinutes(5));
//                    userRepository.save(user);
//
//                    final String message = "Your one time password is \n" + newOTP +
//                            "\nDo not share this one time password with anyone.";
//
//
//
//                    emailService.sendOtpEmail(user, newOTP);
//
//                    resultDTO.setAccessToken("OTP_SENT");
//                    resultDTO.setRefreshToken("OTP_SENT");
//                    return resultDTO;
//                }
//
//
//            }else if(user.getUserType() != UserType.WORKER){
//                if(!nonNull(user.getOtp()) || !userLogin.getOTP().equalsIgnoreCase(user.getOtp())){
//                    throw new BusinessValidationException("OTP incorrect");
//                }
//
//                if(LocalDateTime.now().isAfter(user.getOtpExpiry())){
//                    throw new BusinessValidationException("OTP expired");
//                }
//
//                user.setOtp(null);
//                userRepository.save(user);
//            }


            deviceMetaDataService.loginNotification( user, req, agencyId);

            log.info("User logged in:{}", user.getEmail());

            return resultDTO;
        } else {
            log.info("User {}", user.getFirstName());
            throw new BusinessValidationException("Your account is inactive. Please contact your admin for access.");
        }
    }


    static String generateOTP(int len) {
        String numbers = "**********";
        Random rndm_method = new Random();
        StringBuilder otp = new StringBuilder();
        for (int i = 0; i < len; i++) {
            otp.append(numbers.charAt(rndm_method.nextInt(numbers.length())));
        }
        return otp.toString();
    }



//    public ResultDTO refreshToken(String token) {
////        User user = userRepository.findByUsername(username)
////                .orElseThrow(() -> new RecordNotFoundException("User not found"));
//        ResultDTO resultDTO = refreshToken(token);
////        resultDTO.setFirstname(user.getFirstName());
////        resultDTO.setLastname(user.getLastName());
////        resultDTO.setRoles(user.getRoles());
//        return resultDTO;
//    }

    @Override
    public ResultDTO clientLogin(UserLoginRequest request) {

        AuthClient authClient = authClientRepository.findByClientId(request.getClientId())
                .orElseThrow(() -> new AccessDeniedException("Access denied"));

        User user = userRepository.findByUsername(request.getUsername())
                .orElseThrow(() -> new RecordNotFoundException("User not found"));

        // Check if user belongs to an agency and if that agency is active
        if (user.getAgentId() != null && user.getUserType() != UserType.WORKER) {
            try {
                WorklinkServiceFeignClient.AgencyStatusResponse agency = worklinkServiceFeignClient.getAgencyById(user.getAgentId());
                if (agency != null && "INACTIVE".equalsIgnoreCase(agency.getStatus())) {
                    throw new BusinessValidationException("Your agency account has been deactivated. Please contact support for assistance.");
                }
            } catch (Exception e) {
                if (e instanceof BusinessValidationException) {
                    throw e;
                }
                log.warn("Could not verify agency status for user {}: {}", user.getUsername(), e.getMessage());
                // Continue with login if agency service is unavailable
            }
        }

        if (authClient.getClientId().equalsIgnoreCase(ClientID.ADMIN.name())) {
            Optional<Role> optionalRole = user.getRoles()
                    .stream()
                    .filter(role -> role.getName().equalsIgnoreCase(Roles.ROLE_SYSTEM_ADMIN.name()) ||
                            role.getName().equalsIgnoreCase(Roles.ROLE_SYSTEM_CLERK.name()))
                    .findFirst();

            if (!optionalRole.isPresent()) {
                throw new AccessDeniedException("Access denied");
            }
        }
        if (authClient.getClientId().equalsIgnoreCase(ClientID.AGENT.name())) {
            Optional<Role> optionalRole = user.getRoles()
                    .stream()
                    .filter(role -> role.getName().equalsIgnoreCase(Roles.ROLE_AGENT_ADMIN.name()) ||
                            role.getName().equalsIgnoreCase(Roles.ROLE_AGENT_CLERK.name()))
                    .findFirst();

            if (!optionalRole.isPresent()) {
                throw new AccessDeniedException("Access denied");
            }
        }
        if (authClient.getClientId().equalsIgnoreCase(ClientID.CLIENT.name())) {
            Optional<Role> optionalRole = user.getRoles()
                    .stream()
                    .filter(role -> role.getName().equalsIgnoreCase(Roles.ROLE_CLIENT.name()))
                    .findFirst();

            if (!optionalRole.isPresent()) {
                throw new AccessDeniedException("Access denied");
            }
        }
        ResultDTO resultDTO = getToken(request);
        resultDTO.setId(user.getId());
        resultDTO.setLastname(user.getLastName());
        resultDTO.setRoles(user.getRoles());
        resultDTO.setFirstname(user.getFirstName());
        resultDTO.setAgentId(user.getAgentId());
        resultDTO.setClientId(user.getClientId());
        resultDTO.setWorkerId(user.getWorkerId());
        return resultDTO;
    }

    private ResultDTO getToken(UserLogin userLogin) {

        try {
            AccessTokenResponse accessTokenResponse;
            if (userLogin instanceof UserLoginRequest) {
                UserLoginRequest userLoginRequest = (UserLoginRequest) userLogin;
                accessTokenResponse = oauthFeignClientService.getAccessToken(userLogin.getUsername(),
                        userLogin.getPassword().trim(), userLoginRequest.getClientId(), userLoginRequest.getClientSecret());
            } else {
                accessTokenResponse = oauthFeignClientService.getAccessToken(userLogin.getUsername(),
                        userLogin.getPassword().trim());
            }
            ResultDTO resultDTO = new ResultDTO();
            resultDTO.setAccessToken(accessTokenResponse.getAccessToken());
            resultDTO.setRefreshToken(accessTokenResponse.getRefreshToken());
            resultDTO.setExpireIn(accessTokenResponse.getExpiresIn());
            resultDTO.setTokenType(accessTokenResponse.getTokenType());
            resultDTO.setScope(accessTokenResponse.getScope());

            return resultDTO;
        }catch (Exception ex){
            throw new BusinessValidationException("Username or password incorrect");
        }
    }

    @Override
    public ResultDTO refreshToken(String token) {
        final String url = oauth_service_url+"/oauth/token";
        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        body.add("grant_type", "refresh_token");
        body.add("refresh_token", token); // oauth client identifier

        String authStr = "ADMIN:appclient@123";
        String base64Creds = Base64.encodeBase64String(authStr.getBytes());

        // create headers
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", "Basic " + base64Creds);

        // create request
        HttpEntity request = new HttpEntity(body, headers);

        // make a request
        ResponseEntity<String> response = new RestTemplate().exchange(url, HttpMethod.POST, request, String.class);


        // get JSON response

        ResultDTO resultDTO = new ResultDTO();

        JsonObject jsonObject = new JsonParser().parse(Objects.requireNonNull(response.getBody())).getAsJsonObject();
        resultDTO.setAccessToken(replaceSlashes(String.valueOf(jsonObject.get("access_token"))));
        resultDTO.setExpireIn(replaceSlashes(String.valueOf(jsonObject.get("expires_in"))));
        resultDTO.setRefreshToken(replaceSlashes(String.valueOf(jsonObject.get("refresh_token"))));
        resultDTO.setTokenType(replaceSlashes(String.valueOf(jsonObject.get("token_type"))));
        resultDTO.setScope(replaceSlashes(String.valueOf(jsonObject.get("scope"))));

        return resultDTO;
    }


    private String replaceSlashes(String withSlashes) {
        return withSlashes.replaceAll("\"", "");
    }


}
