package com.cap10mycap10.worklinkservice.model;


import com.cap10mycap10.worklinkservice.enums.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import static java.util.Objects.nonNull;

@Entity
@NoArgsConstructor
@AllArgsConstructor
@Data
@Slf4j
public class Invoice extends AbstractAuditingEntity{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private Long workerId;
    @Enumerated(EnumType.STRING)
    private InvoiceType invoiceType;
    @Enumerated(EnumType.STRING)
    private PaymentGatewayType paymentGatewayType;
    private Long payeeId;
    private String notes;
    private String pollUrl;
    @Enumerated(EnumType.STRING)
    private SettlementStatus settlementStatus = SettlementStatus.PENDING;

    @Transient
    private String redirectUrl;
    @Transient
    private String clientSecret;

    private BigDecimal discount = BigDecimal.valueOf(0.00);

//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDate invoiceDate;

//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDate dueDate;

    @Enumerated(EnumType.STRING)
    private InvoiceStatus invoiceStatus = InvoiceStatus.UNPAID;

    @Getter
    private BigDecimal vatPercentage;


    private Boolean published;
    private BigDecimal serviceCharge;
    private String serviceChargeDesc;

    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "invoice", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<InvoiceItem> invoiceItems = new ArrayList<>();

    @ManyToOne(fetch = FetchType.LAZY)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    private VehicleBooking vehicleBooking;

    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "invoice", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private Set<Payment> payments = new HashSet<>();

    @ManyToOne(fetch = FetchType.LAZY)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @JoinColumn(nullable = false)
    private Agency agency;

    @ManyToOne(fetch = FetchType.LAZY)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    private Client client;

    public Invoice(Transport transport) {
        if(!nonNull(transport.getClient()))
            client = new Client(transport);
        else this.client = transport.getClient();
        client.getInvoices().add(this);
        this.invoiceDate = LocalDate.now();
        this.invoiceType = InvoiceType.CLIENT;
        this.agency = transport.getAgency();
        this.dueDate = LocalDate.now().plusDays(7);
        this.generateTransportInvoiceItems(transport);
//        this.setSubTotalAmount(this.invoiceItems.stream().map(InvoiceItem::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
//        this.setTotalAmount(this.invoiceItems.stream().map(InvoiceItem::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
    }


    public void addInvoiceItem(InvoiceItem invoiceItem) {
        invoiceItem.setInvoice(this);
        invoiceItems.add(invoiceItem);

    }

    public void removeInvoiceItem(InvoiceItem invoiceItem) {
        invoiceItems.remove(invoiceItem);
        invoiceItem.setInvoice(null);
    }


    public void addInvoiceItems(List<InvoiceItem> invoiceItems) {
        for (InvoiceItem item : invoiceItems
        ) {
            addInvoiceItem(item);
        }
    }

    public BigDecimal getTotalAmount() {
        BigDecimal total = getSubTotalAmount();
        if (nonNull(getVatAmount())) {
            total = total.add(getVatAmount());
        }
        if (nonNull(discount)) {
            total = total.subtract(discount);
        }
        return total.setScale(2, RoundingMode.HALF_EVEN);
    }

    public BigDecimal getTotalAmountPaid() {
          return payments.stream().map(Payment::getTotal)
                  .reduce(BigDecimal.ZERO, BigDecimal::add)
                  .setScale(10, RoundingMode.HALF_UP);
    }

    public BigDecimal getAmountDue() {
        return getTotalAmount().subtract(getTotalAmountPaid());
    }

    public BigDecimal getVatAmount() {
        // Calculate VAT amount from individual invoice items
        return invoiceItems.stream()
                .map(InvoiceItem::getTaxAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_EVEN);
    }

    public BigDecimal getSubTotalAmount() {
        return invoiceItems.stream()
                .map(InvoiceItem::getNetAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal getNetSubTotalAmount() {
        // Get the net amount (before tax) for all items
        return invoiceItems.stream()
                .map(InvoiceItem::getNetAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_EVEN);
    }

    private void setInvoiceStatus(InvoiceStatus invoiceStatus) {
        this.invoiceStatus = invoiceStatus;
    }

    public Invoice(VehicleBooking vehicleBooking){
        this.client = vehicleBooking.getClient();
        this.invoiceDate = LocalDate.now();
        this.vehicleBooking = vehicleBooking;
        this.invoiceType = InvoiceType.CLIENT;
        if(vehicleBooking.getByAgency())
            this.invoiceStatus = InvoiceStatus.UNPAID;
        else
            this.invoiceStatus = InvoiceStatus.QUOTATION;
        this.agency = vehicleBooking.getVehicle().getAgency();
        this.dueDate = vehicleBooking.getStart().toLocalDate();
        this.generateVehicleBookingInvoiceItems(vehicleBooking);

        if(nonNull(vehicleBooking.getPromotion())) {
            if (vehicleBooking.getPromotion().getPromotionType().equals(PromotionType.DISCOUNT) )
                discount = this.getSubTotalAmount().multiply(BigDecimal.valueOf(vehicleBooking.getPromotion().getDiscount() / 100));
        }
    }

    public void addBookingFee() {
        this.setServiceChargeDesc("Reservation fee");
        this.setServiceCharge(this.getSubTotalAmount().multiply(BigDecimal.valueOf(0.03)));
    }


    private void generateVehicleBookingInvoiceItems(VehicleBooking vehicleBooking){
        List<LocalDate> dates = vehicleBooking.getDatesBooked();



        dates.forEach(d->{
            InvoiceItem invoiceItem = new InvoiceItem(vehicleBooking, d);
            this.invoiceItems.add(invoiceItem);
        });

        if(nonNull(vehicleBooking.getPromotion()) && vehicleBooking.getPromotion().getPromotionType()==PromotionType.EXTRA_DAYS && (vehicleBooking.getPromotion().getExtraDays()<dates.size())) {
            InvoiceItem lastDay = this.invoiceItems.get(invoiceItems.size()-1);
            discount = lastDay.getTotal();
        }

        vehicleBooking.getVehicleAddons().forEach(d->{
            InvoiceItem invoiceItem = new InvoiceItem(dates.size(), d);
            this.invoiceItems.add(invoiceItem);
        });

        this.invoiceItems.forEach(i->i.setInvoice(this));
    }


    private void generateVehicleBookingInvoiceItems(Transport transport){
        List<LocalDate> dates = getDatesBetween(transport.getStart().toLocalDate(), transport.getEnd().toLocalDate());
        dates.forEach(d->{
            invoiceItems.add(new InvoiceItem(transport, d));
            invoiceItems.add(new InvoiceItem(transport));
        });
    }

    private void generateTransportInvoiceItems(Transport transport){

        //Bill worker hours
        invoiceItems.addAll(transport.getWorkerSpec().stream().flatMap(b->b.getBookings().stream()).flatMap(
                b->b.billClient().stream()
        ).collect(Collectors.toList()));

        //Bill vehicle hours and mileage
        generateVehicleBookingInvoiceItems(transport);

        this.invoiceItems.forEach(i->i.setInvoice(this));
    }

    public static List<LocalDate> getDatesBetween(LocalDate start, LocalDate end){
        List<LocalDate> dates = new ArrayList<>();
        while(!start.isAfter(end)){
            dates.add(start);
            start = start.plusDays(1);
        }
        return dates;
    }

    public void cancel() {
        setInvoiceStatus(InvoiceStatus.CANCELLED);
    }

    public void payInvoice(Payment payment) {
        payments.add(payment);
        payment.setInvoice(this);
        if(payment.getTotal().compareTo(getAmountDue())>=0){
            setInvoiceStatus(InvoiceStatus.PAID);
        }else setInvoiceStatus(InvoiceStatus.UNPAID);
    }



}

