package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.model.TaxRate;
import com.cap10mycap10.worklinkservice.repository.TaxRateRepository;
import com.cap10mycap10.worklinkservice.service.TaxRateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class TaxRateServiceImpl implements TaxRateService {

    private final TaxRateRepository taxRateRepository;

    @Override
    public TaxRate create(TaxRate taxRate) {
        log.info("Creating tax rate: {} for agency: {}", taxRate.getName(), taxRate.getAgencyId());

        // Validate tax rate data
        validateTaxRate(taxRate, null);

        return taxRateRepository.save(taxRate);
    }

    @Override
    public TaxRate update(TaxRate taxRate) {
        log.info("Updating tax rate: {} for agency: {}", taxRate.getName(), taxRate.getAgencyId());

        // Validate tax rate data
        validateTaxRate(taxRate, taxRate.getId());

        return taxRateRepository.save(taxRate);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TaxRate> findById(Long id) {
        return taxRateRepository.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TaxRate> findActiveByAgencyId(Long agencyId) {
        return taxRateRepository.findByAgencyIdAndActiveTrue(agencyId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TaxRate> findByAgencyId(Long agencyId) {
        return taxRateRepository.findByAgencyId(agencyId);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TaxRate> findByNameAndAgencyId(String name, Long agencyId) {
        return taxRateRepository.findByNameAndAgencyId(name, agencyId);
    }

    @Override
    public void deactivate(Long id) {
        taxRateRepository.findById(id).ifPresent(taxRate -> {
            taxRate.setActive(false);
            taxRateRepository.save(taxRate);
            log.info("Deactivated tax rate: {}", taxRate.getName());
        });
    }

    @Override
    public void activate(Long id) {
        taxRateRepository.findById(id).ifPresent(taxRate -> {
            taxRate.setActive(true);
            taxRateRepository.save(taxRate);
            log.info("Activated tax rate: {}", taxRate.getName());
        });
    }

    @Override
    public void delete(Long id) {
        log.info("Attempting to delete tax rate with ID: {}", id);

        // Check if tax rate exists
        TaxRate taxRate = taxRateRepository.findById(id)
                .orElseThrow(() -> new BusinessValidationException("Tax rate not found with ID: " + id));

        // Check if tax rate is in use
        if (isInUse(id)) {
            throw new BusinessValidationException(
                "Cannot delete tax rate '" + taxRate.getName() + "' as it is currently in use by vehicles, addons, or invoices. " +
                "Please deactivate it instead or remove all references before deletion."
            );
        }

        taxRateRepository.deleteById(id);
        log.info("Successfully deleted tax rate: {} (ID: {})", taxRate.getName(), id);
    }

    @Override
    public void createDefaultTaxRates(Long agencyId) {
        log.info("Creating default tax rates for agency: {}", agencyId);
        
        // Create standard VAT rate (15% - common in many countries)
        if (!taxRateRepository.existsByNameAndAgencyId("Standard VAT", agencyId)) {
            TaxRate standardVat = TaxRate.builder()
                    .name("Standard VAT")
                    .percentage(new BigDecimal("15.00"))
                    .description("Standard VAT rate")
                    .agencyId(agencyId)
                    .active(true)
                    .build();
            taxRateRepository.save(standardVat);
        }

        // Create zero rate
        if (!taxRateRepository.existsByNameAndAgencyId("Zero Rate", agencyId)) {
            TaxRate zeroRate = TaxRate.builder()
                    .name("Zero Rate")
                    .percentage(BigDecimal.ZERO)
                    .description("Zero tax rate for exempt items")
                    .agencyId(agencyId)
                    .active(true)
                    .build();
            taxRateRepository.save(zeroRate);
        }
    }

    @Override
    public boolean isInUse(Long id) {
        log.debug("Checking if tax rate with ID {} is in use", id);

        // Check if used by vehicles
        if (taxRateRepository.isUsedByVehicles(id)) {
            log.debug("Tax rate {} is used by vehicles", id);
            return true;
        }

        // Check if used by vehicle inventory (addons)
        if (taxRateRepository.isUsedByVehicleInventory(id)) {
            log.debug("Tax rate {} is used by vehicle inventory", id);
            return true;
        }

        // Check if used by invoice items
        if (taxRateRepository.isUsedByInvoiceItems(id)) {
            log.debug("Tax rate {} is used by invoice items", id);
            return true;
        }

        log.debug("Tax rate {} is not in use", id);
        return false;
    }

    @Override
    public void validateTaxRate(TaxRate taxRate, Long excludeId) {
        log.debug("Validating tax rate: {}", taxRate.getName());

        // Validate required fields
        if (!StringUtils.hasText(taxRate.getName())) {
            throw new BusinessValidationException("Tax rate name is required");
        }

        if (taxRate.getName().trim().length() < 2) {
            throw new BusinessValidationException("Tax rate name must be at least 2 characters long");
        }

        if (taxRate.getName().trim().length() > 50) {
            throw new BusinessValidationException("Tax rate name cannot exceed 50 characters");
        }

        if (taxRate.getPercentage() == null) {
            throw new BusinessValidationException("Tax percentage is required");
        }

        if (taxRate.getPercentage().compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessValidationException("Tax percentage cannot be negative");
        }

        if (taxRate.getPercentage().compareTo(new BigDecimal("100.00")) > 0) {
            throw new BusinessValidationException("Tax percentage cannot exceed 100%");
        }

        if (taxRate.getAgencyId() == null || taxRate.getAgencyId() <= 0) {
            throw new BusinessValidationException("Valid agency ID is required");
        }

        // Validate description length if provided
        if (StringUtils.hasText(taxRate.getDescription()) && taxRate.getDescription().length() > 255) {
            throw new BusinessValidationException("Tax rate description cannot exceed 255 characters");
        }

        // Check name uniqueness within agency
        if (!isNameUniqueForAgency(taxRate.getName().trim(), taxRate.getAgencyId(), excludeId)) {
            throw new BusinessValidationException(
                "A tax rate with the name '" + taxRate.getName().trim() + "' already exists for this agency"
            );
        }

        log.debug("Tax rate validation passed for: {}", taxRate.getName());
    }

    @Override
    public boolean isNameUniqueForAgency(String name, Long agencyId, Long excludeId) {
        log.debug("Checking name uniqueness: '{}' for agency: {}, excluding ID: {}", name, agencyId, excludeId);

        Optional<TaxRate> existingTaxRate = taxRateRepository.findByNameAndAgencyId(name.trim(), agencyId);

        if (existingTaxRate.isEmpty()) {
            return true; // Name is unique
        }

        // If we're updating an existing tax rate, exclude it from the uniqueness check
        if (excludeId != null && existingTaxRate.get().getId().equals(excludeId)) {
            return true; // Same tax rate being updated
        }

        return false; // Name is not unique
    }
}
