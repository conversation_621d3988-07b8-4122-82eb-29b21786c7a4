package com.cap10mycap10.worklinkservice.converter;

import com.cap10mycap10.worklinkservice.service.EncryptionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * JPA converter that automatically encrypts and decrypts string attributes.
 * This converter is used to transparently handle encryption/decryption of sensitive data
 * like passwords when storing to and retrieving from the database.
 */
@Converter
@Component
@Slf4j
public class EncryptedStringConverter implements AttributeConverter<String, String> {

    private EncryptionService encryptionService;

    /**
     * Default constructor for JPA.
     */
    public EncryptedStringConverter() {
        // Default constructor required by JPA
    }

    /**
     * Constructor with encryption service injection.
     * 
     * @param encryptionService the encryption service to use
     */
    @Autowired
    public EncryptedStringConverter(EncryptionService encryptionService) {
        this.encryptionService = encryptionService;
    }

    /**
     * Converts the entity attribute value to its database representation.
     * This method encrypts the plaintext value before storing it in the database.
     *
     * @param attribute the entity attribute value (plaintext)
     * @return the encrypted value to be stored in the database
     */
    @Override
    public String convertToDatabaseColumn(String attribute) {
        if (attribute == null || attribute.trim().isEmpty()) {
            return attribute;
        }

        try {
            // Check if already encrypted to avoid double encryption
            if (encryptionService != null && encryptionService.isEncrypted(attribute)) {
                log.debug("Value appears to already be encrypted, skipping encryption");
                return attribute;
            }

            if (encryptionService != null) {
                String encrypted = encryptionService.encrypt(attribute);
                log.debug("Successfully encrypted attribute for database storage");
                return encrypted;
            } else {
                log.warn("EncryptionService not available, storing value as plaintext");
                return attribute;
            }
        } catch (Exception e) {
            log.error("Failed to encrypt attribute, storing as plaintext", e);
            return attribute;
        }
    }

    /**
     * Converts the database value to its entity attribute representation.
     * This method decrypts the encrypted value retrieved from the database.
     *
     * @param dbData the encrypted value from the database
     * @return the decrypted plaintext value
     */
    @Override
    public String convertToEntityAttribute(String dbData) {
        if (dbData == null || dbData.trim().isEmpty()) {
            return dbData;
        }

        try {
            if (encryptionService != null) {
                // Check if the data is encrypted
                if (encryptionService.isEncrypted(dbData)) {
                    String decrypted = encryptionService.decrypt(dbData);
                    log.debug("Successfully decrypted attribute from database");
                    return decrypted;
                } else {
                    log.debug("Value appears to be plaintext, returning as-is");
                    return dbData;
                }
            } else {
                log.warn("EncryptionService not available, returning value as-is");
                return dbData;
            }
        } catch (Exception e) {
            log.error("Failed to decrypt attribute, returning encrypted value", e);
            return dbData;
        }
    }

    /**
     * Sets the encryption service. This method is used for dependency injection
     * when the converter is instantiated by JPA.
     *
     * @param encryptionService the encryption service to use
     */
    public void setEncryptionService(EncryptionService encryptionService) {
        this.encryptionService = encryptionService;
    }
}
