package com.cap10mycap10.worklinkservice.model;

import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * Entity representing email configuration for agencies.
 * This allows agencies to use their own SMTP servers for white-label email sending.
 */
@Entity
@Table(name = "agency_email_configuration")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
@ToString(exclude = {"smtpPassword"}) // Exclude password from toString for security
public class AgencyEmailConfiguration {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * Reference to the agency this configuration belongs to
     */
    @Column(name = "agency_id", nullable = false, unique = true)
    @NotNull
    private Long agencyId;

    /**
     * SMTP server host (e.g., smtp.gmail.com, smtp.hostinger.com)
     */
    @Column(name = "smtp_host", nullable = false)
    @NotBlank
    private String smtpHost;

    /**
     * SMTP server port (e.g., 587 for TLS, 465 for SSL, 25 for non-encrypted)
     */
    @Column(name = "smtp_port", nullable = false)
    @NotNull
    private Integer smtpPort;

    /**
     * SMTP username (usually the email address)
     */
    @Column(name = "smtp_username", nullable = false)
    @NotBlank
    private String smtpUsername;

    /**
     * SMTP password (encrypted in database)
     */
    @Column(name = "smtp_password", nullable = false)
    @NotBlank
    private String smtpPassword;

    /**
     * Whether SMTP authentication is required
     */
    @Column(name = "smtp_auth", nullable = false)
    @Builder.Default
    private Boolean smtpAuth = true;

    /**
     * Whether to use STARTTLS encryption
     */
    @Column(name = "smtp_starttls_enable", nullable = false)
    @Builder.Default
    private Boolean smtpStarttlsEnable = true;

    /**
     * Whether STARTTLS is required
     */
    @Column(name = "smtp_starttls_required", nullable = false)
    @Builder.Default
    private Boolean smtpStarttlsRequired = true;

    /**
     * Whether to use SSL encryption
     */
    @Column(name = "smtp_ssl_enable", nullable = false)
    @Builder.Default
    private Boolean smtpSslEnable = false;

    /**
     * SSL socket factory class (for SSL connections)
     */
    @Column(name = "smtp_ssl_socket_factory_class")
    private String smtpSslSocketFactoryClass;

    /**
     * From email address for outgoing emails
     */
    @Column(name = "from_email", nullable = false)
    @Email
    @NotBlank
    private String fromEmail;

    /**
     * From name/company name for outgoing emails
     */
    @Column(name = "from_name", nullable = false)
    @NotBlank
    private String fromName;

    /**
     * Reply-to email address
     */
    @Column(name = "reply_to_email")
    @Email
    private String replyToEmail;

    /**
     * Support email address for the agency
     */
    @Column(name = "support_email")
    @Email
    private String supportEmail;

    /**
     * Agency website URL
     */
    @Column(name = "website_url")
    private String websiteUrl;

    /**
     * Agency logo URL for email templates
     */
    @Column(name = "logo_url")
    private String logoUrl;

    /**
     * Whether this email configuration is active
     */
    @Column(name = "is_active", nullable = false)
    @Builder.Default
    private Boolean isActive = true;

    /**
     * Whether the configuration has been tested and verified
     */
    @Column(name = "is_verified", nullable = false)
    @Builder.Default
    private Boolean isVerified = false;

    /**
     * Last test date for the email configuration
     */
    @Column(name = "last_test_date")
    private LocalDateTime lastTestDate;

    /**
     * Last test result message
     */
    @Column(name = "last_test_result")
    private String lastTestResult;

    /**
     * Creation timestamp
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * Last update timestamp
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * User who created this configuration
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * User who last updated this configuration
     */
    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * Additional notes or comments about the configuration
     */
    @Column(name = "notes", length = 1000)
    private String notes;

    /**
     * Check if the configuration is ready to use
     */
    public boolean isReadyToUse() {
        return isActive && isVerified && 
               smtpHost != null && !smtpHost.trim().isEmpty() &&
               smtpPort != null && smtpPort > 0 &&
               smtpUsername != null && !smtpUsername.trim().isEmpty() &&
               smtpPassword != null && !smtpPassword.trim().isEmpty() &&
               fromEmail != null && !fromEmail.trim().isEmpty() &&
               fromName != null && !fromName.trim().isEmpty();
    }

    /**
     * Get the appropriate SSL socket factory class based on configuration
     */
    public String getEffectiveSocketFactoryClass() {
        if (smtpSslEnable && smtpSslSocketFactoryClass != null && !smtpSslSocketFactoryClass.trim().isEmpty()) {
            return smtpSslSocketFactoryClass;
        }
        if (smtpSslEnable) {
            return "javax.net.ssl.SSLSocketFactory";
        }
        return null;
    }

    /**
     * Validate the email configuration
     */
    public void validateConfiguration() throws IllegalArgumentException {
        if (agencyId == null) {
            throw new IllegalArgumentException("Agency ID is required");
        }

        if (smtpHost == null || smtpHost.trim().isEmpty()) {
            throw new IllegalArgumentException("SMTP host is required");
        }

        if (smtpPort == null || smtpPort <= 0 || smtpPort > 65535) {
            throw new IllegalArgumentException("SMTP port must be between 1 and 65535");
        }

        if (smtpUsername == null || smtpUsername.trim().isEmpty()) {
            throw new IllegalArgumentException("SMTP username is required");
        }

        if (smtpPassword == null || smtpPassword.trim().isEmpty()) {
            throw new IllegalArgumentException("SMTP password is required");
        }

        if (fromEmail == null || fromEmail.trim().isEmpty()) {
            throw new IllegalArgumentException("From email is required");
        }

        if (fromName == null || fromName.trim().isEmpty()) {
            throw new IllegalArgumentException("From name is required");
        }

        // Validate email format using simple regex
        String emailRegex = "^[A-Za-z0-9+_.-]+@([A-Za-z0-9.-]+\\.[A-Za-z]{2,})$";
        if (!fromEmail.matches(emailRegex)) {
            throw new IllegalArgumentException("From email format is invalid");
        }

        if (replyToEmail != null && !replyToEmail.trim().isEmpty() && !replyToEmail.matches(emailRegex)) {
            throw new IllegalArgumentException("Reply-to email format is invalid");
        }

        if (supportEmail != null && !supportEmail.trim().isEmpty() && !supportEmail.matches(emailRegex)) {
            throw new IllegalArgumentException("Support email format is invalid");
        }

        // Validate URL format if provided
        if (websiteUrl != null && !websiteUrl.trim().isEmpty()) {
            if (!websiteUrl.startsWith("http://") && !websiteUrl.startsWith("https://")) {
                throw new IllegalArgumentException("Website URL must start with http:// or https://");
            }
        }

        if (logoUrl != null && !logoUrl.trim().isEmpty()) {
            if (!logoUrl.startsWith("http://") && !logoUrl.startsWith("https://")) {
                throw new IllegalArgumentException("Logo URL must start with http:// or https://");
            }
        }
    }
}
