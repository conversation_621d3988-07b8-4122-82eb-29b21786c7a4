package com.cap10mycap10.ouathservice.service;

import com.cap10mycap10.ouathservice.dto.AgencyEmailConfigurationDto;
import com.cap10mycap10.ouathservice.dto.AgencyResultDto;
import com.cap10mycap10.ouathservice.entity.User;

/**
 * Service for handling agency-related operations in oauth-service
 */
public interface AgencyService {
    /**
     * Get agency email configuration for a user
     */
    AgencyEmailConfigurationDto getAgencyEmailConfiguration(Long agencyId);

}
