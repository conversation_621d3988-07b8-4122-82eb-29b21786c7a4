package com.cap10mycap10.userservice.service;


import com.cap10mycap10.userservice.dto.*;
import com.cap10mycap10.userservice.model.Role;
import com.cap10mycap10.userservice.model.User;
import com.cap10mycap10.userservice.model.VerificationToken;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;


import javax.mail.MessagingException;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Locale;
import java.util.Optional;

@Service
public interface UserService {

    UserResponse registerNewUserAccount(UserDto accountDto, HttpServletRequest request, Long agencyId) throws MessagingException;
    UserResponse addRoleToUser(AddRoleToUserDto accountDto, HttpServletRequest request) throws MessagingException;

    User getUser(String verificationToken);

    void createVerificationTokenForUser(User user, String token);

    VerificationToken generateNewVerificationToken(String token);

    void createPasswordResetTokenForUser(User user, String token);

    User findUserByEmail(String email);

    Optional<User> getUserByPasswordResetToken(String token);

    boolean checkIfValidOldPassword(User user, String password);

    Page<UserResponse> findAll(Pageable pageable, Long agentId);

    Page<UserResponse> findAll(Pageable pageable);

    List<UserResponse> findByRole(String role);

    List<UserResponse> findAll();

    UserResponse activate(Long id);

    UserResponse deactivate(Long id);

    UserResponse enableUser(EnableAdminUserRequest request);

    List<Role> findAllRoles();

    void resendRegistrationToken(HttpServletRequest request, String existingToken, Long agencyId) throws MessagingException;

    GenericResponse resetPassword(HttpServletRequest request, String userEmail, Long agencyId) throws MessagingException;

    GenericResponse savePassword(Locale locale, PasswordDto passwordDto);

    void changeUserPassword(ChangePasswordRequest passwordDto);

    UserResponse findById(Long id);

    UserResponse findByEmail(String email);

    Page<UserResponse> findAllAdminUsers(Pageable pageable, String search);

    UserSearchResponse findByUsername(String username);

    UserResponse registerClientAccount(ClientAccountRequest accountDto);

    List<UserResponse> findAllAgentUsers(PageRequest pageRequest, Long agentId);

    void activateAgentUsers(Long agentId, boolean active);

    UserResponse editUser(EditTellerRequest request, Long id);

    Page<UserResponse> findAllClientUsers(PageRequest of, Long clientId);

    void activateClientUsers(Long clientId, boolean active);

    void activateWorkerUsers(Long workerId, boolean active);

    Page<UserResponse> findAllWorkerUsers(PageRequest of, String search);

    UserResponse registerNewUserFeignAccount(UserDto accountDto, Long agencyId) throws MessagingException;

    UserResponse findByClientId(Long id);
}