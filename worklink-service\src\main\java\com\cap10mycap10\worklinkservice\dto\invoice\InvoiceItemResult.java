package com.cap10mycap10.worklinkservice.dto.invoice;

import lombok.Data;

import java.math.BigDecimal;


@Data
public class InvoiceItemResult {
    private Long id;

    private Long shiftId;
    private Long trainingId;

    private String dayOfTheWeek;

    private String startTime;

    private String endTime;

    private String startDate;

    private String endDate;
    private String assignmentCode;
    private String client;
    private Long clientId;

    private double numberOfHoursWorked;

    private BigDecimal rate;

    private BigDecimal total;
    private String directorate;
    private String worker;
    private String shiftType;
    private String description;

    // Tax-related fields
    private Boolean taxExempt;
    private BigDecimal taxRate;
    private BigDecimal taxAmount;
    private BigDecimal netAmount;
    private Boolean taxInclusive;

}
