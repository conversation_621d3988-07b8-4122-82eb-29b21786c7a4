package com.cap10mycap10.worklinkservice.api;



import com.cap10mycap10.worklinkservice.dto.agency.AgencyResultDto;
import com.cap10mycap10.worklinkservice.dto.client.*;
import com.cap10mycap10.worklinkservice.dto.shift.IShiftReportStatus;
import com.cap10mycap10.worklinkservice.dto.worker.WorkerResultDto;
import com.cap10mycap10.worklinkservice.feign.RegisterAgentAdminFeignClient;
import com.cap10mycap10.worklinkservice.feigndtos.feigndtos.request.UserDto;
import com.cap10mycap10.worklinkservice.feigndtos.feigndtos.response.UserResponse;
import com.cap10mycap10.worklinkservice.model.Agency;
import com.cap10mycap10.worklinkservice.model.Client;
import com.cap10mycap10.worklinkservice.search.ClientSearchService;
import com.cap10mycap10.worklinkservice.service.ClientService;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;

import javax.validation.Valid;
import java.net.URI;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static com.cap10mycap10.worklinkservice.config.AppConfiguration.isAdmin;

@Slf4j
@RestController
@Validated
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class ClientController {

    private final ClientService clientService;
    @Autowired
    private ClientSearchService clientSearchService;
    private final RegisterAgentAdminFeignClient registerAgentAdminFeignClient;

    public ClientController(ClientService clientService,   RegisterAgentAdminFeignClient registerAgentAdminFeignClient) {
        this.clientService = clientService;
        this.registerAgentAdminFeignClient = registerAgentAdminFeignClient;
    }



    @PostMapping(value = "client", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ClientDto> create( @Valid @RequestBody ClientDto clientDto) throws JsonProcessingException {
        log.info("Request to add client: {}", clientDto);
        return ResponseEntity.status(201).body(clientService.save(clientDto));
    }

    /*@ViewClient*/
    @GetMapping(value = "client/{id}")
    public ResponseEntity<ClientDto> findById(@PathVariable("id") Long id) {
        log.info("Request to get client with id : {}", id);
        return ResponseEntity.ok(clientService.findById(id));
    }

    @PostMapping("/client/docs")
    public ResponseEntity<Void> addClientDocs(@RequestBody Client clientDocsRequestDto) {
        log.info("Request to add documents to client: {}", clientDocsRequestDto.getId());
        clientService.addClientDocs(clientDocsRequestDto.getId(), clientDocsRequestDto.getClientDocs());
        return ResponseEntity.ok().build();
    }

    /*@ViewClientDashboard*/
    @GetMapping(value = "client-dashboard")
    public ResponseEntity<Integer> findNumberOfClients() {
        log.info("Request to get all clients ");
        return ResponseEntity.ok(clientService.findNumberOfClients());
    }

    /*@ViewClient*/
    @GetMapping(value = "clients/{page}/{size}")
    public ResponseEntity<Page<ClientDto>> findClients(
            @PathVariable("page") int page,
            @PathVariable("size") int size,
            @RequestParam(value = "searchQuery", required = false) String searchQuery,
            @RequestParam(value = "sort", required = false, defaultValue = "name") String sort,
            @RequestParam(value = "direction", required = false, defaultValue = "ASC") String direction,
            @RequestParam(value = "agencyId", required = false) Long agencyId
    ) {
        log.info("Request to get paged clients : {}. {} with search: {} sort: {} direction: {} agencyId: {}", page, size, searchQuery, sort, direction, agencyId);

        // Check if agencyId is required
        if (!isAdmin() && agencyId == null) {
            throw new BusinessValidationException("Provide all required parameters for the agency");
        }

        org.springframework.data.domain.Sort.Direction sortDirection =
                direction.equalsIgnoreCase("DESC") ?
                        org.springframework.data.domain.Sort.Direction.DESC :
                        org.springframework.data.domain.Sort.Direction.ASC;

        PageRequest pageRequest = PageRequest.of(page, size, sortDirection, sort);

        // Pass agencyId to service for filtering
        return ResponseEntity.ok(clientService.findAllPaged(searchQuery, pageRequest, agencyId));
    }



    /* @ViewClient*/
    @GetMapping(value = "client-agencies/{clientId}/{page}/{size}")
    public ResponseEntity<Page<AgencyResultDto>> findAllAgencies(@PathVariable("clientId") Long clientId,
                                                        @PathVariable("page") int page,
                                                        @PathVariable("size") int size) {
        log.info("Request to get client agencies with  : {}, {}, {}", clientId, page, size);
        return ResponseEntity.ok(clientService.findAllAgenciesPaged(clientId, PageRequest.of(page, size)));
    }

    /*@UpdateClient*/
    @PutMapping(value = "client", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ClientDto> update(@RequestBody ClientDto clientUpdateDto) {
        log.info("Request to update client with : {}", clientUpdateDto);
        clientService.update(clientUpdateDto);
        return ResponseEntity.ok().build();
    }


    @PutMapping(value = "client-link/{payerId}/{payeeId}")
    public ResponseEntity linkAgentToClient(@PathVariable("payeeId") Long agentId, @PathVariable("payerId") Long clientId) {

        clientService.linkAgentClient(agentId, clientId);
        return ResponseEntity.ok().build();
    }

    /* @UpdateAgency*/
    @PutMapping(value = "agency-client/{payerId}/{agencyId}")
    public ResponseEntity<AgencyResultDto> updateClient(@PathVariable("payerId") Long clientId,
                                                        @PathVariable("agencyId") Long agencyId) {
        log.info("Request to update client with agency clientid and agentid: {}, {}", clientId, agencyId);
        clientService.addAgency(clientId, agencyId);
        return ResponseEntity.ok().build();
    }

    /*@DeleteClient*/
    @DeleteMapping(value = "client/{id}")
    public ResponseEntity delete(@PathVariable("id") Long id) {
        log.info("Request to delete client with id : {}", id);
        clientService.deleteById(id);
        return ResponseEntity.noContent().build();
    }

    /*@ViewClient*/
    @PostMapping("client-search/{page}/{size}")
    public ResponseEntity<Page<ClientDto>> searchClient(
            @RequestParam(value = "searchCriteria", required = false) String searchCriteria,
            @PathVariable int page, @PathVariable int size) {

        String string = searchCriteria.replaceAll("\\s+","");
        if(string==""){
            return null;
        }
        return ResponseEntity.ok(clientSearchService.fuzzySearch(searchCriteria, page, size));

    }


    @GetMapping(value = "client/agency/search/{agencyId}/{searchCriteria}/{page}/{size}")
    public ResponseEntity<Page<ClientDto>> searchAgencyClients(@PathVariable("agencyId") Long agencyId,
                                                                     @PathVariable("page") int page,
                                                                     @PathVariable("searchCriteria") String searchCriteria,
                                                                     @PathVariable("size") int size) {
        log.info("Request to view agency workers: {}, {}, {}", agencyId, page, size);
        return ResponseEntity.ok(clientService.searchClientForAgency(agencyId,searchCriteria, PageRequest.of(page, size)));
    }

    /*@ViewClientDashboard*/
    @GetMapping("client-stats/{id}")
    public ResponseEntity<IShiftReportStatus> getClientStats(@PathVariable("id") Long id) {
        return ResponseEntity.ok(clientService.getStats(id));
    }

    /*@ViewClientDashboard*/
    @GetMapping("client-counters/{id}")
    public ResponseEntity<ClientStats> getClientAgencies(@PathVariable("id") Long id) {
        return ResponseEntity.ok(clientService.getMyStats(id));
    }


    @GetMapping("client-admin/{id}")
    public ResponseEntity<UserResponse> getClientAdmin(@PathVariable("id") Long id) {
        return ResponseEntity.ok(clientService.findAdminById(id));
    }



    @GetMapping("client-workers/{payerId}/{page}/{size}")
    public ResponseEntity<Page<WorkerResultDto>> getClientWorkers(@PathVariable("payerId") Long clientId,
                                                                  @PathVariable("page") int page,
                                                                  @PathVariable("size") int size) {
        log.info("Request to view client workers: {}, {}, {}", clientId, page, size);
        return ResponseEntity.ok(clientService.findAllWorkers(clientId, PageRequest.of(page, size)));
    }

    @PostMapping("/client/profile-image")
    public ResponseEntity<Object> uploadProfileImage(@RequestParam("file") MultipartFile file,
                                             @RequestParam("payerId") Long clientId
    ) {

        log.info("Request to add agency image");
        clientService.addProfilePic( clientId, file);
        return  ResponseEntity.noContent().build();
    }

}
