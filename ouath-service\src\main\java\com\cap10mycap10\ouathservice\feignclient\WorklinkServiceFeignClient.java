package com.cap10mycap10.ouathservice.feignclient;

import com.cap10mycap10.ouathservice.dto.AgencyEmailConfigurationDto;
import com.cap10mycap10.ouathservice.dto.AgencyResultDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(name = "${feign.client.worklink.name}", path = "/api/v1")
public interface WorklinkServiceFeignClient {

    @GetMapping("/agency/{id}")
    AgencyStatusResponse getAgencyById(@PathVariable("id") Long agencyId);

    /**
     * Get agency information by agent ID
     */
    @GetMapping("/agents/{agentId}/agency")
    AgencyResultDto getAgencyByAgentId(@PathVariable("agentId") Long agentId);

    /**
     * Get agency information by client ID
     */
    @GetMapping("/clients/{clientId}/agency")
    AgencyResultDto getAgencyByClientId(@PathVariable("clientId") Long clientId);

    /**
     * Get agency information by worker ID
     */
    @GetMapping("/workers/{workerId}/agency")
    AgencyResultDto getAgencyByWorkerId(@PathVariable("workerId") Long workerId);

    /**
     * Get agency email configuration
     */
    @GetMapping("/agency-email-config/agency/{agencyId}")
    AgencyEmailConfigurationDto getAgencyEmailConfiguration(@PathVariable("agencyId") Long agencyId);

    /**
     * Get agency email configuration status
     */
    @GetMapping("/agency-email-config/agency/{agencyId}/status")
    AgencyEmailStatusDto getAgencyEmailStatus(@PathVariable("agencyId") Long agencyId);
    
    // DTO for agency status response
    class AgencyStatusResponse {
        private Long id;
        private String name;
        private String status;
        
        public AgencyStatusResponse() {}
        
        public Long getId() {
            return id;
        }
        
        public void setId(Long id) {
            this.id = id;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getStatus() {
            return status;
        }
        
        public void setStatus(String status) {
            this.status = status;
        }
    }

    /**
     * DTO for agency email configuration status
     */
    class AgencyEmailStatusDto {
        private Long agencyId;
        private boolean hasConfiguration;
        private boolean hasActiveConfiguration;
        private boolean hasVerifiedConfiguration;
        private boolean isUsingCustomEmail;

        public AgencyEmailStatusDto() {}

        // Getters and setters
        public Long getAgencyId() { return agencyId; }
        public void setAgencyId(Long agencyId) { this.agencyId = agencyId; }

        public boolean isHasConfiguration() { return hasConfiguration; }
        public void setHasConfiguration(boolean hasConfiguration) { this.hasConfiguration = hasConfiguration; }

        public boolean isHasActiveConfiguration() { return hasActiveConfiguration; }
        public void setHasActiveConfiguration(boolean hasActiveConfiguration) { this.hasActiveConfiguration = hasActiveConfiguration; }

        public boolean isHasVerifiedConfiguration() { return hasVerifiedConfiguration; }
        public void setHasVerifiedConfiguration(boolean hasVerifiedConfiguration) { this.hasVerifiedConfiguration = hasVerifiedConfiguration; }

        public boolean isUsingCustomEmail() { return isUsingCustomEmail; }
        public void setUsingCustomEmail(boolean usingCustomEmail) { isUsingCustomEmail = usingCustomEmail; }
    }
}
