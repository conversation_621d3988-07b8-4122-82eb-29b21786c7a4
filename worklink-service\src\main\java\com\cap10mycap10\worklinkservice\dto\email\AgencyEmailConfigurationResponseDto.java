package com.cap10mycap10.worklinkservice.dto.email;

import com.cap10mycap10.worklinkservice.model.AgencyEmailConfiguration;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO for returning agency email configuration data via API.
 * This DTO includes the decrypted password for internal service communication
 * but should be used carefully to avoid exposing sensitive data in logs.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AgencyEmailConfigurationResponseDto {

    private Long id;
    private Long agencyId;
    private String smtpHost;
    private Integer smtpPort;
    private String smtpUsername;
    
    /**
     * The decrypted SMTP password - handle with care!
     * This field contains the actual password for SMTP authentication.
     */
    private String smtpPassword;
    
    private String fromEmail;
    private String fromName;
    private String replyToEmail;
    private Boolean smtpAuth;
    private Boolean smtpStarttlsEnable;
    private Boolean smtpStarttlsRequired;
    private Boolean smtpSslEnable;
    private String smtpSslSocketFactoryClass;
    private String supportEmail;
    private String websiteUrl;
    private String logoUrl;
    private Boolean isActive;
    private Boolean isVerified;
    private LocalDateTime lastTestDate;
    private String lastTestResult;
    private String notes;

    /**
     * Create a response DTO from an AgencyEmailConfiguration entity.
     * The password will be automatically decrypted by the JPA converter.
     *
     * @param config the email configuration entity
     * @return the response DTO with decrypted password
     */
    public static AgencyEmailConfigurationResponseDto fromEntity(AgencyEmailConfiguration config) {
        if (config == null) {
            return null;
        }

        return AgencyEmailConfigurationResponseDto.builder()
                .id(config.getId())
                .agencyId(config.getAgencyId())
                .smtpHost(config.getSmtpHost())
                .smtpPort(config.getSmtpPort())
                .smtpUsername(config.getSmtpUsername())
                .smtpPassword(config.getDecryptedSmtpPassword()) // Get decrypted password
                .fromEmail(config.getFromEmail())
                .fromName(config.getFromName())
                .replyToEmail(config.getReplyToEmail())
                .smtpAuth(config.getSmtpAuth())
                .smtpStarttlsEnable(config.getSmtpStarttlsEnable())
                .smtpStarttlsRequired(config.getSmtpStarttlsRequired())
                .smtpSslEnable(config.getSmtpSslEnable())
                .smtpSslSocketFactoryClass(config.getSmtpSslSocketFactoryClass())
                .supportEmail(config.getSupportEmail())
                .websiteUrl(config.getWebsiteUrl())
                .logoUrl(config.getLogoUrl())
                .isActive(config.getIsActive())
                .isVerified(config.getIsVerified())
                .lastTestDate(config.getLastTestDate())
                .lastTestResult(config.getLastTestResult())
                .notes(config.getNotes())
                .build();
    }

    /**
     * Override toString to avoid logging the password.
     * This is a security measure to prevent passwords from appearing in logs.
     */
    @Override
    public String toString() {
        return "AgencyEmailConfigurationResponseDto{" +
                "id=" + id +
                ", agencyId=" + agencyId +
                ", smtpHost='" + smtpHost + '\'' +
                ", smtpPort=" + smtpPort +
                ", smtpUsername='" + smtpUsername + '\'' +
                ", smtpPassword='[PROTECTED]'" +
                ", fromEmail='" + fromEmail + '\'' +
                ", fromName='" + fromName + '\'' +
                ", replyToEmail='" + replyToEmail + '\'' +
                ", smtpAuth=" + smtpAuth +
                ", smtpStarttlsEnable=" + smtpStarttlsEnable +
                ", smtpStarttlsRequired=" + smtpStarttlsRequired +
                ", smtpSslEnable=" + smtpSslEnable +
                ", smtpSslSocketFactoryClass='" + smtpSslSocketFactoryClass + '\'' +
                ", supportEmail='" + supportEmail + '\'' +
                ", websiteUrl='" + websiteUrl + '\'' +
                ", logoUrl='" + logoUrl + '\'' +
                ", isActive=" + isActive +
                ", isVerified=" + isVerified +
                ", lastTestDate=" + lastTestDate +
                ", lastTestResult='" + lastTestResult + '\'' +
                ", notes='" + notes + '\'' +
                '}';
    }
}
