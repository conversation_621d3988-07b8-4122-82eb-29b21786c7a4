package com.cap10mycap10.worklinkservice.dto.asset.admin;

import com.cap10mycap10.worklinkservice.dto.promocode.PromotionDto;
import com.cap10mycap10.worklinkservice.enums.*;
import com.cap10mycap10.worklinkservice.model.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Page;

import javax.persistence.Column;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Objects.nonNull;

@Data
@NoArgsConstructor
public class VehicleDto {

    private Long id;
    private String name;
    private String notes;
    private String regno;
    @Column(nullable = false)
    private String description;
//    private float stock;
    private AssetStatus status ;
    private Set<Location> locations = new HashSet<>();
    private Set<Long> locationIds = new HashSet<>();
    Set<PromotionDto> promotions = new HashSet<>();
    private VehicleType type = VehicleType.DEFAULT;
    private Agency agency;
    private String model;
    private String color;
    private String mainPhoto;
    private Float depositAmt;
    private Boolean airConditioning;
    private String engineNumber;
    private String engineSize;
    private String trackerId;
    private String contactPerson;
    private String contactPhone;
    private String contactAddress;
    private Set<VehicleDocument> documents;
    private Set<VehicleInventoryDto> inventory;
    private Set<VehiclePhoto> photos;
    private Float mileage;
    private Set<VehicleInventoryDto> vehicleAddons;
    private Integer doors;
    private Integer minHireDays;
    private Integer seats;
    private Integer maxAge;
    private Integer minAge;
    private Float hourlyRate;
    private Float mileageRate;
    private long jobCount;
    private Float maxDailyMileage;
    private Float excessMileageRate;
    private Boolean forRental = false;
    Set<VehicleRate> vehicleRates;
    private FuelType fuelType;
    private TransmissionType transmissionType;
    private Float capacity;

    private List<Rating> ratings = new ArrayList<>();
    private Float rating;
    Set<VehicleAvailability> vehicleAvailabilities = new HashSet<>();
    public Vehicle toAsset() {
        return new Vehicle(this);
    }

    /**
     * Get the primary location for this vehicle.
     * Returns the first location if any exist, null otherwise.
     */
    public Location getLocation() {
        if (locations == null || locations.isEmpty()) {
            return null;
        }
        return locations.iterator().next();
    }



    public Set<VehiclePhoto> getPhotos() {
        if (photos == null || mainPhoto == null) {
            return photos;
        }

        return photos.stream()
                .sorted((photo1, photo2) -> (nonNull(photo1.getUrl())&&photo1.getUrl().equals(mainPhoto)) ? -1 : 1)
                .collect(Collectors.toCollection(LinkedHashSet::new));
    }
}
