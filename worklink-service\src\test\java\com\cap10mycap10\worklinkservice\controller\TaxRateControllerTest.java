package com.cap10mycap10.worklinkservice.controller;

import com.cap10mycap10.worklinkservice.dto.tax.TaxRateDto;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.model.TaxRate;
import com.cap10mycap10.worklinkservice.service.TaxRateService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(TaxRateController.class)
class TaxRateControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private TaxRateService taxRateService;

    @Autowired
    private ObjectMapper objectMapper;

    private TaxRate taxRate;
    private TaxRateDto taxRateDto;
    private Long agencyId = 1L;
    private Long taxRateId = 1L;

    @BeforeEach
    void setUp() {
        taxRate = new TaxRate();
        taxRate.setId(taxRateId);
        taxRate.setName("Standard VAT");
        taxRate.setPercentage(new BigDecimal("15.00"));
        taxRate.setDescription("Standard VAT rate");
        taxRate.setActive(true);
        taxRate.setAgencyId(agencyId);

        taxRateDto = new TaxRateDto();
        taxRateDto.setName("Standard VAT");
        taxRateDto.setPercentage(new BigDecimal("15.00"));
        taxRateDto.setDescription("Standard VAT rate");
        taxRateDto.setActive(true);
        taxRateDto.setAgencyId(agencyId);
    }

    @Test
    void testGetTaxRatesByAgency_Success() throws Exception {
        // Given
        List<TaxRate> taxRates = Arrays.asList(taxRate);
        when(taxRateService.findByAgencyId(agencyId)).thenReturn(taxRates);

        // When & Then
        mockMvc.perform(get("/api/tax-rates/agency/{agencyId}", agencyId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].name").value("Standard VAT"))
                .andExpect(jsonPath("$[0].percentage").value(15.00))
                .andExpect(jsonPath("$[0].active").value(true));

        verify(taxRateService).findByAgencyId(agencyId);
    }

    @Test
    void testGetActiveTaxRatesByAgency_Success() throws Exception {
        // Given
        List<TaxRate> activeTaxRates = Arrays.asList(taxRate);
        when(taxRateService.findActiveByAgencyId(agencyId)).thenReturn(activeTaxRates);

        // When & Then
        mockMvc.perform(get("/api/tax-rates/agency/{agencyId}/active", agencyId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].name").value("Standard VAT"))
                .andExpect(jsonPath("$[0].active").value(true));

        verify(taxRateService).findActiveByAgencyId(agencyId);
    }

    @Test
    void testGetTaxRateById_Success() throws Exception {
        // Given
        when(taxRateService.findById(taxRateId)).thenReturn(Optional.of(taxRate));

        // When & Then
        mockMvc.perform(get("/api/tax-rates/{id}", taxRateId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("Standard VAT"))
                .andExpect(jsonPath("$.percentage").value(15.00));

        verify(taxRateService).findById(taxRateId);
    }

    @Test
    void testGetTaxRateById_NotFound() throws Exception {
        // Given
        when(taxRateService.findById(taxRateId)).thenReturn(Optional.empty());

        // When & Then
        mockMvc.perform(get("/api/tax-rates/{id}", taxRateId))
                .andExpect(status().isNotFound());

        verify(taxRateService).findById(taxRateId);
    }

    @Test
    void testCreateTaxRate_Success() throws Exception {
        // Given
        when(taxRateService.create(any(TaxRate.class))).thenReturn(taxRate);

        // When & Then
        mockMvc.perform(post("/api/tax-rates")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(taxRateDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.name").value("Standard VAT"))
                .andExpect(jsonPath("$.percentage").value(15.00));

        verify(taxRateService).create(any(TaxRate.class));
    }

    @Test
    void testCreateTaxRate_ValidationError() throws Exception {
        // Given
        when(taxRateService.create(any(TaxRate.class)))
                .thenThrow(new BusinessValidationException("Tax rate name is required"));

        // When & Then
        mockMvc.perform(post("/api/tax-rates")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(taxRateDto)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value("Tax rate name is required"));

        verify(taxRateService).create(any(TaxRate.class));
    }

    @Test
    void testCreateTaxRate_InvalidInput() throws Exception {
        // Given
        taxRateDto.setName(""); // Invalid name
        taxRateDto.setPercentage(new BigDecimal("-5.00")); // Invalid percentage

        // When & Then
        mockMvc.perform(post("/api/tax-rates")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(taxRateDto)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testUpdateTaxRate_Success() throws Exception {
        // Given
        when(taxRateService.findById(taxRateId)).thenReturn(Optional.of(taxRate));
        when(taxRateService.update(any(TaxRate.class))).thenReturn(taxRate);

        // When & Then
        mockMvc.perform(put("/api/tax-rates/{id}", taxRateId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(taxRateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("Standard VAT"))
                .andExpect(jsonPath("$.percentage").value(15.00));

        verify(taxRateService).findById(taxRateId);
        verify(taxRateService).update(any(TaxRate.class));
    }

    @Test
    void testUpdateTaxRate_NotFound() throws Exception {
        // Given
        when(taxRateService.findById(taxRateId)).thenReturn(Optional.empty());

        // When & Then
        mockMvc.perform(put("/api/tax-rates/{id}", taxRateId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(taxRateDto)))
                .andExpect(status().isNotFound());

        verify(taxRateService).findById(taxRateId);
        verify(taxRateService, never()).update(any(TaxRate.class));
    }

    @Test
    void testUpdateTaxRate_ValidationError() throws Exception {
        // Given
        when(taxRateService.findById(taxRateId)).thenReturn(Optional.of(taxRate));
        when(taxRateService.update(any(TaxRate.class)))
                .thenThrow(new BusinessValidationException("A tax rate with this name already exists"));

        // When & Then
        mockMvc.perform(put("/api/tax-rates/{id}", taxRateId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(taxRateDto)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value("A tax rate with this name already exists"));

        verify(taxRateService).findById(taxRateId);
        verify(taxRateService).update(any(TaxRate.class));
    }

    @Test
    void testActivateTaxRate_Success() throws Exception {
        // Given
        doNothing().when(taxRateService).activate(taxRateId);

        // When & Then
        mockMvc.perform(patch("/api/tax-rates/{id}/activate", taxRateId))
                .andExpect(status().isOk());

        verify(taxRateService).activate(taxRateId);
    }

    @Test
    void testDeactivateTaxRate_Success() throws Exception {
        // Given
        doNothing().when(taxRateService).deactivate(taxRateId);

        // When & Then
        mockMvc.perform(patch("/api/tax-rates/{id}/deactivate", taxRateId))
                .andExpect(status().isOk());

        verify(taxRateService).deactivate(taxRateId);
    }

    @Test
    void testDeleteTaxRate_Success() throws Exception {
        // Given
        doNothing().when(taxRateService).delete(taxRateId);

        // When & Then
        mockMvc.perform(delete("/api/tax-rates/{id}", taxRateId))
                .andExpect(status().isOk());

        verify(taxRateService).delete(taxRateId);
    }

    @Test
    void testDeleteTaxRate_InUse() throws Exception {
        // Given
        doThrow(new BusinessValidationException("Cannot delete tax rate as it is currently in use"))
                .when(taxRateService).delete(taxRateId);

        // When & Then
        mockMvc.perform(delete("/api/tax-rates/{id}", taxRateId))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.message").value("Cannot delete tax rate as it is currently in use"));

        verify(taxRateService).delete(taxRateId);
    }

    @Test
    void testCreateTaxRate_MissingRequiredFields() throws Exception {
        // Given
        TaxRateDto invalidDto = new TaxRateDto();
        // Missing required fields

        // When & Then
        mockMvc.perform(post("/api/tax-rates")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidDto)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testCreateTaxRate_InvalidPercentageRange() throws Exception {
        // Given
        taxRateDto.setPercentage(new BigDecimal("150.00")); // Invalid percentage > 100

        // When & Then
        mockMvc.perform(post("/api/tax-rates")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(taxRateDto)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testGetTaxRatesByAgency_EmptyList() throws Exception {
        // Given
        when(taxRateService.findByAgencyId(agencyId)).thenReturn(Arrays.asList());

        // When & Then
        mockMvc.perform(get("/api/tax-rates/agency/{agencyId}", agencyId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isEmpty());

        verify(taxRateService).findByAgencyId(agencyId);
    }

    @Test
    void testCreateTaxRate_DuplicateName() throws Exception {
        // Given
        when(taxRateService.create(any(TaxRate.class)))
                .thenThrow(new BusinessValidationException("A tax rate with the name 'Standard VAT' already exists for this agency"));

        // When & Then
        mockMvc.perform(post("/api/tax-rates")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(taxRateDto)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value("A tax rate with the name 'Standard VAT' already exists for this agency"));

        verify(taxRateService).create(any(TaxRate.class));
    }
}
