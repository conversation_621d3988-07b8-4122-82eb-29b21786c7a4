package com.cap10mycap10.ouathservice.service;

import com.cap10mycap10.ouathservice.dto.AgencyEmailConfigurationDto;
import com.cap10mycap10.ouathservice.dto.AgencyResultDto;
import com.cap10mycap10.ouathservice.entity.User;
import com.cap10mycap10.ouathservice.feignclient.WorklinkServiceFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Implementation of AgencyService for oauth-service
 */
@Service
@Slf4j
public class AgencyServiceImpl implements AgencyService {

    @Autowired
    private WorklinkServiceFeignClient worklinkServiceFeignClient;


    @Override
    public AgencyEmailConfigurationDto getAgencyEmailConfiguration( Long agencyId) {
        try {
             if (agencyId == null) {
                log.debug("No agency ID found for agencyId {}", agencyId);
                return null;
            }

            log.debug("Getting email configuration for agency ID: {}", agencyId);
            AgencyEmailConfigurationDto config = worklinkServiceFeignClient.getAgencyEmailConfiguration(agencyId);
            
            // Only return configuration if it's ready to use
            if (config != null && config.isReadyToUse()) {
                return config;
            } else {
                log.debug("Agency {} email configuration is not ready to use", agencyId);
                return null;
            }
        } catch (Exception e) {
            log.warn("Failed to get agency email configuration for agencyId {}: {}", agencyId, e.getMessage());
            return null;
        }
    }
}
